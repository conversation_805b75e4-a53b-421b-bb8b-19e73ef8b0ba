/*
Includes: 
* #5745
* #5754
* #5755
* #5721
*/
PROMPT Creating package body script for package AUTH
CREATE OR REPLACE
PACKAGE BODY MCAUTH.AUTH AS

  /* Core authentication package */

  clientIdVar constant VARCHAR2(9) 	:= '_CLIENTID';
  ipVar constant VARCHAR2(10) 		:= 'IP_ADDRESS';
  deviceIdVar constant VARCHAR2(9) 	:= 'DEVICE_ID';
  appVersionIdVar constant VARCHAR2(22) := 'APP_VERSION_IDENTIFIER';
  osVersionIdVar constant VARCHAR2(21) 	:= 'OS_VERSION_IDENTIFIER';
  eventVar constant VARCHAR2(5) 	:= 'EVENT';
  extAuthIdVar constant VARCHAR2(8) := '_EXTAUTH';
  cHostVar CONSTANT VARCHAR2(4) := 'HOST';
  cSessLogLevelVar CONSTANT VARCHAR2(8) := 'LOGLEVEL';
  dfltLang constant VARCHAR2(100) 	:= sspkg.readVchar(pkgCtxId || '/dfltLang');
  dynSQL VARCHAR2(8000);
  TYPE reservedWordsType IS TABLE OF VARCHAR2(16);
  reservedWords constant reservedWordsType := reservedWordsType(clientIdVar);
  deviceAuthorizationStatus VARCHAR2(30) := NULL;
  loginStatusCode VARCHAR2(500);
  loginStatusMessage VARCHAR2(1024);
  loginStatusMessageMaxLength CONSTANT PLS_INTEGER := 1024;

  fauth_maxAttempts constant INTEGER := sspkg.readInt(pkgCtxId || '/FailedAuth.maxAttempts');
  fauth_lockTime constant INTEGER := sspkg.readInt(pkgCtxId || '/FailedAuth.lockTime');
  fauth_maxLoginAttemptsNotif constant INTEGER := sspkg.readInt('/Customization/failedAuthNotification/maxLoginAttempts');


  gsmSCID CLIENT.ID%TYPE;
  gsmSPhone CLIENT.GSM%TYPE;
  gsmApplication VARCHAR2(40);
  gsmAccOwner VARCHAR2(40);

  vDisplayLastLogins constant PLS_INTEGER := NVL(sspkg.ReadInt(pkgCtxId || '/displayLastLogins'), 10);

  vmELBAActKeyDuration PLS_INTEGER;
  vmTokenActKeyDuration PLS_INTEGER;

  vPasswordMinLength CONSTANT PLS_INTEGER := NVL(sspkg.readInt(pkgCtxId || '/minimumPasswordLength'), 8);
  vPasswordMaxLength CONSTANT PLS_INTEGER := NVL(sspkg.readInt(pkgCtxId || '/maximumPasswordLength'), 40);

  chk_pwdcontusername 	 BOOLEAN := sspkg.readBool(pkgCtxId || '/Check_PasswordContainsUsername');
  chk_pwdcontrevusername BOOLEAN := sspkg.readBool(pkgCtxId || '/Check_PasswordContainsRevUsername');

  vPasswordMaxRetentionNo CONSTANT PLS_INTEGER := NVL(sspkg.readInt(pkgCtxId || '/PasswordMaxRetentionNo'), 0);

  vPasswordExpireInDays CONSTANT PLS_INTEGER := NVL(sspkg.readInt(pkgCtxId || '/PasswordExpireInDays'), 0);
  vPasswordExpirationWarning CONSTANT PLS_INTEGER := NVL(sspkg.readInt(pkgCtxId || '/PasswordExpirationWarning'), 7);
  vChangePwdAfterExpired CONSTANT BOOLEAN := sspkg.readBool(pkgCtxId || '/AllowPasswordChangeAfterExpiration');

  digitarray CONSTANT VARCHAR2(80) := NVL(sspkg.readVchar(pkgCtxId || '/digitarray'), '0123456789');
  lowchararray CONSTANT VARCHAR2(80) := NVL(sspkg.readVchar(pkgCtxId || '/lowchararray'), 'abcdefghijklmnopqrstuvwxyz');
  upchararray CONSTANT VARCHAR2(80) := NVL(sspkg.readVchar(pkgCtxId || '/upchararray'), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ');
  punctarray CONSTANT VARCHAR2(80) := NVL(sspkg.readVchar(pkgCtxId || '/punctarray'), '!"#$%&()``*+,-/:;<=>?_');

  cAuthKeyDurationMADMAP CONSTANT VARCHAR2(80) := pkgCtxId || '/AuthenticationKeyDurationMADMAP';
  cmTokenAuthKeyDurationMADMAP CONSTANT VARCHAR2(80) := pkgCtxId || '/mTokenAuthenticationKeyDurationMADMAP';
  cAuthKeyDurationThin CONSTANT VARCHAR2(80) := pkgCtxId || '/AuthenticationKeyDurationThinclient';

  cActivationCodeMsg CONSTANT VARCHAR2(45) := '/Core/Auth/Plugin/mOTP/msg/YourActivationCode';
  cSendUsernameMsg CONSTANT VARCHAR2(34) := '/Core/Admin/Forms/SendUsernameBody';

  cMTokenActKey CONSTANT VARCHAR2(14) := 'MTOKEN_ACT_KEY';
  cMTokenActKeyValidUntil CONSTANT VARCHAR2(26) := 'MTOKEN_ACT_KEY_VALID_UNTIL';
  cMTokenUserId CONSTANT VARCHAR2(14) := 'MTOKEN_USER_ID';

  cmELBAActKey CONSTANT VARCHAR2(13) := 'MELBA_ACT_KEY';
  cmELBAActKeyValidUntil CONSTANT VARCHAR2(25) := 'MELBA_ACT_KEY_VALID_UNTIL';

  cmElbaActKeySegmentDelimiter CONSTANT VARCHAR2(38) := pkgCtxId || '/mElbaActKeySegmentDelimiter';
  cmTokenActKeySegmentDelimiter CONSTANT VARCHAR2(39) := pkgCtxId || '/mTokenActKeySegmentDelimiter';

  satisfyMin CONSTANT PLS_INTEGER := sspkg.readInt(pkgCtxId || '/minPassComplexityLevel');

	cERR_NO_SUCH_USER   CONSTANT VARCHAR2(25) := pkgCtxId || '/err/NoSuchUser';
	cERR_InvalidChallengeResponse   CONSTANT VARCHAR2(80) := pkgCtxId || '/err/InvalidChallengeResponse';
	cERR_InvalidOTP                 CONSTANT VARCHAR2(80) := pkgCtxId || '/err/InvalidOTP';
	cERR_ExpectOTP                 CONSTANT VARCHAR2(80) := pkgCtxId || '/err/ExpectOTP';
	cERR_WrongOTPType                 CONSTANT VARCHAR2(80) := pkgCtxId || '/err/WrongOTPType';
	cERR_InvalidSignature           CONSTANT VARCHAR2(45) := pkgCtxId || '/err/InvalidSignature';
	cERR_UnsuportedSignatureMethod  CONSTANT VARCHAR2(54) := pkgCtxId || '/err/UnsuportedSignatureMethod';

	cERR_InternalError CONSTANT VARCHAR2(80) := pkgCtxId || '/err/InternalError';
	cERR_WrongCredentials CONSTANT VARCHAR2(80) := pkgCtxId || '/err/WrongCredentials';
	cERR_LockedAccount CONSTANT VARCHAR2(80) := pkgCtxId || '/err/LockedAccount';
	cERR_ExtAuthProblem CONSTANT VARCHAR2(80) := pkgCtxId || '/err/ExtAuthProblem';
	cERR_DisabledExtAuth CONSTANT VARCHAR2(80) := pkgCtxId || '/err/DisabledExtAuth';

	cERR_UnauthorizedDevice CONSTANT VARCHAR2(80) := pkgCtxId || '/err/UnauthorizedDevice';
	cERR_NotPermited CONSTANT VARCHAR2(80) := pkgCtxId || '/err/NotPermited';
	cERR_NullCredentials CONSTANT VARCHAR2(80) := pkgCtxId || '/err/NullCredentials';
	cERR_DisabledAccount CONSTANT VARCHAR2(80) := pkgCtxId || '/err/DisabledAccount';
	cERR_ExprdPwdChangeAllowed CONSTANT VARCHAR2(80) := pkgCtxId || '/err/ExpiredPasswordChangeAllowed';
	cERR_PasswordExpireInDays CONSTANT VARCHAR2(80) := pkgCtxId || '/err/PasswordExpireInDays';

	cERR_UnableToDisablePassword CONSTANT VARCHAR2(80) := pkgCtxId || '/err/UnableToDisablePassword';
	cERR_PermLockedAccount CONSTANT VARCHAR2(80) := pkgCtxId || '/err/PermLockedAccount';
	cERR_ExpiredAccount CONSTANT VARCHAR2(80) := pkgCtxId || '/err/ExpiredAccount';
	cERR_MissingSecKey CONSTANT VARCHAR2(80) := pkgCtxId || '/err/MissingSecKey';
	cERR_ExpiredPassword CONSTANT VARCHAR2(80) := pkgCtxId || '/err/ExpiredPassword';
	cERR_RqrPasswordAuthentication CONSTANT VARCHAR2(80) := pkgCtxId || '/err/RequirePasswordAuthentication';
	cERR_ApplicationLicenseMissing CONSTANT VARCHAR2(80) := pkgCtxId || '/err/ApplicationLicenseMissing';
	cERR_NoCertificate CONSTANT VARCHAR2(80) := pkgCtxId || '/err/NoCertificate';
	cERR_WrongCertificate CONSTANT VARCHAR2(80) := pkgCtxId || '/err/WrongCertificate';

	cERR_ShortPassword CONSTANT VARCHAR2(80) := pkgCtxId || '/err/ShortPassword';
	cERR_passwordTooLong CONSTANT VARCHAR2(80) := pkgCtxId || '/err/passwordTooLong';
	cERR_PasswordContainsUsername CONSTANT VARCHAR2(80) := pkgCtxId || '/err/PasswordContainsUsername';
	cERR_PwdCntnsUnameInReverese CONSTANT VARCHAR2(80) := pkgCtxId || '/err/PasswordContainsUsernameInReverese';
	cERR_PasswordWithoutDigit CONSTANT VARCHAR2(80) := pkgCtxId || '/err/PasswordWithoutDigit';
	cERR_PasswordWithoutLowChar CONSTANT VARCHAR2(80) := pkgCtxId || '/err/PasswordWithoutLowChar';
	cERR_PasswordWithoutUpChar CONSTANT VARCHAR2(80) := pkgCtxId || '/err/PasswordWithoutUpChar';
	cERR_PasswordWithoutPunctMark CONSTANT VARCHAR2(80) := pkgCtxId || '/err/PasswordWithoutPunctMark';
	cERR_PasswordInvalidCharacters CONSTANT VARCHAR2(80) := pkgCtxId || '/err/PasswordInvalidCharacters';
	cERR_PasswordReuseNotAllowed CONSTANT VARCHAR2(80) := pkgCtxId || '/err/PasswordReuseNotAllowed';
	cERR_pwdComplexityRuleHeader CONSTANT VARCHAR2(80) := pkgCtxId || '/err/pwdComplexityRuleHeader';


	cERR_NoSession CONSTANT VARCHAR2(80) := pkgCtxId || '/err/NoSession';

	cERR_SuccessfullPromotion CONSTANT VARCHAR2(80) := pkgCtxId || '/err/SuccessfullPromotion';
	cERR_SuccessfullRegistration CONSTANT VARCHAR2(80) := pkgCtxId || '/err/SuccessfullRegistration';
	cERR_MltplExtAuthRegistrations CONSTANT VARCHAR2(80) := pkgCtxId || '/err/MultipleExtAuthRegistrations';
	cERR_InvalidActivationKey CONSTANT VARCHAR2(80) := pkgCtxId || '/err/InvalidActivationKey';
	cERR_ActivationKeyExpired CONSTANT VARCHAR2(80) := pkgCtxId || '/err/ActivationKeyExpired';
	cERR_InvalidDevice CONSTANT VARCHAR2(80) := pkgCtxId || '/err/InvalidDevice';
	cERR_UnknownDeviceType CONSTANT VARCHAR2(80) := pkgCtxId || '/err/UnknownDeviceType';
	cERR_ValidActKeyAlreadyExists CONSTANT VARCHAR2(80) := pkgCtxId || '/err/ValidActKeyAlreadyExists';
	cERR_NoLicense CONSTANT VARCHAR2(80) := pkgCtxId || '/err/NoLicense';
	cERR_UnableToFindPrimaryAccOwn CONSTANT VARCHAR2(80) := pkgCtxId || '/err/UnableToFindPrimaryAccOwner';

	cERR_invalidAccOwner CONSTANT VARCHAR2(80) := pkgCtxId || '/err/invalidAccOwner';

	cERR_notLongerValid CONSTANT VARCHAR2(80) := '/Core/SessMgmt/err/notLongerValid';



	cDeviceNotPasswordAuthorized CONSTANT VARCHAR2(1) := '0';
	cDevicePasswordAuthorized CONSTANT VARCHAR2(1) := '1';
	cDevicePasswordAuthorizedPHX CONSTANT VARCHAR2(3) := 'PH5';

	vCaseSensitivePwd BOOLEAN:= sspkg.readBool(pkgCtxId || '/caseSensitivePwd');

	cMobileDeviceWithPwdHashed CONSTANT VARCHAR2(1) := '1';
	cMobileDeviceWithOutPwdHashed CONSTANT VARCHAR2(1) := '0';



  TYPE vClientType IS RECORD (
    rowrec rowid,
	id NUMBER(38),
	enabled NUMBER(1),
	password VARCHAR2(40),
	password_enabled NUMBER(1),
	valid_from DATE,
	valid_to DATE,
	vAuthKey VARCHAR2(400 CHAR),
	vAuthKeyValidUntil DATE,
	vUserId VARCHAR2(400 CHAR),
	password_valid_from VARCHAR2(400 CHAR),
	password_change_required NUMBER(1),
	password_cs VARCHAR2(40));

	FUNCTION bool2char (pValue BOOLEAN)
	RETURN VARCHAR2 IS
	BEGIN
		IF pValue THEN RETURN 'TRUE';
		ELSE RETURN 'FALSE';
		END IF;
	END bool2char;

	PROCEDURE checkLicense(pApplicationId VARCHAR2 := getApplicationId(), pClientId client.id%TYPE := getClientId());

	PROCEDURE RegisterSplunkNotification(pActionId VARCHAR2, pClientId client.id%TYPE, pIpAddress clientlog.ipAddress%TYPE, pApplicationId clientlog.application_id%TYPE);

	FUNCTION getExtAuthAttributeValue (pClientId IN client.id%TYPE, pDeviceId app_extauth.dev_id%TYPE, pApplicationId app_extauth.application_id%TYPE, pAttribute IN VARCHAR2)
	RETURN VARCHAR2;

	PROCEDURE writeCustomLog(pUnit VARCHAR2, pLogText VARCHAR2)
	IS
	BEGIN
		NULL;
	END writeCustomLog;

	FUNCTION getSUser RETURN VARCHAR2 as
	begin
		RETURN mcsm.read('USERID');
	END getSUser;

	FUNCTION getSApp RETURN VARCHAR2 as
	begin
		RETURN mcsm.getApplicationId;
	END getSApp;

	FUNCTION isServiceRegistered(pClientId IN app_extauth.client_id%TYPE, pApplicationId app_extauth.application_id%TYPE)
	RETURN BOOLEAN
	IS
		myUnit CONSTANT VARCHAR2(30) := 'isServiceRegistered';
		vPom PLS_INTEGER;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId);

		SELECT NULL INTO vPom FROM DUAL
		WHERE EXISTS (SELECT NULL
		FROM app_extauth WHERE client_id = pClientId AND application_id = pApplicationId);

		RETURN TRUE;

    EXCEPTION
        WHEN NO_DATA_FOUND THEN
        RETURN FALSE;
	END;

	FUNCTION getClientEmail(pClientId client.id%TYPE) RETURN VARCHAR2
	IS
		vEmailAddress VARCHAR2(40);
		myUnit VARCHAR2(30) := 'getClientEmail';
	BEGIN
		SELECT NVL(eu.contact_email, c.email) INTO vEmailAddress FROM client c, mcore.end_users eu
		WHERE c.id = pClientId AND eu.id = pClientId;

		RETURN vEmailAddress;
	EXCEPTION
		WHEN NO_DATA_FOUND THEN
			slog.error(pkgCtxId, myUnit, common_pck.cERR_UnknownEndUser || ':' || pClientId);
			RETURN NULL;
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
			RETURN NULL;
	END;


/* Return count of incorrect logins/otp checks/chresp checks/signature checks for client and
type of operation (CHECKCHRESP, CHECKOTP, GSMLOGIN, LOGIN, CHECKSIGN) */
FUNCTION countWrongCredentials(vclientId INTEGER, vlogtypeId VARCHAR2, lastDate out DATE)
RETURN INTEGER as
  cnt INTEGER;
begin
  -- lastDate is NOT usable, kept for compatibility with previous versions
  SELECT count(*), max(logdate)
  INTO cnt, lastDate
  FROM clientlog cl
  WHERE logstatus='ERROR' and
    logtype_id = vlogtypeId and
    status_code in (cERR_WrongCredentials, cERR_LockedAccount) and
    client_id = vclientId and
    logdate > (SELECT max(relevant_date) FROM
      (SELECT NVL(max(logdate), sysdate-100000) relevant_date
       FROM clientlog
      WHERE logtype_id = vlogtypeId and description not in ('Login attempt') and client_id = vclientId and
      (logstatus='OK' or (logstatus='ERROR' and status_code = cERR_LockedAccount))));

  RETURN cnt;
END countWrongCredentials;

/* Count successive failures and lock account IF needed */
PROCEDURE checkAndLock(vclientId INTEGER, vlogtypeId VARCHAR2) as

 wcCnt INTEGER;
 lastDate DATE;
 myunit CONSTANT VARCHAR2(30) := 'checkAndLock';

 PRAGMA AUTONOMOUS_TRANSACTION;		-- da se bolje vidi u kodu!
begin
  wcCnt := countWrongCredentials(vclientId, vlogtypeid, lastDate);
  slog.debug(pkgCtxId, myUnit, vclientId ||':'||vlogtypeId||':'||fauth_maxAttempts||':'||fauth_Locktime||':'||wcCnt);
  IF wcCnt >= NVL(fauth_maxAttempts, 0) THEN
    slog.debug(pkgCtxId, myUnit, 'Violated :' || fauth_maxAttempts ||':'||wcCnt);
    IF vlogtypeId = 'GSMLOGIN' THEN
		slog.debug(pkgCtxId, myUnit, 'Locking GSM login');
      update client set
             gsmlocked_since = sysdate,
             gsmlocked_until = sysdate + fauth_Locktime/(24*60*60)
             WHERE id = vclientId;
      commit;
    elsif vlogtypeid in ('CHECKCHRESP', 'CHECKOTP', 'LOGIN', 'CHECKPWD') THEN
		slog.debug(pkgCtxId, myUnit, 'Locking user ' || vclientId);
		update client set
              usernamelocked_since = sysdate,
              usernamelocked_until = sysdate + fauth_Locktime/(24*60*60)
              WHERE id = vclientId;
      commit;
    ELSE
      rollback;
    END IF;
  END IF;
  rollback;
END checkAndLock;


 PROCEDURE checkAndSendNotification(vclientId client.id%TYPE)

    IS

        wcCnt INTEGER;
        lastDate DATE;
        v_toAddr VARCHAR2(40 CHAR);
        v_fromAddr  VARCHAR2(100);
        v_subject VARCHAR2(100);
        v_body VARCHAR2(4000);
        vLang VARCHAR2(10 CHAR):='bs';
        myunit CONSTANT VARCHAR2(30) := 'checkAndSendNotification';
        vchannelId VARCHAR2(100);
        v_enabled_functionality BOOLEAN;


    BEGIN
		vchannelId := sspkg.readvchar('/Core/Main/TranPays/Notifications/MsgChannelId');
        v_enabled_functionality := sspkg.readbool('/Customization/failedAuthNotification/enabledNotification');

        IF NOT v_enabled_functionality THEN
          slog.debug(pkgCtxId, myUnit, 'Sending mail notification for failed login not enabled for this bank!');
          RETURN;
        END IF;


        wcCnt := countWrongCredentials(vclientId, common_pck.cLogin, lastDate);
        slog.debug(pkgCtxId, myUnit, vclientId ||':'||common_pck.cLogin||':'|| lastDate||':'||wcCnt);

            IF wcCnt = NVL(fauth_maxLoginAttemptsNotif, 1) THEN

                slog.debug(pkgCtxId, myUnit, 'Number of current failed logins'||':'|| wcCnt);

				v_toAddr := getClientEmail(vclientId);

                v_subject := mlang.trans(vLang,'/Notifications/failedAuthNotification/mailSubject');
                slog.debug(pkgCtxId, myUnit, 'Prepare subject');
                v_body := mlang.trans('bs','/Notifications/failedAuthNotification/mailBody',  TO_CHAR(lastDate,common_pck.cDATE_MASK), TO_CHAR(lastDate,common_pck.cTIME_MASK));
                slog.debug(pkgCtxId, myUnit, 'Prepare body');
                v_fromAddr:= sspkg.readvchar('/Notifications/failedAuthNotification/fromAddr');
  		BEGIN
                            mcore.send_mail(
                                fromAddr => v_fromAddr,
                                toAddr => v_toAddr,
                                subject => v_subject,
                                bodyMsg => v_body,
                                MC_ID => vchannelId,
                                sendAfter => NULL,
                                sendBefore => NULL
                                );
                                slog.debug(pkgCtxId, myUnit, 'Notification sent');

                        EXCEPTION
                            WHEN OTHERS THEN
                            slog.error(pkgCtxId, myUnit, 'Unable to send email notification for failed login!');
                            slog.error(pkgCtxId, myUnit, SQLERRM);
                END;


            END IF;
        COMMIT;

        EXCEPTION
            WHEN OTHERS THEN
                slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
            ROLLBACK;


    END checkAndSendNotification;







/* Return TRUE IF username is locked, and dates WHEN lock is issued and WHEN will expire */
FUNCTION isUsernameLocked(vusername VARCHAR2, sinceDate out DATE, untilDate out DATE) RETURN BOOLEAN as
		myunit CONSTANT VARCHAR2(30) := 'isUsernameLocked';

	BEGIN
		slog.debug(pkgCtxId, myUnit, vusername);

	for i in (SELECT usernamelocked_since, usernamelocked_until FROM client WHERE lower(username) = lower(vusername)) LOOP
		sinceDate := i.usernameLocked_since;
		untilDate := i.usernameLocked_until;
		IF sysdate between NVL(i.usernameLocked_since, sysdate-1) and NVL(i.usernameLocked_until, sysdate+1) THEN
			IF (i.usernameLocked_since is NULL and i.usernameLocked_until is NULL) THEN
				slog.debug(pkgCtxId, myUnit, vusername || ':FALSE-1');
			  RETURN FALSE;
			ELSE
				slog.debug(pkgCtxId, myUnit, vusername || ':TRUE:' || untilDate);
			  RETURN TRUE;
			END IF;
		ELSE
			slog.debug(pkgCtxId, myUnit, vusername || ':FALSE-2');
		  RETURN FALSE;
		END IF;
	END LOOP;
		slog.debug(pkgCtxId, myUnit, vusername || ':FALSE-3');
  RETURN FALSE;
END;

/* Return TRUE IF username is locked */
FUNCTION isUsernameLocked(vusername VARCHAR2) RETURN BOOLEAN as
sinceDate DATE;
untilDate DATE;
begin
  RETURN isUsernameLocked(vusername, sinceDate, untilDate);
END;

/* Return TRUE IF gsm is locked, and dates WHEN lock is issued and WHEN will expire */
FUNCTION isGsmLocked(vgsm VARCHAR2, sinceDate out DATE, untilDate out DATE) RETURN BOOLEAN as
begin
  for i in (SELECT gsmlocked_since, gsmlocked_until FROM client WHERE gsm=vgsm) LOOP
    sinceDate := i.gsmLocked_since;
    untilDate := i.gsmLocked_until;
    IF sysdate between NVL(i.gsmLocked_since, sysdate-1) and NVL(i.gsmLocked_until, sysdate+1) THEN
        RETURN NOT(i.gsmLocked_since is NULL and i.gsmLocked_until is NULL);
    ELSE
      RETURN FALSE;
    END IF;
  END LOOP;
  RETURN FALSE;
END;

/* Return TRUE IF gsm is locked, and dates WHEN lock is issued and WHEN will expire */
	FUNCTION isGsmLocked(vgsm VARCHAR2) RETURN BOOLEAN as
	sinceDate DATE;
	untilDate DATE;
	BEGIN
		RETURN isGsmLocked(vgsm, sinceDate, untilDate);
	END;


  /* set Session var 4 client ID */
  PROCEDURE setSCID(clientId client.id%TYPE) as
  begin
      mcsm.write(clientIdVar, clientId);
  END setSCID;

  /* get Session var 4 client ID */
  FUNCTION getSCID RETURN VARCHAR2 as
  begin
    RETURN mcsm.read(clientIdVar);
  END getSCID;

  /* get IP address used during logong */
  FUNCTION getIPAddress RETURN VARCHAR2 as
  BEGIN
    RETURN mcsm.read(ipVar);
  END getIPAddress;

  FUNCTION getHostname RETURN VARCHAR2 as
  BEGIN
	RETURN mcsm.read(cHostVar);
  END getHostname;

  FUNCTION getSLogLevel RETURN VARCHAR2 as
  BEGIN
	RETURN mcsm.read(cSessLogLevelVar);
  END getSLogLevel;


  /* returns client identifier, regardless of application user*/
  FUNCTION getClientId RETURN VARCHAR2 is
  begin
    RETURN NVL(getSCID, getGsmSCID);
  END getClientId;

  /* returns client application identifier*/
  FUNCTION getApplicationId RETURN VARCHAR2 is
  begin
    RETURN NVL(getSApp, getGsmApplication);
  END getApplicationId;

  PROCEDURE setDeviceId(pDeviceId app_extauth.dev_id%TYPE) as
  begin
    mcsm.write(deviceIdVar, pDeviceId);
  END setDeviceId;

  /* returns client device identifier*/
  FUNCTION getDeviceId RETURN VARCHAR2 as
  begin
    RETURN mcsm.getDeviceId;
  END getDeviceId;

  PROCEDURE setAppVersionId(pAppVersionId VARCHAR2)
  IS
  BEGIN
     mcsm.write(appVersionIdVar, pAppVersionId);
  END setAppVersionId;

  FUNCTION getAppVersionId RETURN VARCHAR2 as
  begin
     RETURN mcsm.read(appVersionIdVar);
  END getAppVersionId;

  PROCEDURE setOSVersionId(pOSVersionId VARCHAR2)
  IS
  BEGIN
     mcsm.write(osVersionIdVar, pOSVersionId);
  END setOSVersionId;

  FUNCTION getOSVersionId RETURN VARCHAR2 as
  begin
     RETURN mcsm.read(osVersionIdVar);
  END getOSVersionId;

  PROCEDURE setEvent(pEvent VARCHAR2) as
  begin
    mcsm.write(eventVar, pEvent);
  END setEvent;

  /* returns event identifier*/
  FUNCTION getEvent RETURN VARCHAR2 as
  begin
    RETURN mcsm.read(eventVar);
  END getEvent;

  PROCEDURE setAppExtAuthId(pExtAuthId VARCHAR2) as
  begin
    mcsm.write(extAuthIdVar, pExtAuthId);
  END setAppExtAuthId;

  /* returns client ExtAuth identifier*/
  FUNCTION getAppExtAuthId RETURN VARCHAR2 as
  begin
    RETURN mcsm.read(extAuthIdVar);
  END getAppExtAuthId;

  FUNCTION getCID4User(vusername client.username%TYPE, raiseNotFound BOOLEAN := TRUE) RETURN client.id%TYPE AS
    myunit CONSTANT VARCHAR2(30) := 'getCID4User';
  begin
   slog.debug(pkgCtxId, myUnit, vusername);
   FOR i in (SELECT id FROM client WHERE LOWER(username)=LOWER(vusername)) LOOP
    slog.debug(pkgCtxId, myUnit, 'Found client');
    RETURN i.id;
   END LOOP;
   IF raiseNotFound THEN
    slog.error(pkgCtxId, myUnit, cERR_NO_SUCH_USER || ':' || vusername);
    sspkg.raiseError(cERR_NO_SUCH_USER, 'User "'||vusername||'" does NOT exist', pkgCtxId, myunit);
   ELSE
    RETURN NULL;
   END IF;
  END getCID4User;

  FUNCTION getCID4Gsm(vgsm client.username%TYPE, raiseNotFound BOOLEAN := TRUE) RETURN client.id%type as
    myunit CONSTANT VARCHAR2(30) := 'getCID4Gsm';
  begin
   slog.debug(pkgCtxId, myUnit, vgsm);
   for i in (SELECT id FROM client WHERE gsm=vgsm) LOOP
    RETURN i.id;
   END LOOP;
   IF raiseNotFound THEN
    sspkg.raiseError(cERR_NO_SUCH_USER, 'Gsm phone "'||vgsm||'" does NOT exist', pkgCtxId, myunit);
   ELSE
    RETURN NULL;
   END IF;
  END;

  FUNCTION getGsmForCID(vUserId client.id%TYPE)
  RETURN client.gsm%TYPE IS
    vGsm client.gsm%TYPE;
    myunit CONSTANT VARCHAR2(30) := 'getGsmForCID';
  BEGIN
	slog.debug(pkgCtxId, myUnit, vUserId);
    SELECT gsm
      INTO vGsm
    FROM client
    WHERE id = vUserId;
    RETURN vGsm;
  EXCEPTION
    WHEN NO_DATA_FOUND THEN
        sspkg.raiseError(cERR_NO_SUCH_USER, 'User "'||vUserId||'" does NOT exist', pkgCtxId, myunit);
  END;

  /*  */
  FUNCTION getDeviceAuthorizationStatus RETURN VARCHAR2 as
  begin
    RETURN deviceAuthorizationStatus;
  END;

  /* Return last login status code. NULL is fine, otherwise there is error code */
  FUNCTION getLoginStatusCode RETURN VARCHAR2 as
  begin
    RETURN loginStatusCode;
  END;

  /* Return last status message */
  FUNCTION getLoginStatusDetails RETURN VARCHAR2 as
  begin
    RETURN loginStatusMessage;
  END;

  PROCEDURE writeClientLog(
    logOpeType logoperation_type.id%type,
    message logoperation_type.description%type,
    vlogstatus VARCHAR2 := 'OK',
    vclientId CLIENT.ID%TYPE,
    vipaddress VARCHAR2 := getIPAddress(),
    vhost VARCHAR2 := getHostname(),
    vgsm VARCHAR2 := NULL,
    vstatus_code VARCHAR2 := NULL,
    vstatus_message VARCHAR2 := NULL,
	vApplicationId VARCHAR2 := getApplicationId(),
	vSessionId VARCHAR2 := mcsm.getSessionId(),
	vDeviceId VARCHAR2 := getDeviceid(),
	vAppVersionId VARCHAR2 := getAppVersionId(),
	vOSVersionId VARCHAR2 := getOSVersionId()
	) as

  PRAGMA AUTONOMOUS_TRANSACTION;
    myunit CONSTANT VARCHAR2(30) := 'writeClientLog';
    pstatus_message clientLog.status_message%TYPE;
  BEGIN
    slog.debug(pkgCtxId, myUnit);

    IF logOpeType = 'SETACCOWNER' THEN
      pstatus_message := vstatus_message;
    ELSE
      pstatus_message := SUBSTR(vstatus_message || ' - APP. ID - ' || vApplicationId, 1, 4000);
    END IF;

    IF vclientId is NOT NULL THEN
      INSERT INTO clientLog(id, logtype_id, description, logdate, client_id, hostname, ipaddress, logstatus, gsm, status_code, status_message, application_id, session_identifier, device_id, app_version_identifier, os_version_identifier)
      VALUES(clientlog_seq.NEXTVAL, SUBSTR(logOpeType, 1, 20), SUBSTR(message, 1, 4000) , SYSDATE, vclientId, SUBSTR(vhost, 1, 40), SUBSTR(vipaddress, 1, 40) , SUBSTR(vlogstatus, 1, 20), SUBSTR(vgsm, 1, 40), SUBSTR(vstatus_code,1 , 400), SUBSTR(pstatus_message, 1, 4000), SUBSTR(vApplicationId, 1, 40), SUBSTR(vSessionId, 1, 60), SUBSTR(vDeviceId, 1, 60), SUBSTR(vAppVersionId, 1, 100), SUBSTR(vOSVersionId, 1, 100));
      COMMIT;
    ELSE
      slog.warn(pkgCtxId, myUnit, 'Client "'||vclientId||'" does NOT exist');
      COMMIT;
    END IF;
  EXCEPTION
    WHEN sspkg.sysException THEN
      ROLLBACK;
      RAISE;
    WHEN OTHERS THEN
      ROLLBACK;
      sspkg.raiseOraError(pkgCtxId, myUnit);
  END;

  PROCEDURE setDevicePwdHashStatus(
    pClientId client.id%TYPE,
    pDeviceId app_extauth.dev_id%TYPE,
	pPwdHashStatus app_extauth.ph6%TYPE
	) AS

  PRAGMA AUTONOMOUS_TRANSACTION;
    myunit CONSTANT VARCHAR2(30) := 'setDevicePwdHashStatus';
  BEGIN
    slog.debug(pkgCtxId, myUnit);

	UPDATE app_extauth SET ph6 = pPwdHashStatus WHERE
	dev_id = pDeviceId AND client_id = pClientId
	AND NVL(ph6, '-') <> pPwdHashStatus;
	COMMIT;

  EXCEPTION
    WHEN sspkg.sysException THEN
	  slog.error(pkgCtxId, myUnit, sspkg.getErrorMessage);
      ROLLBACK;
      RAISE;
    WHEN OTHERS THEN
	  slog.error(pkgCtxId, myunit, sqlcode||':'||sqlerrm);
      ROLLBACK;
      sspkg.raiseOraError(pkgCtxId, myUnit);
  END;

  -- Close session and RAISE no error
  PROCEDURE quietCloses as
  begin
    mcsm.closes;
  EXCEPTION WHEN OTHERS THEN NULL;
  END;

  -- Destroy session and RAISE no error
  PROCEDURE quietDestroyS as
  begin
    mcsm.destroys;
  EXCEPTION WHEN OTHERS THEN NULL;
  END;

  FUNCTION hasLicense(pApplicationId VARCHAR2, pClientId client.id%TYPE)
  RETURN BOOLEAN IS
    myunit CONSTANT VARCHAR2(30) := 'hasLicense';
    vPom PLS_INTEGER;
  BEGIN
    slog.debug(pkgCtxId, myUnit, pApplicationId || ':' || pClientId);

	SELECT NULL
	  INTO vPom
      FROM DUAL
    WHERE EXISTS (SELECT /*+ FIRST_ROWS(1) */ NULL
		from licensemgrrt.lic_used l join licensemgrrt.modul m on (m.id = l.modul_id)
		where l.end_users_id = pClientId and m.app_id = pApplicationId);

     RETURN TRUE;

    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN FALSE;

  END hasLicense;

  FUNCTION hasLicense(pApplicationId VARCHAR2)
  RETURN BOOLEAN IS
    myunit CONSTANT VARCHAR2(30) := 'hasLicense2';
  BEGIN
	slog.debug(pkgCtxId, myUnit, pApplicationId);
	RETURN hasLicense(pApplicationId, getSCID());
  END hasLicense;

  FUNCTION isAccountant(pClientId IN client.id%TYPE DEFAULT getClientId())
  RETURN BOOLEAN
  IS
    myunit CONSTANT VARCHAR2(30) := 'isAccountant';
    vPom PLS_INTEGER;
  BEGIN
    slog.debug(pkgCtxId, myUnit);

	SELECT NULL
	  INTO vPom
      FROM DUAL
    WHERE EXISTS (SELECT NULL
                    FROM licensemgrrt.lic_used lu JOIN licensemgrrt.modul m ON (lu.modul_id = m.id)
                   WHERE lu.end_users_id = pClientId
				      AND m.accountant = 1 AND m.app_id = getSApp());
     RETURN TRUE;

    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            RETURN FALSE;
    END isAccountant;

	PROCEDURE RegisterSplunkNotification(pActionId IN VARCHAR2, pClientId client.id%TYPE, pIpAddress clientlog.ipAddress%TYPE, pApplicationId clientlog.application_id%TYPE) IS
		myunit CONSTANT VARCHAR2(30) := 'RegisterSplunkNotification';
		vAccountOwnerId mcore.end_users.ph1%TYPE;

		vMessage strmadmin.splunk_serviceaccessdata := strmadmin.splunk_serviceaccessdata();
		vClientActionsDataNtf strmadmin.splunk_clientactionsdata := strmadmin.splunk_clientactionsdata();
		vSessionAccOwner VARCHAR2(30);

		CURSOR cAccOwners IS
			SELECT DISTINCT ba.account_owner_id account_owner_id
			FROM mcore.bank_accounts ba
			JOIN mcore.action_grants ag ON (ag.account_id = ba.id)
			WHERE ag.action_id IN (common_pck.cACT_CreateTranpay, common_pck.cACT_SignTranpays)
			AND   ag.user_id = getClientId()
			AND   ag.application_id = getApplicationId()
			AND   ag.req_type_id IN (common_pck.cRTI_UPP, common_pck.cRTI_PPI)
			AND   ag.valid = 1
			AND   ba.status = 'A'
			AND   ba.bank_account_type_id IN ('21','80','81');

			CURSOR cAccOwners1 IS
			SELECT DISTINCT ba.account_owner_id account_owner_id
			FROM mcore.bank_accounts ba
			JOIN mcore.action_grants ag ON (ag.account_id = ba.id)
			WHERE ag.action_id = common_pck.cACT_ViewAccount
			AND   ag.user_id = pClientId
			AND   ag.application_id = getApplicationId()
			AND   ag.valid = 1
			AND   ba.status = 'A'
			AND   ba.bank_account_type_id IN ('21','80','81');


		PRAGMA AUTONOMOUS_TRANSACTION;
	BEGIN

		IF sspkg.ReadBool(common_pck.cSPK_HEC_INTERFACE_Context || '/SERVICE_ACCESS_DATA/enabled') and pActionId = common_pck.cLogin THEN

/*
TYPE mcore.splunk_serviceaccessdata AS OBJECT
( datetime date,
  service_type_id VARCHAR2(40 CHAR),
  account_id VARCHAR2(40 CHAR),
  acc_owner_id VARCHAR2(40 CHAR),
  transaction_id NUMBER(40 CHAR),
  tranpay_target_account_id VARCHAR2(60 CHAR),
  tranpay_amount NUMBER,
  tranpay_currency VARCHAR2(40 CHAR),
  client_ip VARCHAR2(40 CHAR),
  user_id NUMBER,
  action_id VARCHAR2(40 CHAR),*/


		slog.debug(pkgCtxId, myUnit);

		vSessionAccOwner := getAccountOwner();
		IF vSessionAccOwner IS NULL THEN
			slog.debug(pkgCtxId, myUnit, 'Session accOwner not set!');
			ROLLBACK;
			RETURN;
		END IF;


			vMessage.datetime := sysdate;
			vMessage.service_type_id := getApplicationId();
			vMessage.client_ip := getIPAddress();
			vMessage.user_id := getClientId();
			vMessage.action_id := pActionId;


			slog.debug(pkgCtxId, myUnit, 'AccOwner for session ' || vSessionAccOwner);

			IF vSessionAccOwner = '%' THEN

				FOR accOwner IN cAccOwners LOOP
					vMessage.acc_owner_id := accOwner.account_owner_id;

					strmadmin.EnqueueSPLUNKSrvcAccsNtfMsg(vMessage);
					slog.debug(pkgCtxId, myUnit, 'Notification for accOwner: ' || vMessage.acc_owner_id);

				END LOOP;
			ELSE
				vMessage.acc_owner_id := vSessionAccOwner;
				strmadmin.EnqueueSPLUNKSrvcAccsNtfMsg(vMessage);
				slog.debug(pkgCtxId, myUnit, 'Notification for accOwner: ' || vMessage.acc_owner_id);
			END IF;
			COMMIT;

		END IF;

			IF sspkg.ReadBool(common_pck.cSPK_HEC_INTERFACE_Context || '/CLIENT_ACTIONS_DATA/enabled') and pActionId in (common_pck.cSuccLogin, common_pck.cFailedLogin) THEN

/*
TYPE mcore.splunk_clientactionsdata AS OBJECT
( id VARCHAR2(40 CHAR),
  client_id VARCHAR2(40 CHAR),
  acc_owner_id VARCHAR2(20 CHAR),
  acc_owner_id_affected VARCHAR2(20 CHAR),
  action_date DATE,
  ip_address VARCHAR2(15 CHAR),
  session_id VARCHAR2(40 CHAR),
  action_type VARCHAR2(20 CHAR),
  service_type VARCHAR2(40 CHAR),
  account_id VARCHAR2(20 CHAR),
  account_currency_id NUMBER,
  CONSTRUCTOR FUNCTION splunk_clientactionsdata RETURN SELF AS RESULT
)
*/

			vClientActionsDataNtf.id := strmadmin.splunk_client_actions_seq.nextval;

            vClientActionsDataNtf.client_id := pClientId;

			vClientActionsDataNtf.action_date := SYSDATE;

			vClientActionsDataNtf.ip_address := pIpAddress;

			vClientActionsDataNtf.session_id := mcsm.getSessionId();

			vClientActionsDataNtf.action_type := pActionId;

			vClientActionsDataNtf.service_type := 'mobile';

			IF pApplicationId = common_pck.cAPP_THIN THEN

				vClientActionsDataNtf.service_type := 'internet';

			END IF;

				BEGIN
					SELECT eu.ph1
					INTO vAccountOwnerId
					FROM mcore.end_users eu
					WHERE eu.id = pClientId;

					IF vAccountOwnerId IS NOT NULL THEN
						vClientActionsDataNtf.acc_owner_id_affected := vAccountOwnerId;
						vClientActionsDataNtf.acc_owner_id := vAccountOwnerId;
						strmadmin.EnqSPLUNKClientActionsNtfMsg(pMessage => vClientActionsDataNtf);
						slog.debug(pkgCtxId, myUnit, 'Notification for accOwner: ' || vClientActionsDataNtf.acc_owner_id);
						slog.debug(pkgCtxId, myUnit, 'EnqSPLUNKClientActionsNtfMsg done!');
					ELSE
						FOR accOwner IN cAccOwners1 LOOP
							vClientActionsDataNtf.acc_owner_id_affected := accOwner.account_owner_id;
							vClientActionsDataNtf.acc_owner_id := accOwner.account_owner_id;
							strmadmin.EnqSPLUNKClientActionsNtfMsg(pMessage => vClientActionsDataNtf);
							slog.debug(pkgCtxId, myUnit, 'Notification for accOwner: ' || vClientActionsDataNtf.acc_owner_id);
							slog.debug(pkgCtxId, myUnit, 'EnqSPLUNKClientActionsNtfMsg done!');
						END LOOP;
					END IF;
				EXCEPTION
					WHEN NO_DATA_FOUND THEN
							slog.error(pkgCtxId, myUnit, common_pck.cERR_UnknownEndUser || ':' || pClientId);
					WHEN OTHERS THEN
						slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
				END;

			COMMIT;

		END IF;
	ROLLBACK;
	EXCEPTION
		WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, common_pck.cERR_UnknownAccount);
			ROLLBACK;

		WHEN OTHERS THEN
			slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
			ROLLBACK;
	END RegisterSplunkNotification;

	PROCEDURE RegisterAccOwnerAccess(pMessage VARCHAR2) IS
		myunit CONSTANT VARCHAR2(30) := 'RegisterAccOwnerAccess';

    		vClientID NUMBER;
    		vSelectedAccOwner VARCHAR2(30);

		CURSOR cAccOwners(pAccOwnerId mcore.bank_accounts.account_owner_id%TYPE) IS
			SELECT DISTINCT ba.account_owner_id account_owner_id
			FROM mcore.bank_accounts ba
			JOIN mcore.action_grants ag ON (ag.account_id = ba.id)
			WHERE ag.action_id IN (common_pck.cACT_CreateTranpay, common_pck.cACT_SignTranpays)
			AND   ag.user_id = getClientId()
			AND   ag.application_id = getApplicationId()
			AND   ag.req_type_id IN (common_pck.cRTI_UPP, common_pck.cRTI_PPI)
			AND   ag.valid = 1
			AND   ba.status = 'A'
      			AND   ba.account_owner_id like pAccOwnerId;

	BEGIN
		slog.debug(pkgCtxId, myUnit);

    		vClientID := getClientId();
    		vSelectedAccOwner := mcsm.getSelectedAccOwner();

    		IF vSelectedAccOwner IS NULL THEN
			slog.debug(pkgCtxId, myUnit, 'Selected accOwner not set!');
			RETURN;
		END IF;

    IF NOT sspkg.readbool(pkgCtxId || '/registerAccOwnerInClientLog') THEN
        slog.debug(pkgCtxId, myUnit, 'Account owner registration in clientlog is not enabled!');
        RETURN;
    END IF;

    slog.debug(pkgCtxId, myUnit, 'Selected AccOwner from session: ' || vSelectedAccOwner);

    IF vSelectedAccOwner is not NULL THEN
      FOR accOwner IN cAccOwners(vSelectedAccOwner) LOOP
        writeClientLog(logOpeType => 'SETACCOWNER', message => pMessage, vclientId => vClientID, vstatus_message => accOwner.account_owner_id);
        slog.debug(pkgCtxId, myUnit, 'Register account owner ' || accOwner.account_owner_id || ' in clientlog');
      END LOOP;
    END IF;

  EXCEPTION
    WHEN no_data_found THEN
      slog.error(pkgCtxId, myUnit, common_pck.cERR_UnknownAccount);
    WHEN OTHERS THEN
      slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
  END RegisterAccOwnerAccess;

  PROCEDURE setLogged(clientId client.id%TYPE) as
	myunit CONSTANT VARCHAR2(30) := 'setLogged';
	vDefAccOwner mcore.end_users.ph1%TYPE;
  begin
    slog.debug(pkgCtxId, myUnit, clientId);
    setSCID(clientId);
    -- Check IF user is NOT an accountant. Then set account owner as %
    IF NOT isAccountant() THEN
        slog.debug(pkgCtxId, myUnit, 'Not an accountant!');
        mcsm.setAccountOwner(pAccountOwnerId => '%');
        mcsm.setSelectedAccOwner(pAccountOwnerId => '%');
    ELSE
	  -- #3556: cAPP_MOBILE nema mogu?nost odabira vlasnika ra?una. Login na cAPP_MOBILE sprije?ava rad ako se pojavljuje kombinacija pravno / fizi?ko lice
	  -- See mmobile.auth.body:224
      IF getSApp() = common_pck.cAPP_MOBILE THEN
      slog.debug(pkgCtxId, myUnit, 'Accountant, but application is ' || common_pck.cAPP_MOBILE);

        IF NOT sspkg.readBool('/Core/Auth/setDefAccountOwnerDuringLogon') THEN
          mcsm.setAccountOwner(pAccountOwnerId => '%');
        ELSE
          BEGIN
            SELECT eu.ph1
            INTO vDefAccOwner
            FROM mcore.end_users eu
            WHERE eu.id = clientId;
          EXCEPTION
            WHEN no_data_found THEN
              vDefAccOwner := NULL;
          END;
          mcsm.setAccountOwner(pAccountOwnerId => vDefAccOwner);
        END IF;
      END IF;

      mcsm.setSelectedAccOwner(pAccountOwnerId => NULL);

    END IF;

    writeClientLog(logOpeType => 'LOGIN', message => 'EXTENDED_LOGIN', vclientId => clientId, vstatus_message => 'Login successfull');

	-- za novi splunk bitna je samo prijava na sistem kako bi pratili brute-force attack
	RegisterSplunkNotification(pActionId => common_pck.cSuccLogin, pClientID => clientId, pIpAddress => getIPAddress(), pApplicationId => getApplicationId());
  	RegisterAccOwnerAccess('BY LOGIN');
  END setLogged;

  /* True IF login PROCEDURE is fully and successfully completed (basic login and extended login IF needed) */

  FUNCTION isLogged RETURN BOOLEAN as
  begin
	RETURN NOT(getSCID is NULL);
  END;

  PROCEDURE write(varName VARCHAR2, varValue VARCHAR2) AS
   myunit CONSTANT VARCHAR2(30) := 'write';
  BEGIN
    slog.debug(pkgCtxId, myUnit, varName || ':' || mcsm.trunc4log(varValue));
    IF NOT isLogged THEN
        sspkg.raiseError(cERR_NotPermited, 'Cannot change var before login is completed', pkgCtxId, myUnit);
    END IF;

    for rw in reservedWords.first .. reservedWords.last LOOP
      IF upper(varName) = upper(reservedWords(rw)) THEN
        sspkg.raiseError(cERR_NotPermited, 'Reserved vars cannot be changed', pkgCtxId, myUnit);
      END IF;
    END LOOP;
    IF SUBSTR(varName, 1, 1) = '_' THEN
        sspkg.raiseError(cERR_NotPermited, 'Cannot write to vars that with names that start with "_"', pkgCtxId, myUnit);
    END IF;

    mcsm.write(varName, varValue);

  EXCEPTION
    WHEN sspkg.sysException THEN
      RAISE;
    WHEN OTHERS THEN
      sspkg.raiseOraError(pkgCtxId, myUnit);
      RAISE;
  END;

  FUNCTION read(varName VARCHAR2) RETURN VARCHAR2 AS
    myunit CONSTANT VARCHAR2(30) := 'read';
  BEGIN
    slog.debug(pkgCtxId, myUnit, varName);
    IF NOT isLogged THEN
        sspkg.raiseError(cERR_NotPermited, 'Cannot read var before login is completed', pkgCtxId, myUnit);
    END IF;
    RETURN mcsm.read(varName);
  END;

  FUNCTION readMDate(varName VARCHAR2) RETURN DATE AS
    myunit CONSTANT VARCHAR2(30) := 'readMDate';
  BEGIN
    slog.debug(pkgCtxId, myUnit, varName);
    IF NOT isLogged THEN
        sspkg.raiseError(cERR_NotPermited, 'Cannot read var before login is completed', pkgCtxId, myUnit);
    END IF;
    RETURN mcsm.readMDate(varName);
  END;

  FUNCTION hash(var VARCHAR2) RETURN VARCHAR2 AS
  BEGIN
    RETURN common_pck.hash(var);
  END hash;

  -- Used by : clientRequirePKILogin (public)
  -- clientSignatureMethod(public)
  -- basicLogin()
  -- NAPOMENA: Funkcija u slučaju thinclient-a ne treba očekivati pDeviceId jer thinclient nezna ovu informaciju apriori
  --           Za razliku od toga, "device-aware" aplikacije kao što je elbamobile su vezanu uz device, te isti proslijeđuju
  FUNCTION clientExtAuthID(pUsername VARCHAR2, pDeviceId IN OUT VARCHAR2, pApplicationId VARCHAR2)
  RETURN app_extauth.extauth_id%TYPE AS
    myunit CONSTANT VARCHAR2(30) := 'clientExtAuthID(uname,dev,app)';
	vClientId client.id%TYPE;
	vExtAuthId app_extauth.extauth_id%TYPE;
  BEGIN
	slog.debug(pkgCtxId, myUnit, pUsername ||':' || pDeviceId || ':' || pApplicationId);

	deviceAuthorizationStatus := NULL;
	vClientId := getCID4User(vusername=>pUsername);
	slog.debug(pkgCtxId, myUnit, 'Determined client ID for user: ' || vClientId);
    BEGIN
		IF common_pck.isAppDeviceAware(pApplicationId) = 0 THEN
			slog.debug(pkgCtxId, myUnit, 'Device unaware application!');
			-- In this case, dev_id is not known prior login, and have to be fetched from database
			-- Only one record should be found (and is supported)!
			SELECT ae.extauth_id, ae.dev_id
			INTO vExtAuthId, pDeviceId
			FROM app_extauth ae
			WHERE ae.client_id = vClientId
			AND ae.application_id = pApplicationId
			AND ae.valid = 1;
		ELSE
			-- Dev_id is known prior login, and is used to restrict data!
			slog.debug(pkgCtxId, myUnit, 'Device aware application!');
			BEGIN
				SELECT ae.extauth_id
				INTO vExtAuthId
				FROM app_extauth ae
				WHERE ae.client_id = vClientId
				AND ae.application_id = pApplicationId
				AND ae.dev_id = pDeviceId
				AND ae.valid = 1;
			EXCEPTION
				WHEN NO_DATA_FOUND THEN
					deviceAuthorizationStatus := 'UNAUTHORIZED';
					sspkg.raiseError(cERR_UnauthorizedDevice, NULL, pkgCtxId, myunit);
			END;

		END IF;
		slog.debug(pkgCtxId, myUnit, 'vExtAuthId:'|| vExtAuthId);
    RETURN vExtAuthId;

	EXCEPTION
		WHEN NO_DATA_FOUND THEN
			slog.error(pkgCtxId, myUnit, 'NO-DATA-FOUND:' || vClientId || ':' || pDeviceId);
			sspkg.raiseError('/Core/Auth/err/ExtAuthProblem', NULL, pkgCtxId, myunit);
		WHEN TOO_MANY_ROWS THEN
			slog.error(pkgCtxId, myUnit, 'TOO_MANY_ROWS:' || vClientId || ':' || pDeviceId);
			sspkg.raiseError('/Core/Auth/err/ExtAuthProblem', NULL, pkgCtxId, myunit);
		WHEN sspkg.sysexception THEN
			RAISE;
		WHEN OTHERS THEN
			sspkg.raiseOraError(pkgCtxId, myUnit);
	END;

  END clientExtAuthID;

	-- Private !!!!
	FUNCTION clientExtAuthID
	RETURN app_extauth.extauth_id%TYPE AS
		myunit CONSTANT VARCHAR2(30) := 'clientExtAuthID';
		vDeviceId app_extauth.dev_id%TYPE;
	BEGIN
		slog.debug(pkgCtxId, myUnit);
		-- DeviceId should already be saved in session context!
		vDeviceId := getDeviceid();
		RETURN clientExtAuthID(pUsername=>lower(getSUser), pDeviceId=>vDeviceId, pApplicationId=>getApplicationId());

	EXCEPTION
		WHEN sspkg.sysException THEN
		RAISE;
		WHEN OTHERS THEN
		sspkg.raiseOraError(pkgCtxId, myUnit);
		RAISE;
	END clientExtAuthID;

	FUNCTION clientOTPType(pUsername VARCHAR2, pDeviceId VARCHAR2, pApplicationId VARCHAR2, pClientExtAuthId app_extauth.extauth_id%TYPE := NULL) RETURN VARCHAR2 AS
		myunit CONSTANT VARCHAR2(30) := 'clientOTPType';
		vClientExtAuthId app_extauth.extauth_id%TYPE := pClientExtAuthId;
		vDeviceId app_extauth.dev_id%TYPE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pUsername || ':' ||pDeviceId||':'||pApplicationId);
		vDeviceId := pDeviceId;
		IF vClientExtAuthId IS NULL THEN
			vClientExtAuthId := clientExtAuthID(pUsername=>pUsername, pDeviceId=>vDeviceId, pApplicationId=>pApplicationId);
		END IF;

		IF vClientExtAuthId is NULL THEN
			RETURN NULL;
		ELSE
			RETURN upper(sspkg.readVChar(extAuthCtx||'/'||vClientExtAuthId||'/OtpType'));
		END IF;
	END clientOTPType;

	FUNCTION clientOTPType(pClientExtAuthId app_extauth.extauth_id%TYPE := NULL) RETURN VARCHAR2 AS
		myunit CONSTANT VARCHAR2(30) := 'clientOTPType2';
	BEGIN
		slog.debug(pkgCtxId, myUnit);
		RETURN clientOTPType(pUsername=>lower(getSUser), pDeviceId=>getDeviceid(), pApplicationId=>getApplicationId(), pClientExtAuthId => pClientExtAuthId);

	EXCEPTION
		WHEN sspkg.sysException THEN
		RAISE;
		WHEN OTHERS THEN
		sspkg.raiseOraError(pkgCtxId, myUnit);
		RAISE;
	END clientOTPType;

	FUNCTION clientLoginMethod(pUsername VARCHAR2, pDeviceId VARCHAR2, pApplicationId VARCHAR2, pClientExtAuthId app_extauth.extauth_id%TYPE := NULL) RETURN VARCHAR2 AS
		myunit CONSTANT VARCHAR2(30) := 'clientLoginMethod';
		vClientExtAuthId app_extauth.extauth_id%TYPE := pClientExtAuthId;
		vDeviceId app_extauth.dev_id%TYPE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pUsername || ':' ||pDeviceId||':'||pApplicationId);
		vDeviceId := pDeviceId;
		IF vClientExtAuthId IS NULL THEN
			vClientExtAuthId := clientExtAuthID(pUsername=>pUsername, pDeviceId=>vDeviceId, pApplicationId=>pApplicationId);
		END IF;

		IF vClientExtAuthId is NULL THEN
			RETURN NULL;
		ELSE
			RETURN upper(sspkg.readVChar(extAuthCtx||'/'||vClientExtAuthId||'/LoginMethod'));
		END IF;
	END clientLoginMethod;

	FUNCTION clientSignatureOtpType(pUsername VARCHAR2, pDeviceId VARCHAR2, pApplicationId VARCHAR2, pClientExtAuthId app_extauth.extauth_id%TYPE := NULL)
	RETURN VARCHAR2 AS
		vClientExtAuthId app_extauth.extauth_id%TYPE := pClientExtAuthId;
		vDeviceId app_extauth.dev_id%TYPE;
	BEGIN
		vDeviceId := pDeviceId;
		IF vClientExtAuthId IS NULL THEN
			vClientExtAuthId := clientExtAuthID(pUsername=>pUsername, pDeviceId=>vDeviceId, pApplicationId=>pApplicationId);
		END IF;

		IF vClientExtAuthId IS NULL THEN
			RETURN NULL;
		ELSE
			RETURN upper(sspkg.readVChar(extAuthCtx||'/'||vClientExtAuthId||'/SignatureOtpType'));
		END IF;

	END clientSignatureOtpType;

	FUNCTION clientSignatureOtpType(pClientExtAuthId app_extauth.extauth_id%TYPE := NULL) RETURN VARCHAR2 AS
		myunit CONSTANT VARCHAR2(30) := 'clientSignatureOtpType2';
	BEGIN
		slog.debug(pkgCtxId, myUnit);
		RETURN clientSignatureOtpType(pUsername=>lower(getSUser), pDeviceId=>getDeviceid(), pApplicationId=>getApplicationId(), pClientExtAuthId => pClientExtAuthId);
	EXCEPTION
		WHEN sspkg.sysException THEN
		RAISE;
		WHEN OTHERS THEN
		sspkg.raiseOraError(pkgCtxId, myUnit);
		RAISE;
	END clientSignatureOtpType;

	FUNCTION getExtAuthBoolParameterValue(pClientExtAuthId app_extauth.extauth_id%TYPE := NULL, pParameter VARCHAR2)
	RETURN BOOLEAN AS
		vClientExtAuthId app_extauth.extauth_id%TYPE := pClientExtAuthId;
		myunit CONSTANT VARCHAR2(30) := 'getExtAuthBoolParameterValue';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientExtAuthId || ':' || pParameter);
		IF vClientExtAuthId IS NULL THEN
			vClientExtAuthId := clientExtAuthId();
			slog.debug(pkgCtxId, myUnit, 'vClientExtAuthId: ' || vClientExtAuthId);
		END IF;
		IF vClientExtAuthId IS NULL THEN
			slog.warn(pkgCtxId, myUnit, 'Unable to determine client ExtAuthId!');
			RETURN FALSE;
		ELSE
			slog.debug(pkgCtxId, myUnit, 'Fetch value for key ' || extAuthCtx || '/' || vClientExtAuthId || '/' || pParameter);
			RETURN sspkg.readBool(extAuthCtx || '/' || vClientExtAuthId || '/' || pParameter);
		END IF;
	EXCEPTION
		WHEN sspkg.sysException THEN
			RAISE;
		WHEN OTHERS THEN
			sspkg.raiseOraError(pkgCtxId, myUnit);
			RAISE;
	END getExtAuthBoolParameterValue;

	FUNCTION getExtAuthVcharParameterValue(pClientExtAuthId app_extauth.extauth_id%TYPE := NULL, pParameter VARCHAR2)
	RETURN VARCHAR2 AS
		vClientExtAuthId app_extauth.extauth_id%TYPE := pClientExtAuthId;
		myunit CONSTANT VARCHAR2(30) := 'getExtAuthVcharParameterValue';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientExtAuthId || ':' || pParameter);
		IF vClientExtAuthId IS NULL THEN
			vClientExtAuthId := clientExtAuthId();
		END IF;
		IF vClientExtAuthId IS NULL THEN
			RETURN NULL;
		ELSE
			RETURN sspkg.readVChar(extAuthCtx || '/' || vClientExtAuthId || '/' || pParameter);
		END IF;
	EXCEPTION
		WHEN sspkg.sysException THEN
			RAISE;
		WHEN OTHERS THEN
			sspkg.raiseOraError(pkgCtxId, myUnit);
			RAISE;
	END getExtAuthVcharParameterValue;

	-- there is no session WHEN this PROCEDURE executes
	FUNCTION getExtAuthBoolParameterValue(pUsername VARCHAR2, pDeviceId VARCHAR2, pApplicationId VARCHAR2, pClientExtAuthId app_extauth.extauth_id%TYPE := NULL, pParameter VARCHAR2)
	RETURN BOOLEAN AS
		vDeviceId app_extauth.dev_id%TYPE;
		vClientExtAuthId app_extauth.extauth_id%TYPE := pClientExtAuthId;
		myunit CONSTANT VARCHAR2(30) := 'getExtAuthBoolParameterValue2';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pParameter);
		vDeviceId := pDeviceId;
		IF vClientExtAuthId IS NULL THEN
			vClientExtAuthId := clientExtAuthID(pUsername=>pUsername, pDeviceId=>vDeviceId, pApplicationId=>pApplicationId);
		END IF;
		IF vClientExtAuthId IS NULL THEN
			RETURN FALSE;
		ELSE
			RETURN sspkg.readBool(extAuthCtx || '/' || vClientExtAuthId || '/' || pParameter);
		END IF;
	EXCEPTION
		WHEN sspkg.sysException THEN
			RAISE;
		WHEN OTHERS THEN
			sspkg.raiseOraError(pkgCtxId, myUnit);
			RAISE;
	END getExtAuthBoolParameterValue;

	FUNCTION getExtAuthVcharParameterValue(pUsername VARCHAR2, pDeviceId VARCHAR2, pApplicationId VARCHAR2, pClientExtAuthId app_extauth.extauth_id%TYPE := NULL, pParameter VARCHAR2)
	RETURN VARCHAR2 AS
		vDeviceId app_extauth.dev_id%TYPE;
		vClientExtAuthId app_extauth.extauth_id%TYPE := pClientExtAuthId;
		myunit CONSTANT VARCHAR2(30) := 'getExtAuthVcharParameterValue';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pUsername || ':' || pDeviceId || ':' || pApplicationId || ':' || pClientExtAuthId || ':' || pParameter);
		vDeviceId := pDeviceId;
		IF vClientExtAuthId IS NULL THEN
			vClientExtAuthId := clientExtAuthID(pUsername=>pUsername, pDeviceId=>vDeviceId, pApplicationId=>pApplicationId);
		END IF;
		IF vClientExtAuthId IS NULL THEN
			RETURN NULL;
		ELSE
			RETURN sspkg.readVChar(extAuthCtx || '/' || vClientExtAuthId || '/' || pParameter);
		END IF;
	EXCEPTION
		WHEN sspkg.sysException THEN
			RAISE;
		WHEN OTHERS THEN
			sspkg.raiseOraError(pkgCtxId, myUnit);
			RAISE;
	END getExtAuthVcharParameterValue;

	FUNCTION clientRequirePKILogin(vusername VARCHAR2, pDeviceId VARCHAR2, pApplicationId VARCHAR2, pClientExtAuthId app_extauth.extauth_id%TYPE := NULL)
	RETURN BOOLEAN AS
	BEGIN
		RETURN getExtAuthBoolParameterValue (pUsername => vusername, pDeviceId => pDeviceId, pApplicationId => pApplicationId, pClientExtAuthId => pClientExtAuthId, pParameter => 'RequirePKILogin');
	END clientRequirePKILogin;

	FUNCTION clientRequirePKILogin(pClientExtAuthId app_extauth.extauth_id%TYPE := NULL)
	RETURN BOOLEAN AS
	BEGIN
		RETURN getExtAuthBoolParameterValue (pClientExtAuthId => pClientExtAuthId, pParameter => 'RequirePKILogin');
	END clientRequirePKILogin;

	FUNCTION clientSignatureMethod(pClientExtAuthId app_extauth.extauth_id%TYPE := NULL) RETURN VARCHAR2 AS
	BEGIN
		RETURN getExtAuthVcharParameterValue (pClientExtAuthId => pClientExtAuthId, pParameter => 'SignatureMethod');
	END clientSignatureMethod;

	-- Returns -1 if password will never expire
	-- Returns 0 if password has expired
	-- Returns value gt 0 which is the number of days when password will expire
	FUNCTION passwordExpireIn(pPasswordCreatedAt IN DATE)
	RETURN PLS_INTEGER IS
		myunit CONSTANT VARCHAR2(30) := 'passwordExpireIn';
		vCurrentDate DATE;
		vPasswordWillExpireAt DATE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, TO_CHAR(pPasswordCreatedAt, common_pck.cDATE_MASK));
		vCurrentDate := TRUNC(SYSDATE);
		IF (vPasswordExpireInDays > 0) AND (pPasswordCreatedAt IS NOT NULL) THEN
			vPasswordWillExpireAt := pPasswordCreatedAt + vPasswordExpireInDays;
			slog.debug(pkgCtxId, myUnit, 'Password will expire at ' || TO_CHAR(vPasswordWillExpireAt, common_pck.cDATE_MASK));
			IF  vPasswordWillExpireAt < vCurrentDate THEN
				RETURN 0;
			ELSE
				RETURN (vPasswordWillExpireAt - vCurrentDate);
			END IF;
		END IF;

		RETURN -1;
	END passwordExpireIn;

	FUNCTION passwordExpireIn(pUserId IN client.id%TYPE)
	RETURN PLS_INTEGER IS
		myunit CONSTANT VARCHAR2(30) := 'passwordExpireIn2';
		vPasswordCreatedAt DATE;
		vCurrentDate DATE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pUserId || ':' || TO_CHAR(vCurrentDate, common_pck.cDATE_MASK));
		vCurrentDate := TRUNC(SYSDATE);
		IF vPasswordExpireInDays > 0 THEN
			BEGIN
				SELECT TO_DATE(PH4, common_pck.cDATE_MASK)
				INTO vPasswordCreatedAt
				FROM client
				WHERE id = pUserId;
			EXCEPTION
				WHEN no_data_found THEN
					slog.error(pkgCtxId, myUnit, cERR_NO_SUCH_USER || ':' || pUserId);
					sspkg.raiseError(cERR_NO_SUCH_USER, NULL, pkgCtxId, myunit);
				WHEN OTHERS THEN
					slog.error(pkgCtxId, myUnit, sqlerrm);
					sspkg.raiseOraError(pkgCtxId, myunit);
			END;
			RETURN passwordExpireIn(pPasswordCreatedAt => vPasswordCreatedAt);
		END IF;

		RETURN -1;
	END passwordExpireIn;

	PROCEDURE disablePassword(pUserId client.id%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'disablePassword';
		PRAGMA AUTONOMOUS_TRANSACTION;
	BEGIN
		UPDATE client SET password_enabled = 0, ph5 = TO_CHAR(SYSDATE, 'DD.MM.YYYY HH24:MI:SS') WHERE id = pUserId;
		COMMIT;
	EXCEPTION
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myUnit, cERR_UnableToDisablePassword);
			ROLLBACK;
	END disablePassword;

	FUNCTION checkPwdMatchOld(pUsername VARCHAR2, pOldPassword VARCHAR2, pOldPasswordHash VARCHAR2, pNOnce IN VARCHAR2)
	RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(30) := 'checkPwdMatchOld';
	BEGIN
		IF pNOnce IS NOT NULL THEN
			--digest = SHA-1(username:MD5(password):cnonce)
			IF NVL(dbms_crypto.hash(UTL_I18N.STRING_TO_RAW (pUsername||NVL(pOldPasswordHash, 'NULL')||pNOnce), DBMS_CRYPTO.HASH_SH1),'NULL') NOT IN (UPPER(pOldPassword), pOldPassword) THEN

				loginStatusCode := cERR_WrongCredentials;
				loginStatusMessage := SUBSTR(mlang.trans(getLang, cERR_WrongCredentials),1,loginStatusMessageMaxLength);

				checkAndLock(getCID4User(pUsername), 'CHPWD');

				slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" incorrect password (nOnce)');
				RETURN FALSE;
			END IF;
		ELSE

			IF NVL(pOldPasswordHash, 'NULL') NOT IN (hash(pOldPassword), hash(upper(pOldPassword))) THEN
				loginStatusCode := cERR_WrongCredentials;
				loginStatusMessage := SUBSTR(mlang.trans(getLang, cERR_WrongCredentials),1,loginStatusMessageMaxLength);
				checkAndLock(getCID4User(pUsername), 'CHPWD');
				slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" incorrect password');
				RETURN FALSE;
			END IF;
		END IF;
		RETURN TRUE;
    END checkPwdMatchOld;

  function getSecKey(pclientId IN app_extauth.client_id%TYPE, papplication_id IN app_extauth.application_id%TYPE, pdev_id IN app_extauth.dev_id%TYPE)
  return app_extauth.sec_key%TYPE
  IS
	myunit CONSTANT VARCHAR2(30) := 'getSecKey';
	vSecKey app_extauth.sec_key%TYPE;
  begin
   slog.debug(pkgCtxId, myUnit, pclientId || ':' || papplication_id || ':' || pdev_id);
	BEGIN
		select sec_key
		into vSecKey
		from app_extauth
		where client_id = pclientId
		and application_id=papplication_id
		and valid = 1
		and dev_id=pdev_id;
	EXCEPTION
		WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, cERR_ExtAuthProblem);
			raise;
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myUnit, sqlerrm);
			sspkg.raiseOraError(pkgCtxId, myunit);

   END;
   RETURN vSecKey;

  end getSecKey;

  procedure getPwdHash(
  	pclientId IN client.id%TYPE,
  	pUsePwdIndicator OUT VARCHAR2,
  	pPwdCaseInsensitiveHash OUT VARCHAR2,
  	pPwdCaseSensitiveHash OUT VARCHAR2)
  IS
	myunit CONSTANT VARCHAR2(30) := 'getPwdHash';
	-- case sensitive password indicator for mobile device - new version with passwordHashed in basicLogin
	vPh6 app_extauth.ph6%TYPE;
	vfrontAppSendHashOfUCasedPwd BOOLEAN;

  begin
   slog.debug(pkgCtxId, myUnit, pclientId);

   vfrontAppSendHashOfUCasedPwd := sspkg.readBool(pkgCtxId || '/frontAppSendHashOfUCasedPwd');

    BEGIN
		SELECT password, password_cs INTO pPwdCaseInsensitiveHash, pPwdCaseSensitiveHash
		FROM client
		WHERE id = pClientId;
	EXCEPTION
		WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, 'Unable to find client data for client_id: ' || pClientId);
            sspkg.raiseError(common_pck.cERR_UnknownEndUser, NULL, pkgCtxId, myunit);
	END;

	pUsePwdIndicator := common_pck.cPwdCaseInSensitiveIndicator;
   -- IF condition added for case sensitive password policy - in case of using old version of mobile dev (without passing pPasswordHashed (real value, not uppercase)) and having not null column password_cs
   	IF getApplicationId = common_pck.cAPP_MOBILE THEN

		BEGIN
			SELECT ph6 INTO vPh6
			FROM app_extauth
			WHERE dev_id = getDeviceId
				AND client_id = pclientId
				AND application_id = common_pck.cAPP_MOBILE
				AND valid = 1;
		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myUnit, cERR_UnauthorizedDevice || ':' ||getDeviceId);
				sspkg.raiseError(cERR_UnauthorizedDevice, NULL, pkgCtxId, myunit);
		END;

		IF (vCaseSensitivePwd AND vPh6 = cMobileDeviceWithPwdHashed AND pPwdCaseSensitiveHash IS NOT NULL) OR NOT vfrontAppSendHashOfUCasedPwd THEN
		    pUsePwdIndicator := common_pck.cPwdCaseSensitiveIndicator;
		END IF;
   	ELSE
		IF vCaseSensitivePwd and pPwdCaseSensitiveHash IS NOT NULL THEN
			pUsePwdIndicator := common_pck.cPwdCaseSensitiveIndicator;
		END IF;
   	END IF;


  end getPwdHash;

	FUNCTION verifyClientPassword (pClientId IN client.id%TYPE, pPassword IN VARCHAR2, pPwdHashed IN BOOLEAN)
	RETURN BOOLEAN IS
		vUsePwdIndicator VARCHAR2(3);

		vPasswordCISHash client.password%TYPE;
		vPasswordCSHash client.password_cs%TYPE;
		vPasswordHash client.password_cs%TYPE;
		myunit CONSTANT VARCHAR2(30) := 'verifyClientPassword';
	BEGIN
		slog.debug(pkgCtxId, myUnit);


		BEGIN
            getPwdHash (
            	pclientId => pClientId,
            	pUsePwdIndicator => vUsePwdIndicator,
            	pPwdCaseInsensitiveHash => vPasswordCISHash,
            	pPwdCaseSensitiveHash => vPasswordCSHash);

            IF vUsePwdIndicator = common_pck.cPwdCaseInSensitiveIndicator THEN
            	vPasswordHash := vPasswordCISHash;
            ELSE
            	vPasswordHash := vPasswordCSHash;
            END IF;

        EXCEPTION
            WHEN sspkg.sysException THEN
				RAISE;
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
                sspkg.raiseError(cERR_InternalError, NULL, pkgCtxId, myunit);
        END;

		IF pPwdHashed THEN
		    slog.debug(pkgCtxId, myUnit, 'pPwdHashed TRUE');
			slog.debug(pkgCtxId, myUnit, vPasswordHash || ':' || pPassword);
			RETURN (NVL(vPasswordHash, 'NULL') = pPassword);
		ELSE
			slog.debug(pkgCtxId, myUnit, 'pPwdHashed FALSE');

			IF vCaseSensitivePwd AND vPasswordCSHash IS NOT NULL THEN
			     slog.debug(pkgCtxId, myUnit, vPasswordHash || ':' || hash(pPassword));
				RETURN NVL(vPasswordHash, 'NULL') = hash(pPassword);
			ELSE
				slog.debug(pkgCtxId, myUnit, vPasswordHash || ':' || hash(pPassword) || ':' || hash(upper(pPassword)));
				RETURN NVL(vPasswordHash, 'NULL') = hash(upper(pPassword));
			END IF;
		END IF;

	END verifyClientPassword;

	FUNCTION verifyClientPassword (pClientId IN client.id%TYPE, pPassword IN VARCHAR2, pPwdHashed IN BOOLEAN,
		pWrite2Log IN BOOLEAN, pCheckAndLock IN BOOLEAN, pCheckAndSendNotification IN BOOLEAN,
		pLogOperationType IN VARCHAR2, pLogMessage IN VARCHAR2, pHost IN VARCHAR2, pIpAddress IN VARCHAR2, pLangId IN VARCHAR2, pApplicationId IN VARCHAR2, pDeviceId IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL)
	RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(30) := 'verifyClientPassword';
	BEGIN
		slog.debug(pkgCtxId, myUnit);

		IF NOT verifyClientPassword ( pClientId => pClientId, pPassword => pPassword,  pPwdHashed => pPwdHashed) THEN

			IF pWrite2Log THEN
				loginStatusCode := cERR_WrongCredentials;
				loginStatusMessage := SUBSTR(mlang.trans(pLangId, cERR_WrongCredentials),1,loginStatusMessageMaxLength);

				writeClientLog(logOpeType => pLogOperationType, message => pLogMessage, vlogstatus=> 'ERROR',
					vclientId => pClientId, vhost => pHost, vipaddress => pIpAddress,
					vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=> pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);
					RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => pClientId, pIpAddress => pIpAddress, pApplicationId => pApplicationId);
			END IF;

			IF pCheckAndLock THEN
				checkAndLock(pClientId, pLogOperationType);
			END IF;

			IF pCheckAndSendNotification THEN
				checkAndSendNotification(pClientId);
			END IF;

			slog.warn(pkgCtxId, myUnit, 'Incorrect password');

			RETURN FALSE;
		END IF;

		loginStatusCode := NULL;
		loginStatusMessage := NULL;

		writeClientLog(logOpeType => pLogOperationType, message => pLogMessage, vlogstatus=> 'OK',
			vclientId => pClientId, vhost => pHost, vipaddress => pIpAddress,
			vstatus_code => loginStatusCode, vstatus_message => 'Password verified', vApplicationId=> pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);


		RETURN TRUE;
	END verifyClientPassword;

  /* THIS FUNCTION HAVE TO BE KEPT PRIVATE ALL TIMES */
  FUNCTION basicAccountCheck(
	pUsername VARCHAR2,
	pPassword VARCHAR2,
	ipAddress VARCHAR2,
	host VARCHAR2,
	sessLogLevel INTEGER,
	langId VARCHAR2,
	pApplicationId VARCHAR2,
	pInstallationId VARCHAR2,
	pClient vClientType,
	pPasswordExpireInDays OUT PLS_INTEGER,
	pNOnce IN VARCHAR2,
	pDeviceId VARCHAR2,
	pClientExtAuthId VARCHAR2,
	pPasswordHashed IN VARCHAR2 DEFAULT NULL,
	pAppVersionId IN VARCHAR2 DEFAULT NULL,
	pOSVersionId IN VARCHAR2 DEFAULT NULL,
	pIsFirstLogin IN BOOLEAN DEFAULT FALSE)
  RETURN BOOLEAN
  IS
	myunit CONSTANT VARCHAR2(30) := 'basicAccountCheck';
	lSinceDate DATE;
    lUntilDate DATE;
	vCurrentDate CONSTANT DATE := SYSDATE;
	vPasswordWillExpireInDays PLS_INTEGER := 0;
	vPasswordChangeTimeOut PLS_INTEGER;
	vPasswordChangeAllowedUntil DATE;
	vDeviceReauthorized VARCHAR2(1);


  BEGIN

    vPasswordChangeTimeOut := NVL(sspkg.readInt(pkgCtxId || '/GracePeriodForPasswordChange'), 15);
	pPasswordExpireInDays := 0;
	slog.debug(pkgCtxId, myUnit);

	IF pUsername is NULL THEN
      slog.warn(pkgCtxId, myUnit, 'Username cannot be NULL');
      loginStatusCode := cERR_NullCredentials;
      loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_NullCredentials),1,loginStatusMessageMaxLength);
      -- 'Username cannot be NULL';
      RETURN FALSE;
	END IF;

	-- Mandatory always. There are successive checks based on this condition
	IF pPassword IS NULL THEN
      slog.warn(pkgCtxId, myUnit, 'Password cannot be NULL');
      loginStatusCode := cERR_NullCredentials;
      loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_NullCredentials),1,loginStatusMessageMaxLength);
	  writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR',
		vclientId => pClient.id, vhost => host, vipaddress => ipaddress,
		vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);
		RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => pClient.id, pIpAddress => ipaddress, pApplicationId => pApplicationId);
      RETURN FALSE;
   END IF;

   IF isUsernameLocked(pUsername, lSinceDate, lUntilDate)  THEN

		IF lUntilDate > vCurrentDate + 1 THEN
			loginStatusCode := cERR_LockedAccount;
			loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_PermLockedAccount),1,loginStatusMessageMaxLength);
		ELSE
			loginStatusCode := cERR_LockedAccount;
			loginStatusMessage := SUBSTR(mlang.trans(langId, loginStatusCode) || ' '||to_char(lUntilDate, 'dd.mm.yyyy hh24:mi'),1,loginStatusMessageMaxLength);
		END IF;

        writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR',
			vclientId => pClient.id, vhost => host, vipaddress => ipaddress,
			vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);
			RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => pClient.id, pIpAddress => ipaddress, pApplicationId => pApplicationId);
        slog.warn(pkgCtxId, myUnit, 'Username "'||pUsername||'" is locked');
        RETURN FALSE;
   END IF;

--  Now check IF user can login
   /* Is client enabled */
   IF pClient.enabled <> 1 THEN
        loginStatusCode := cERR_DisabledAccount;
        loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_DisabledAccount),1,loginStatusMessageMaxLength);

        writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR',
			vclientId => pClient.id, vhost => host, vipaddress => ipaddress,
			vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);
        slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" is disabled');
		RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => pClient.id, pIpAddress => ipaddress, pApplicationId => pApplicationId);
        RETURN FALSE;
    END IF;

   IF pClient.password_enabled <> 1 THEN
        loginStatusCode := cERR_DisabledAccount;
        loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_DisabledAccount),1,loginStatusMessageMaxLength);
        --loginStatusMessage := SUBSTR('User login is disabled';
        writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN',
			vlogstatus=>'ERROR', vclientId => pClient.id, vhost => host, vipaddress => ipaddress,
			vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);
        slog.warn(pkgCtxId, myUnit, 'User password "'||pUsername||'" is disabled');
		RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => pClient.id, pIpAddress => ipaddress, pApplicationId => pApplicationId);
        RETURN FALSE;
    END IF;

   /* Is client expired */
   IF NOT vCurrentDate between NVL(pClient.valid_from, vCurrentDate-1) AND NVL(pClient.valid_to, vCurrentDate+1) THEN
        loginStatusCode := cERR_ExpiredAccount;
        loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_ExpiredAccount),1,loginStatusMessageMaxLength);
        writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN',
			vlogstatus=>'ERROR', vclientId => pClient.id, vhost => host, vipaddress => ipaddress,
			vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);
        slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" account expired');
		RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => pClient.id, pIpAddress => ipaddress, pApplicationId => pApplicationId);
        RETURN FALSE;
    END IF;

	   /* Is password ok */
	IF pNOnce IS NOT NULL THEN

		BEGIN
			checkNOnceDigest(pClientId => pClient.id, pUsername => pUsername, pDigest => pPassword, pPassword => pClient.password,pPasswordCs => pClient.password_cs, ipAddress => ipAddress, host => host, langId => langId, pApplicationId => pApplicationId, pNOnce => pNOnce, pDeviceId => pDeviceId, pClientExtAuthId => pClientExtAuthId, pPasswordHashed => pPasswordHashed, pAppVersionId => pAppVersionId, pOSVersionId => pOSVersionId, pIsFirstLogin => pIsFirstLogin);
		EXCEPTION
			WHEN sspkg.sysexception THEN
				slog.error(pkgCtxId, myUnit, NVL(sspkg.getErrorUserMessage, sspkg.getErrorMessage));
				RETURN FALSE;
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
				RETURN FALSE;
		END;

	ELSE
		slog.debug(pkgCtxId, myUnit, 'Checking password');

		IF NOT verifyClientPassword (
				pClientId => pClient.id,
				pPassword => pPassword,
				pPwdHashed => FALSE,
				pWrite2Log => TRUE, pCheckAndLock => TRUE, pCheckAndSendNotification => TRUE,
				pLogOperationType => 'LOGIN', pLogMessage => 'BASIC_LOGIN',
				pHost => host, pIpAddress => ipaddress, pLangId => langId,
				pApplicationId => pApplicationId,
				pDeviceId => pDeviceId,
				pAppVersionId => pAppVersionId,
				pOSVersionId => pOSVersionId) THEN

			RETURN FALSE;
		END IF;

	END IF;

	IF pClient.password_change_required = 1 THEN
		slog.debug(pkgCtxId, myUnit, 'Password change required');
		pPasswordExpireInDays := 0;
		loginStatusCode := cERR_ExprdPwdChangeAllowed;
		loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_ExprdPwdChangeAllowed),1,loginStatusMessageMaxLength);
		writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR',
			vclientId => pClient.id, vhost => host, vipaddress => ipaddress,
			vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);
		RETURN FALSE;

	ELSE

		-- If > 0, check should be done
		slog.debug(pkgCtxId, myUnit, 'Password valid from '|| pClient.password_valid_from);
		vPasswordWillExpireInDays := passwordExpireIn(pPasswordCreatedAt => TO_DATE(pClient.password_valid_from,common_pck.cDATE_MASK));

		IF vPasswordWillExpireInDays = 0 THEN
			-- Password has expired. Prevent login !
			slog.debug(pkgCtxId, myUnit, 'Password expired');
			pPasswordExpireInDays := 0;

			vPasswordChangeAllowedUntil := TO_DATE(pClient.password_valid_from,common_pck.cDATE_MASK)+vPasswordExpireInDays+vPasswordChangeTimeOut/(24*60);
			slog.debug(pkgCtxId, myUnit, 'vPasswordChangeAllowedUntil ' || TO_CHAR(vPasswordChangeAllowedUntil,'DD.MM.YYYY HH24:MI'));

			-- Ako nema promjene lozinke nakon isteka ili ako je prošao dozvoljeni period za zamjenu
			IF NOT vChangePwdAfterExpired OR vPasswordChangeAllowedUntil < vCurrentDate
			THEN
				slog.debug(pkgCtxId, myUnit, 'Password changed at ' || pClient.password_valid_from ||'. Change allowed until ' || TO_CHAR(vPasswordChangeAllowedUntil,'DD.MM.YYYY HH24:MI'));
				loginStatusCode := cERR_ExpiredPassword;
				loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_ExpiredPassword),1,loginStatusMessageMaxLength);
				disablePassword(pUserId => pClient.id);
			ELSE
				slog.debug(pkgCtxId, myUnit, 'ExpiredPasswordChangeAllowed');
				loginStatusCode := cERR_ExprdPwdChangeAllowed;
				loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_ExprdPwdChangeAllowed),1,loginStatusMessageMaxLength);
			END IF;

			writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR',
				vclientId => pClient.id, vhost => host, vipaddress => ipaddress,
				vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);

			slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" password expired');
			RETURN FALSE;

		ELSE
			BEGIN
				-- If NULL (not set), treat as reauthorized
				vDeviceReauthorized := NVL(auth.getExtAuthAttributeValue (pClientId => pClient.id, pDeviceId => pDeviceId, pApplicationId => pApplicationId, pAttribute => cDevicePasswordAuthorizedPHX), cDevicePasswordAuthorized);
			EXCEPTION
				WHEN sspkg.sysexception THEN
					IF sspkg.getErrorCode = cERR_ExtAuthProblem THEN
						vDeviceReauthorized := cDevicePasswordAuthorized;
					ELSE
						RAISE;
					END IF;
			END;

			IF vDeviceReauthorized <> cDevicePasswordAuthorized THEN
				slog.debug(pkgCtxId, myUnit, 'Device require password reauthorization');

				loginStatusCode := cERR_RqrPasswordAuthentication;
				loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_RqrPasswordAuthentication),1,loginStatusMessageMaxLength);
				writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR',
					vclientId => pClient.id, vhost => host, vipaddress => ipaddress,
					vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);

			ELSE

				IF vPasswordWillExpireInDays > 0  THEN

					slog.debug(pkgCtxId, myUnit, 'vPasswordWillExpireInDays > 0');
					IF vPasswordWillExpireInDays <= vPasswordExpirationWarning THEN
						-- Already in grace period
						slog.debug(pkgCtxId, myUnit, 'vPasswordWillExpireInDays <= vPasswordExpirationWarning');
						pPasswordExpireInDays := vPasswordWillExpireInDays;

						loginStatusCode := cERR_PasswordExpireInDays;
						loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_PasswordExpireInDays, vPasswordWillExpireInDays),1,loginStatusMessageMaxLength);

						writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR',
							vclientId => pClient.id, vhost => host, vipaddress => ipaddress,
							vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);

						slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" password will expire in ' || vPasswordWillExpireInDays ||' days!');
					END IF;
				END IF;
			END IF;
		END IF;
	END IF;

	pPasswordExpireInDays := -1;


	RETURN TRUE;

  END basicAccountCheck;

  FUNCTION basicLoginWithoutExtAuth(pUsername VARCHAR2, pPassword VARCHAR2,
	ipAddress VARCHAR2, host VARCHAR2, sessLogLevel INTEGER, langId VARCHAR2,
	pApplicationId VARCHAR2, pInstallationId VARCHAR2, pDeviceId VARCHAR2, pClient vClientType,
	pSessionId out VARCHAR2, pPasswordExpireInDays OUT PLS_INTEGER, pNOnce IN VARCHAR2, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL, pIsFirstLogin IN BOOLEAN DEFAULT FALSE)
  RETURN BOOLEAN
  IS
	myunit CONSTANT VARCHAR2(30) := 'basicLoginWithoutExtAuth';
	vSessionId VARCHAR2(4000);
	vPwdExpireInDays PLS_INTEGER;
  BEGIN
	slog.debug(pkgCtxId, myUnit);

	IF basicAccountCheck(
			pUsername =>pUsername,
			pPassword =>pPassword,
			ipAddress =>ipAddress,
			host =>host,
			sessLogLevel =>sessLogLevel,
			langId =>langId,
			pApplicationId =>pApplicationId,
			pInstallationId =>pInstallationId,
			pClient => pClient,
			pPasswordExpireInDays => vPwdExpireInDays,
			pNOnce => pNOnce,
			pDeviceId => pDeviceId,
			pClientExtAuthId => NULL,
			pPasswordHashed => pPasswordHashed,
			pAppVersionId => pAppVersionId,
			pOSVersionId => pOSVersionId,
			pIsFirstLogin => pIsFirstLogin) OR (loginStatusCode = cERR_ExprdPwdChangeAllowed)

	THEN

		IF (sspkg.readBool(pkgCtxId || '/preventMultipleSessionsFor_' || pApplicationId)) THEN
			slog.debug(pkgCtxId, myUnit, 'Try to delete all previous sessions for user ' || pUsername || ' and application ID: ' || pApplicationId || ' and installation ID:' || pInstallationId);
			mcsm.expireSession(pApplicationId => pApplicationId, pUserId => pClient.id);
		END IF;

		pPasswordExpireInDays := vPwdExpireInDays;
		slog.debug(pkgCtxId, myUnit, 'Try to create session for user ' || pUsername || ' FROM client ' || ipAddress || ' for installation ' || pInstallationId || ' and device ' || pDeviceId);
		slog.debug(pkgCtxId, myUnit, 'basicAccountCheck() succeeded! Password will expire in ' || vPwdExpireInDays);
		vSessionId := mcsm.creates(pUsername, ipAddress, host, sessLogLevel=>sessLogLevel, langId=>NVL(langId, dfltLang),
                    application_id => pApplicationId, installation_id => pInstallationId, device_id => pDeviceId);

		IF vSessionId IS NOT NULL THEN
			slog.debug(pkgCtxId, myUnit, 'User ' || pUsername || ' has successfully established session with id : ' || vSessionId);

			-- We need a session to be able to change password in following step
			pSessionId := vSessionId;

			IF loginStatusCode IS NOT NULL THEN
				RETURN loginStatusCode <> cERR_ExprdPwdChangeAllowed;
			ELSE
				RETURN TRUE;
			END IF;
		ELSE
			sspkg.raiseError(cERR_InternalError, 'Internal error: Basic account check succeeded, but session was not established!', pkgCtxId, myunit);
		END IF;
    END IF;

	pPasswordExpireInDays := vPwdExpireInDays;
	slog.debug(pkgCtxId, myUnit, 'Password will expire in ' || vPwdExpireInDays);

	RETURN FALSE;
  END basicLoginWithoutExtAuth;

   FUNCTION basicLogin(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2,
    host VARCHAR2, certificate VARCHAR2, otpType out VARCHAR2,
    sessionId out VARCHAR2, sessLogLevel INTEGER := NULL,
    langId VARCHAR2 := NULL,  pApplicationId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL, pDeviceId VARCHAR2 := NULL,
	pPasswordExpireInDays OUT PLS_INTEGER, pNOnce IN VARCHAR2 DEFAULT NULL, pUserId OUT PLS_INTEGER, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL, pIsFirstLogin IN BOOLEAN DEFAULT FALSE)
  RETURN BOOLEAN AS
    myunit CONSTANT VARCHAR2(30) := 'basicLogin';

    ires INTEGER;

    CURSOR cclient is
	SELECT rowid, id, enabled, password, password_enabled, valid_from, valid_to, NULL, NULL, NULL, ph4, password_change_required, password_cs
	FROM client
	WHERE lower(username) = lower(vusername)
	AND vusername is NOT NULL;

	vclient vClientType;

	vExtAuth app_extauth.extauth_id%TYPE;
	vBasicLoginResult BOOLEAN := TRUE;
	vSessionId VARCHAR2(4000);
	vDeviceId app_extauth.dev_id%TYPE;

	loginMethod varchar2(20);
	vPwdExpireInDays PLS_INTEGER;
    
    l_sqlcode  NUMBER;
  	l_sqlerrm  VARCHAR2(4000);

  BEGIN
   slog.debug(pkgCtxId, myUnit, vusername || ':' || ipAddress ||':' || sessLogLevel || ':' || langId || ':' || pApplicationId || ':' || pInstallationId ||':'||pDeviceId);

   deviceAuthorizationStatus := NULL;
   loginStatusCode := NULL;
   loginStatusMessage := NULL;

   quietCloseS;

-- Fetch client
   OPEN cclient;
   FETCH cclient INTO vclient;
   IF cclient%NOTFOUND THEN
      slog.error(pkgCtxId, myUnit, 'User "'||vusername||'" does NOT exist');
      loginStatusCode := cERR_WrongCredentials;
      loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_WrongCredentials),1,loginStatusMessageMaxLength);
      CLOSE cclient;
      RETURN FALSE;
   END IF;
   CLOSE cclient;

   pUserId := vclient.id;

   -- password policy - adding indicator to ph6 column for sending password hashed from mobilne device

    IF pDeviceId IS NOT NULL THEN
		IF pPasswordHashed IS NOT NULL THEN
			slog.debug(pkgCtxId, myUnit, 'pPasswordHashed is not null');
			setDevicePwdHashStatus(vclient.id, pDeviceId, cMobileDeviceWithPwdHashed);
		ELSE
			slog.debug(pkgCtxId, myUnit, 'pPasswordHashed is null');
			setDevicePwdHashStatus(vclient.id, pDeviceId, cMobileDeviceWithOutPwdHashed);
		END IF;
	END IF;

   writeClientLog(logOpeType => 'LOGIN', message => 'Login attempt', vlogstatus=>'OK',
			vclientId => vclient.id, vhost => host, vipaddress => ipaddress,
			vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);

   BEGIN
	checkLicense(pApplicationId => pApplicationId, pClientId => vclient.id);
   EXCEPTION
    WHEN sspkg.sysexception THEN
		loginStatusCode := sspkg.getErrorCode;
		loginStatusMessage := SUBSTR(mlang.trans(langId, loginStatusCode),1,loginStatusMessageMaxLength);

        writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR',
			vclientId => vclient.id, vhost => host, vipaddress => ipaddress,
			vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);
			RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => vclient.id, pIpAddress => ipaddress, pApplicationId => pApplicationId);
		RETURN FALSE;
   END;

   -- Will raise a common_pck.cERR_InvalidApplication exception if an invalid application was provided
   BEGIN
	common_pck.CheckApplication(pApplicationId => pApplicationId);
   EXCEPTION
    WHEN sspkg.sysexception THEN
		loginStatusCode := sspkg.getErrorCode;
		loginStatusMessage := SUBSTR(mlang.trans(langId, loginStatusCode),1,loginStatusMessageMaxLength);

        writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR',
			vclientId => vclient.id, vhost => host, vipaddress => ipaddress,
			vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);
		RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => vclient.id, pIpAddress => ipaddress, pApplicationId => pApplicationId);
		RETURN FALSE;
   END;

   vDeviceId := pDeviceId;

   vBasicLoginResult := basicLoginWithoutExtAuth(
		pUsername =>vusername,
		pPassword =>vpassword,
		ipAddress =>ipAddress,
		host =>host,
		sessLogLevel =>sessLogLevel,
		langId =>langId,
		pApplicationId =>pApplicationId,
		pInstallationId =>pInstallationId,
		pDeviceId =>vDeviceId,
		pClient =>vclient,
		pSessionId =>vSessionId,
		pPasswordExpireInDays => vPwdExpireInDays,
		pNOnce => pNOnce,
		pPasswordHashed => pPasswordHashed,
		pAppVersionId => pAppVersionId,
		pOSVersionId => pOSVersionId,
		pIsFirstLogin => pIsFirstLogin);

	pPasswordExpireInDays := vPwdExpireInDays;

    slog.debug(pkgCtxId, myUnit, 'basicLoginWithoutExtAuth RET: ' || bool2char(vBasicLoginResult) || ':' || loginStatusCode || ':' || vPwdExpireInDays);

	sessionId := vSessionId;

	slog.debug(pkgCtxId, myUnit, 'SessionId:' || SUBSTR(vSessionId,1,30));

    IF vSessionId IS NOT NULL THEN
		/* From this moment on, data FROM app_extauth is required*/
		vExtAuth := clientExtAuthID(pUsername =>lower(vusername), pDeviceId => vDeviceId, pApplicationId => pApplicationId);
		slog.debug(pkgCtxId, myUnit, 'Determine ExtAuth ' || vExtAuth);
		setAppExtAuthId(vExtAuth);
		IF pDeviceId IS NULL THEN
			setDeviceId(pDeviceId=>vDeviceId);
		END IF;

		setAppVersionId(pAppVersionId);
		setOSVersionId(pOSVersionId);

	END IF;

	IF NOT vBasicLoginResult AND (loginStatusCode <> cERR_ExprdPwdChangeAllowed) THEN
        slog.debug(pkgCtxId, myUnit, 'basicLoginWithoutExtAuth returned FALSE and expired password cannot be changed!');
		RETURN FALSE;
	END IF;

	IF vBasicLoginResult AND pIsFirstLogin THEN
       slog.debug(pkgCtxId, myUnit, 'basicLoginWithoutExtAuth returned TRUE but it is first login and we raise unauthorized device for secure'); --#25993
	   sspkg.raiseError(cERR_UnauthorizedDevice, null, pkgCtxId, myUnit);
	END IF;

   IF NOT NVL(sspkg.readBool(extAuthCtx||'/'||vExtAuth||'/Enabled'), FALSE) THEN
    loginStatusCode := cERR_DisabledExtAuth;
    loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_DisabledExtAuth),1,loginStatusMessageMaxLength);
    writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR', vclientid => vclient.id, vhost => host, vipaddress => ipaddress, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);

    slog.error(pkgCtxId, myUnit, 'Authentication type "'||vExtAuth||'" is disabled');
	RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => vclient.id, pIpAddress => ipaddress, pApplicationId => getApplicationId());
    RETURN FALSE;
   END IF;

	IF vExtAuth in (common_pck.cEA_mToken_OTP, common_pck.cEA_mToken_CHRESP, common_pck.cEA_mToken_CHLoginOTPsign) THEN

		IF NOT hasLicense(	pApplicationId => common_pck.cAPP_MTOKEN, pClientId => vclient.id) THEN
			loginStatusCode := cERR_ApplicationLicenseMissing;
			loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_ApplicationLicenseMissing),1,loginStatusMessageMaxLength);
			writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR', vclientid => vclient.id, vhost => host, vipaddress => ipaddress, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
			RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => vclient.id, pIpAddress => ipaddress, pApplicationId => getApplicationId());
			slog.error(pkgCtxId, myUnit, 'The client does not have an application license!');
			RETURN FALSE;
		END IF;

    END IF;

   -- TODO: Kreirati novu internu preopterecenu varijantu ove funkcije, kojoj ce se proslijedivati ExtAuthId
   IF clientRequirePKILogin(vusername=>vusername, pDeviceId=>vDeviceId, pApplicationId=>pApplicationId, pClientExtAuthId => vExtAuth) THEN
      IF certificate is NULL THEN
        loginStatusCode := cERR_NoCertificate;
        loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_NoCertificate),1,loginStatusMessageMaxLength);
        writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR', vclientid => vclient.id, vhost => host, vipaddress => ipaddress, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>pApplicationId);

        slog.warn(pkgCtxId, myUnit, 'No certificate provided for "'||vusername||'"');
		RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => vclient.id, pIpAddress => ipaddress, pApplicationId => pApplicationId);
        RETURN FALSE;
      ELSE
       dynsql := sspkg.readVChar(extAuthCtx||'/'||vExtAuth||'/CheckCertificateApi');
       CASE
        WHEN dynsql is NOT NULL THEN
          EXECUTE IMMEDIATE dynsql USING OUT ires, getCID4User(vusername), certificate;
          IF ires = 1 THEN
            NULL;
          ELSE /* Null or anything ELSE - that is error */
            loginStatusCode := cERR_WrongCertificate;
            loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_WrongCertificate),1,loginStatusMessageMaxLength);
            writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR', vclientid => vclient.id, vhost => host, vipaddress => ipaddress, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>pApplicationId);

            slog.warn(pkgCtxId, myUnit, 'User "'||vusername||'" has wrong certificate');
			RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => vclient.id, pIpAddress => ipaddress, pApplicationId => pApplicationId);
            RETURN FALSE;
          END IF;
        ELSE
          loginStatusCode := cERR_ExtAuthProblem;
          loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_ExtAuthProblem),1,loginStatusMessageMaxLength);
          writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR', vclientid => vclient.id, vhost => host, vipaddress => ipaddress, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>pApplicationId);

          slog.error(pkgCtxId, myUnit, 'There is no check_certificate api for extauth_method');
		  RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => vclient.id, pIpAddress => ipaddress, pApplicationId => pApplicationId);
          RETURN FALSE;
       END CASE;
      END IF;
   END IF;

	-- TODO: Kreirati novu internu preopterecenu varijantu ove funkcije, kojoj ce se proslijedivati ExtAuthId
   loginMethod := clientLoginMethod(vusername, vDeviceId, pApplicationId, pClientExtAuthId => vExtAuth);
   otpType := clientOTPType(vusername, vDeviceId, pApplicationId, pClientExtAuthId => vExtAuth);

   -- ovo je način dijeljena vrste događaja između modula
   setEvent (common_pck.cAUTHEVT_LOGIN);

   CASE NVL(loginMethod, common_pck.cOTPTYPE_NULL)
    WHEN 'NULL' THEN
		-- This session is created. User can query own data. If loginMethod is != 'NULL' THEN extendedLogin should set _USERID
        setLogged(vclient.id);
		otpType := common_pck.cOTPTYPE_NULL;  -- ??? Trebalo bi da već sadrži tu istu vrijednost

        slog.info(pkgCtxId, myUnit, 'Login succeeded');
	WHEN common_pck.cOTPTYPE_OTP THEN
		CASE otpType
			WHEN common_pck.cOTPTYPE_SENDOTP THEN
        		IF vBasicLoginResult THEN
					dynsql := sspkg.readVChar(extAuthCtx||'/'||vExtAuth||'/SendOtpApi');
          			slog.debug(pkgCtxId, myUnit, 'Retrieved SendOtpApi');
					IF dynsql is NOT NULL THEN
            			slog.debug(pkgCtxId, myUnit, 'Execute SendOtpApi');
						EXECUTE IMMEDIATE dynSQL using getCID4User(getSUser);
					ELSE
						loginStatusCode := cERR_ExtAuthProblem;
						loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_ExtAuthProblem),1,loginStatusMessageMaxLength);
						writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR', vclientid => vclient.id, vhost => host, vipaddress => ipaddress, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>pApplicationId);
						RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => vclient.id, pIpAddress => ipaddress, pApplicationId => pApplicationId);
						slog.error(pkgCtxId, myUnit, 'There is no SendOtpApi for ExtAuth "'||vExtAuth||'"');
						RETURN FALSE;
					END IF;
        		ELSE
          			slog.info(pkgCtxId, myUnit, 'BasicAccount check due expired password failed. Will not send OTP until requested!');
        		END IF;
			WHEN common_pck.cOTPTYPE_GENOTP THEN
				slog.info(pkgCtxId, myUnit, 'Login (1/2) succeeded, need extended login');
			WHEN common_pck.cOTPTYPE_CHRESP THEN
				slog.info(pkgCtxId, myUnit, 'Login (1/2) succeeded, need extended login');
			WHEN common_pck.cOTPTYPE_CHRESPPIN THEN
				slog.info(pkgCtxId, myUnit, 'Login (1/2) succeeded, need extended login');
			ELSE
				loginStatusCode := cERR_ExtAuthProblem;
				loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_ExtAuthProblem),1,loginStatusMessageMaxLength);
				writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR', vclientid => vclient.id, vhost => host, vipaddress => ipaddress, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>pApplicationId);
				RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => vclient.id, pIpAddress => ipaddress, pApplicationId => pApplicationId);
				slog.error(pkgCtxId, myUnit, 'Unsupported OtpType "'||otpType||'"');
				RETURN FALSE;
		END CASE;
	ELSE
		loginStatusCode := cERR_ExtAuthProblem;
		loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_ExtAuthProblem),1,loginStatusMessageMaxLength);
		writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR', vclientid => vclient.id, vhost => host, vipaddress => ipaddress, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>pApplicationId);
		RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => vclient.id, pIpAddress => ipaddress, pApplicationId => pApplicationId);
		slog.error(pkgCtxId, myUnit, 'Unsupported OtpType "'||otpType||'"');
		RETURN FALSE;
	END CASE;

	writeClientLog('LOGIN', 'BASIC_LOGIN', vlogstatus=>'OK',
			vclientId => vclient.id, vhost => host, vipaddress => ipaddress,
			vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>pApplicationId);

	closes;

	slog.debug(pkgCtxId, myUnit, 'END-SessionId:' || sessionId);

	IF loginStatusCode IS NOT NULL THEN
		RETURN loginStatusCode <> cERR_ExprdPwdChangeAllowed;
	ELSE
		RETURN TRUE;
	END IF;

   EXCEPTION
    WHEN sspkg.sysException THEN
		slog.debug(pkgCtxId, myUnit, 'Catch exception ' || sspkg.getErrorCode);
		IF (sspkg.getErrorCode = cERR_ExtAuthProblem) THEN
			loginStatusCode := cERR_ExtAuthProblem;
			loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_ExtAuthProblem),1,loginStatusMessageMaxLength);
			quietDestroyS;
			RETURN FALSE;
		ELSIF (sspkg.getErrorCode = cERR_UnauthorizedDevice) THEN
			loginStatusCode := cERR_UnauthorizedDevice;
			loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_UnauthorizedDevice),1,loginStatusMessageMaxLength);
			quietDestroyS;
            slog.debug(pkgCtxId, myUnit, 'Session destroyed. Raising ' || sspkg.getErrorCode);

            sspkg.raiseError(cERR_UnauthorizedDevice, null, pkgCtxId, myUnit);

		ELSE
			loginStatusCode := cERR_InternalError;
			loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_InternalError) || ' : ' ||sspkg.getErrorCode,1,loginStatusMessageMaxLength);
			quietDestroyS;
			RETURN FALSE;
		END IF;
    WHEN OTHERS THEN
	  l_sqlcode := SQLCODE;
  	  l_sqlerrm := SQLERRM;
      loginStatusCode := cERR_InternalError;
      loginStatusMessage := SUBSTR(mlang.trans(getLang, cERR_InternalError),1,loginStatusMessageMaxLength);
	  slog.error(pkgCtxId, myUnit, l_sqlcode||'-'||l_sqlerrm);
      quietDestroyS;
      RETURN FALSE;
  END basicLogin;
  
  FUNCTION extendedLogin(sessionId VARCHAR2, otp VARCHAR2, challenge VARCHAR2 := NULL) RETURN BOOLEAN AS
    myunit CONSTANT VARCHAR2(30) := 'extendedLogin';
    ires INTEGER;
    otpType VARCHAR2(200);

    lSinceDate DATE;
    lUntilDate DATE;
	vExtAuth app_extauth.extauth_id%TYPE;
	l_sqlcode  NUMBER;
  	l_sqlerrm  VARCHAR2(4000);
    
  BEGIN
   slog.debug(pkgCtxId, myUnit, 'OTP:CH=' || otp ||':' || challenge);
   loginStatusCode := NULL;
   loginStatusMessage := NULL;

   -- #9740
   opens(sessionId);

   IF otp is NULL THEN
      slog.warn(pkgCtxId, myUnit, 'OTP cannot be NULL');
      loginStatusCode := cERR_NullCredentials;
      loginStatusMessage := SUBSTR(mlang.trans(getLang, cERR_NullCredentials),1,loginStatusMessageMaxLength);
      writeClientLog('LOGIN', 'EXTENDED_LOGIN', vlogstatus=>'ERROR', vclientid => getCID4User(getSUser), vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
	  RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => getCID4User(getSUser), pIpAddress => getIPAddress(), pApplicationId => getApplicationId());
      RETURN FALSE;
   END IF;

   IF isUsernameLocked(getSUser, lSinceDate, lUntilDate)  THEN
        loginStatusCode := cERR_LockedAccount;
        loginStatusMessage := SUBSTR(mlang.trans(getLang, cERR_LockedAccount) || ' ' || to_char(lUntilDate, 'dd.mm.yyyy hh24:mi'),1,loginStatusMessageMaxLength);
        writeClientLog('LOGIN', 'EXTENDED_LOGIN', vlogstatus=>'ERROR', vclientid => getCID4User(getSUser), vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
        slog.warn(pkgCtxId, myUnit, 'Username "'||getSUser||'" is locked');
		RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => getCID4User(getSUser), pIpAddress => getIPAddress(), pApplicationId => getApplicationId());
        RETURN FALSE;
   END IF;

   /* Test logic */
   vExtAuth := clientExtAuthID();
   otpType := clientOTPType(vExtAuth);

   setEvent (common_pck.cAUTHEVT_LOGIN);

   slog.debug(pkgCtxId, myUnit, 'EA:OT:' || vExtAuth || ':' || otpType);

   IF otpType is NULL THEN
     loginStatusCode := cERR_WrongOTPType;
     loginStatusMessage := SUBSTR(mlang.trans(getLang, cERR_WrongOTPType),1,loginStatusMessageMaxLength);
     writeClientLog('LOGIN', 'EXTENDED_LOGIN', vlogstatus=>'ERROR', vclientid => getCID4User(getSUser), vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
     quietDestroyS;
	 RegisterSplunkNotification(pActionId => common_pck.cFailedLogin,  pClientId => getCID4User(getSUser), pIpAddress => getIPAddress(), pApplicationId => getApplicationId());
     RETURN FALSE;
   END IF;

   IF otpType IN (common_pck.cOTPTYPE_CHRESP, common_pck.cOTPTYPE_CHRESPPIN) THEN
    dynsql := sspkg.readVChar(extAuthCtx||'/'||vExtAuth||'/CheckChRespApi');
   ELSE
    dynsql := sspkg.readVChar(extAuthCtx||'/'||vExtAuth||'/CheckOtpApi');
   END IF;
	slog.debug(pkgCtxId, myUnit, 'DynSQL:' || dynsql);
   IF dynsql is NOT NULL THEN
    IF otpType IN (common_pck.cOTPTYPE_CHRESP, common_pck.cOTPTYPE_CHRESPPIN) THEN
        EXECUTE IMMEDIATE dynSQL USING OUT ires, getCID4User(getSUser), challenge, otp;
    ELSE
        EXECUTE IMMEDIATE dynSQL USING OUT ires, getCID4User(getSUser), otp;
    END IF;
    IF ires = 1 THEN
      NULL;
    ELSE
	  slog.error(pkgCtxId, myUnit, 'Wrong OTP');
      loginStatusCode := cERR_WrongCredentials;
      loginStatusMessage := SUBSTR(mlang.trans(getLang, cERR_WrongCredentials),1,loginStatusMessageMaxLength);
      writeClientLog('LOGIN', 'EXTENDED_LOGIN', vlogstatus=>'ERROR', vclientid => getCID4User(getSUser), vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
      checkAndLock(getCID4User(getSUser), 'LOGIN');
      checkAndSendNotification(getCID4User(getSUser));
	  RegisterSplunkNotification(pActionId => common_pck.cFailedLogin,  pClientId => getCID4User(getSUser), pIpAddress => getIPAddress(), pApplicationId => getApplicationId());
      RETURN FALSE;
    END IF;
   ELSE
     slog.error(pkgCtxId, myUnit, 'Internal error: /Core/Auth/err/ExtAuthProblem');
     loginStatusCode := cERR_ExtAuthProblem;
     loginStatusMessage := SUBSTR(mlang.trans(getLang, cERR_ExtAuthProblem),1,loginStatusMessageMaxLength);
     writeClientLog('LOGIN', 'EXTENDED_LOGIN', vlogstatus=>'ERROR', vclientid => getCID4User(getSUser), vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
     quietDestroyS;
	 RegisterSplunkNotification(pActionId => common_pck.cFailedLogin,  pClientId => getCID4User(getSUser), pIpAddress => getIPAddress(), pApplicationId => getApplicationId());
     RETURN FALSE;
   END IF;

   setLogged(getCID4User(getSUser));
   slog.info(pkgCtxId, myUnit, 'Login (2/2) succeeded');
   closes;
   RETURN TRUE;

   EXCEPTION
    WHEN sspkg.sysException THEN
      loginStatusCode := cERR_InternalError;
      loginStatusMessage := SUBSTR(mlang.trans(getLang, cERR_InternalError) || ' : ' || sspkg.getErrorCode,1,loginStatusMessageMaxLength);
      quietDestroyS;
      RETURN FALSE;
    WHEN OTHERS THEN
      l_sqlcode := SQLCODE;
  	  l_sqlerrm := SQLERRM;
      loginStatusCode := cERR_InternalError;
      loginStatusMessage := SUBSTR(mlang.trans(getLang, cERR_InternalError),1,loginStatusMessageMaxLength);
	  slog.error(pkgCtxId, myUnit, l_sqlcode||'-'||l_sqlerrm);
      quietDestroyS;
      RETURN FALSE;
  END extendedLogin;

  PROCEDURE opens(sessionId VARCHAR2) AS
    myunit CONSTANT VARCHAR2(30) := 'opens';
  BEGIN
    slog.debug(pkgCtxId, myUnit, sessionId);
    mcsm.opens(sessionId);

   EXCEPTION
    WHEN sspkg.sysException THEN
      quietCloseS;
      RAISE;
    WHEN OTHERS THEN
      quietCloseS;
      sspkg.raiseOraError(pkgCtxId, myUnit);
      RAISE;
  END opens;

  PROCEDURE closes AS
    myunit CONSTANT VARCHAR2(30) := 'closes';
  BEGIN
   slog.debug(pkgCtxId, myUnit);
   mcsm.closes;

   EXCEPTION
    WHEN sspkg.sysException THEN
      slog.error(pkgCtxId, myUnit, 'Unhook session failed!!!');
      RAISE;
    WHEN OTHERS THEN
      slog.error(pkgCtxId, myUnit, 'Unhook session failed!!!');
      sspkg.raiseOraError(pkgCtxId, myUnit);
      RAISE;
  END closes;

  PROCEDURE logout AS
    myunit CONSTANT VARCHAR2(30) := 'logout';
  BEGIN
   slog.info(pkgCtxId, myUnit);

   writeClientLog('LOGOUT', 'LOGOUT', vclientid => getSCID);
   mcsm.destroys;

   EXCEPTION
    WHEN sspkg.sysException THEN
      slog.error(pkgCtxId, myUnit, 'Logout failed!!!');
      RAISE;
    WHEN OTHERS THEN
      slog.error(pkgCtxId, myUnit, 'Logout failed!!!');
      sspkg.raiseOraError(pkgCtxId, myUnit);
      RAISE;
  END logout;

  FUNCTION resetPasswordRQ(contactClient VARCHAR2, secQuestionID VARCHAR2, secQuestionAnswer VARCHAR2) RETURN BOOLEAN AS
    myunit CONSTANT VARCHAR2(30) := 'resetPasswordRQ';
    res BOOLEAN;
  BEGIN
    slog.debug(pkgCtxId, myUnit, contactClient || ':' || secQuestionID || ':' || secQuestionAnswer);

   /* Test logic */
    res := TRUE;
    RETURN res;
   /* End Test logic */

   EXCEPTION
    WHEN sspkg.sysException THEN
      RAISE;
    WHEN OTHERS THEN
      sspkg.raiseOraError(pkgCtxId, myUnit);
      RAISE;
  END resetPasswordRQ;

  FUNCTION isPasswordAlreadyUsed(pUserId client.id%TYPE, pNewPassword VARCHAR2)
  RETURN PLS_INTEGER IS
	myunit CONSTANT VARCHAR2(30) := 'isPasswordAlreadyUsed';

    CURSOR c IS
	SELECT DISTINCT password FROM
	(SELECT password, SYSDATE date_changed
	FROM client
	WHERE id = pUserId
	UNION
    SELECT password, MIN(jn_datetime) date_changed
	FROM client_jn c
	WHERE id = pUserId
	GROUP BY password
	ORDER BY date_changed DESC)
	WHERE ROWNUM <= vPasswordMaxRetentionNo;

	TYPE vCursorTT IS TABLE OF client.password%TYPE INDEX BY PLS_INTEGER;
	vCursorTable vCursorTT;
  BEGIN
	slog.debug(pkgCtxId, myUnit, pUserId || ':' || vPasswordMaxRetentionNo || ':' || pNewPassword);

	IF vPasswordMaxRetentionNo = 0 THEN
		slog.debug(pkgCtxId, myUnit, 'Verification disabled!');
		RETURN 0;
	END IF;

	OPEN c;
	FETCH c BULK COLLECT INTO vCursorTable;
	slog.debug(pkgCtxId, myUnit, 'Query returned ' || vCursorTable.COUNT || ' rows');

	IF vCursorTable.COUNT = 0 THEN
		CLOSE c;
		slog.debug(pkgCtxId, myUnit, 'Query found no data!');
		RETURN 0;
	END IF;
	CLOSE c;

	slog.debug(pkgCtxId, myUnit, 'N:' || hash(pNewPassword));
	slog.debug(pkgCtxId, myUnit, 'Y:' || hash(upper(pNewPassword)));

	FOR i IN 1..vCursorTable.COUNT LOOP
		slog.debug(pkgCtxId, myUnit, 'C:'||i||':'||vCursorTable(i));
		IF NVL(vCursorTable(i), 'NULL') IN (hash(pNewPassword), hash(upper(pNewPassword))) THEN
			slog.debug(pkgCtxId, myUnit, 'Already used at pos ' || i);
			RETURN 1;
		END IF;
	END LOOP;

	slog.debug(pkgCtxId, myUnit, 'Not used previously');
	RETURN 0;
  END isPasswordAlreadyUsed;

  FUNCTION isPasswordAlreadyUsed(pNewPassword VARCHAR2)
  RETURN PLS_INTEGER IS
	myunit CONSTANT VARCHAR2(30) := 'isPasswordAlreadyUsed2';
  BEGIN
		slog.debug(pkgCtxId, myUnit);
		RETURN isPasswordAlreadyUsed(pUserId => getClientId(), pNewPassword => pNewPassword);
  END isPasswordAlreadyUsed;

  FUNCTION pwd_complexity_verify_function(pPassword IN VARCHAR2, digit OUT BOOLEAN, lowchar OUT BOOLEAN, upchar OUT BOOLEAN, punct OUT BOOLEAN)
  RETURN BOOLEAN IS
  	m PLS_INTEGER;
  	condsatisied PLS_INTEGER := 0;

	myunit CONSTANT VARCHAR2(30) := 'pwd_complexity_verify_function';

  BEGIN
	slog.debug(pkgCtxId, myUnit, pPassword);

	digit:=FALSE;
	lowchar:=FALSE;
	upchar:=FALSE;
	punct:=FALSE;

  -- Provjera da li lozinka sadrži najmanje 1 slovo, 1 cifru i 1 interpunkcijski znak)
  IF condsatisied = satisfyMin THEN
  	RETURN TRUE;
  END IF;

  -- 1. (da li sadrži cifre)
  slog.debug(pkgCtxId, myUnit, 'Digit array :' || digitarray);
  m := length(pPassword);
  FOR i IN 1..length(digitarray) LOOP
  	FOR j IN 1..m LOOP
  		IF substr(pPassword,j,1) = substr(digitarray,i,1) THEN
  			digit:=TRUE; condsatisied := condsatisied + 1; EXIT;
  		END IF;
  	END LOOP;

	EXIT WHEN digit;
  	END LOOP;

  IF condsatisied = satisfyMin THEN
  	RETURN TRUE;
  END IF;

  -- 2. (da li sadrži malo slovo)
  slog.debug(pkgCtxId, myUnit, 'Lowchar array :' || lowchararray);
  FOR i IN 1..length(lowchararray) LOOP
  	FOR j IN 1..m LOOP
  		IF substr(pPassword,j,1) = substr(lowchararray,i,1) THEN
  			lowchar:=TRUE; condsatisied := condsatisied + 1; EXIT;
  		END IF;
  	END LOOP;
  	EXIT WHEN lowchar;
  END LOOP;

  IF condsatisied = satisfyMin THEN
  	RETURN TRUE;
  END IF;

  -- 3. (da li sadrži veliko slovo)
  slog.debug(pkgCtxId, myUnit, 'Upchar array :' || upchararray);
  FOR i IN 1..length(upchararray) LOOP
  	FOR j IN 1..m LOOP
  		IF substr(pPassword,j,1) = substr(upchararray,i,1) THEN
  			upchar:=TRUE; condsatisied := condsatisied + 1; EXIT;
  		END IF;
  	END LOOP;
  	EXIT WHEN upchar;
  END LOOP;

  IF condsatisied = satisfyMin THEN
  	RETURN TRUE;
  END IF;

  -- 4. (da li sadrži interpunkcijski znak)
  slog.debug(pkgCtxId, myUnit, 'Punct. array :' || punctarray);
  FOR i IN 1..length(punctarray) LOOP
  	FOR j IN 1..m LOOP
  		IF substr(pPassword,j,1) = substr(punctarray,i,1) THEN
  			punct:=TRUE; condsatisied := condsatisied+1; EXIT;
  		END IF;
  	END LOOP;
  	EXIT WHEN punct;
  END LOOP;

  IF condsatisied < satisfyMin THEN
  	RETURN FALSE;
  END IF;

  RETURN TRUE;
  END pwd_complexity_verify_function;

  FUNCTION checkArePwdCharactersAllowed(pNewPassword IN VARCHAR2)
  RETURN BOOLEAN IS
	myunit CONSTANT VARCHAR2(30) := 'checkArePwdCharactersAllowed';
	m PLS_INTEGER;
  BEGIN
	slog.debug(pkgCtxId, myUnit, pNewPassword);
    m := length(pNewPassword);

	FOR j IN 1..m LOOP
		IF  INSTR(digitarray, substr(pNewPassword,j,1)) = 0 AND
			INSTR(lowchararray, substr(pNewPassword,j,1)) = 0 AND
			INSTR(upchararray, substr(pNewPassword,j,1)) = 0 AND
			INSTR(punctarray, substr(pNewPassword,j,1)) = 0
		THEN
			slog.debug(pkgCtxId, myUnit, 'Inavlid character : ' || substr(pNewPassword,j,1));
			RETURN FALSE;
		END IF;
	END LOOP;

	RETURN TRUE;
  END checkArePwdCharactersAllowed;

	FUNCTION checkPwdMinLength(pUsername VARCHAR2, pNewPassword VARCHAR2)
	RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(30) := 'checkPwdMinLength';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pUsername);
	    IF LENGTH(pNewPassword) < vPasswordMinLength THEN
			loginStatusCode := cERR_ShortPassword;
			checkAndLock(getCID4User(pUsername), 'CHPWD');
			slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" to short password specified');
			RETURN FALSE;
		END IF;
		RETURN TRUE;
	END checkPwdMinLength;

	FUNCTION checkPwdMaxLength(pUsername VARCHAR2, pNewPassword VARCHAR2)
	RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(30) := 'checkPwdMaxLength';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pUsername);
		IF LENGTH(pNewPassword) > vPasswordMaxLength THEN
			loginStatusCode := cERR_passwordTooLong;
			checkAndLock(getCID4User(pUsername), 'CHPWD');
			slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" to long password specified');
			RETURN FALSE;
		END IF;

		RETURN TRUE;
	END checkPwdMaxLength;

	FUNCTION checkPwdContUName(pUsername VARCHAR2, pNewPassword VARCHAR2)
	RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(30) := 'checkPwdContUName';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pUsername);
		IF chk_pwdcontusername THEN
			slog.debug(pkgCtxId, myUnit, 'Verify if password contains username');
			IF INSTR( NLS_LOWER(pNewPassword), NLS_LOWER(pUsername)) <> 0 THEN
				loginStatusCode := cERR_PasswordContainsUsername;
				checkAndLock(getCID4User(pUsername), 'CHPWD');
				slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" password contains username');
				RETURN FALSE;
			END IF;
		END IF;
		RETURN TRUE;
	END checkPwdContUName;

	FUNCTION checkPwdContUNameRev(pUsername VARCHAR2, pNewPassword VARCHAR2)
	RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(30) := 'checkPwdContUNameRev';
		reverse_user VARCHAR2(255);
	BEGIN
		slog.debug(pkgCtxId, myUnit, pUsername);
		IF chk_pwdcontrevusername THEN
			slog.debug(pkgCtxId, myUnit, 'Verify if password contains reversed username');
			FOR i in REVERSE 1..LENGTH(pUsername) LOOP
			reverse_user := reverse_user || SUBSTR(pUsername, i, 1);
			END LOOP;

			IF INSTR(NLS_LOWER(pNewPassword), NLS_LOWER(reverse_user)) <> 0 THEN
				slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" password contains reversed username');
				loginStatusCode := cERR_PwdCntnsUnameInReverese;
				checkAndLock(getCID4User(pUsername), 'CHPWD');
				RETURN FALSE;
			END IF;
		END IF;
		RETURN TRUE;
	END checkPwdContUNameRev;

	FUNCTION checkPwdComplexity (pUsername VARCHAR2, pNewPassword VARCHAR2)
	RETURN BOOLEAN IS

	myunit CONSTANT VARCHAR2(30) := 'checkPwdComplexity';
        isdigit BOOLEAN;
        islowchar BOOLEAN;
        isupchar BOOLEAN;
        ispunct BOOLEAN;
        vSatisfyComplexity BOOLEAN;
        pwdCharactersAllowed BOOLEAN;
		headerMsg VARCHAR2(100);
		counter PLS_INTEGER :=0;

	BEGIN
	    slog.debug(pkgCtxId, myUnit, pUsername);

	    IF sspkg.readBool('/Core/Auth/Check_PasswordComplexity') THEN

		slog.debug(pkgCtxId, myUnit, 'Verify password complexity');

		vSatisfyComplexity := pwd_complexity_verify_function(
						pPassword => pNewPassword,
						digit => isdigit,
						lowchar =>islowchar,
						upchar=>isupchar,
						punct =>ispunct);

		IF NOT vSatisfyComplexity THEN

			IF NOT isdigit THEN
                    		loginStatusMessage := loginStatusMessage || chr(10) || mlang.trans(getLang, cERR_PasswordWithoutDigit);
                    		slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" password contains no digits');
                    		counter := counter + 1;
                	END IF;

                	IF NOT islowchar THEN
                    		loginStatusMessage := loginStatusMessage || chr(10) ||  mlang.trans(getLang, cERR_PasswordWithoutLowChar);
                    		slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" password contains no lower case characters');
                    		counter := counter + 1;
                	END IF;

                	IF NOT isupchar THEN
                    		loginStatusMessage := loginStatusMessage || chr(10) ||  mlang.trans(getLang, cERR_PasswordWithoutUpChar);
                    		slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" password contains no upper case characters');
                    		counter := counter + 1;
                	END IF;

               		 IF NOT ispunct THEN
                    		loginStatusMessage :=  loginStatusMessage || chr(10) ||  mlang.trans(getLang, cERR_PasswordWithoutPunctMark, punctarray);
                    		slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" password contains no punctuations marks');
                    		counter := counter + 1;
                	END IF;
                        -- Should never occur !!!
                	slog.error(pkgCtxId, myUnit, 'Password does not satisfy complexity requirements, but reason could not be determined!! Code error ???');

                	headerMsg :=  mlang.trans(getLang, cERR_pwdComplexityRuleHeader, counter-1);
                	loginStatusMessage := headerMsg || chr(10) || loginStatusMessage;
		END IF;

            		pwdCharactersAllowed := checkArePwdCharactersAllowed(pNewPassword => pNewPassword);

			IF NOT pwdCharactersAllowed THEN
				loginStatusMessage := loginStatusMessage || chr(10) || 'Dodatno: ' || mlang.trans(getLang, cERR_PasswordInvalidCharacters);
				slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" password contains invalid character!');
			END IF;

            	IF NOT vSatisfyComplexity OR NOT pwdCharactersAllowed THEN
                	RETURN FALSE;
            	END IF;

	END IF;

	RETURN TRUE;

	END checkPwdComplexity;

	FUNCTION checkPwdChangeExtAuth(
		pClientId NUMBER,
		pUsername VARCHAR2,
		pDeviceId VARCHAR2,
		pApplicationId VARCHAR2,
		pClientSignatureMethod VARCHAR2,
		otp VARCHAR2 := NULL,
		challenge VARCHAR2 := NULL)
	RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(30) := 'checkPwdChangeExtAuth';
		checkResult BOOLEAN := FALSE;
		vSessionAlreadyExists BOOLEAN := FALSE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId || ':' || pUsername || ':' || pDeviceId || ':' || pApplicationId ||':' || otp || ':' || challenge || ':' || pClientSignatureMethod);

		-- Provjeri da li je sesija na pocetku bila kako bi se kasnije znalo da li je prekinuta!
		IF getSUser IS NOT NULL THEN
			vSessionAlreadyExists := TRUE;
		END IF;

		setEvent (common_pck.cAUTHEVT_SIGN);


		CASE pClientSignatureMethod
			WHEN common_pck.cOTPTYPE_GENOTP THEN
				checkResult := checkOTP(otp);
				slog.debug(pkgCtxId, myUnit, 'Check result :' || bool2char (checkResult));
				IF NOT checkResult THEN
					loginStatusCode := cERR_WrongCredentials;
					checkAndLock(pClientId, 'CHPWD');
					slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" incorrect otp');
					RETURN FALSE;
				END IF;
			WHEN common_pck.cOTPTYPE_SENDOTP THEN
				checkResult := checkOTP(otp);
				slog.debug(pkgCtxId, myUnit, 'Check result :' || bool2char (checkResult));
				IF NOT checkResult THEN
					loginStatusCode := cERR_WrongCredentials;
					checkAndLock(pClientId, 'CHPWD');
					slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" incorrect otp');
					RETURN FALSE;
				END IF;
			WHEN common_pck.cOTPTYPE_CHRESP THEN
				checkResult := checkCHRESP(challenge => challenge, response => otp);
				slog.debug(pkgCtxId, myUnit, 'Check result :' || bool2char (checkResult));
				IF NOT checkResult THEN
					slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" incorrect response');

					loginStatusCode := cERR_WrongCredentials;
					checkAndLock(pClientId, 'CHPWD');

					IF vSessionAlreadyExists AND getSUser IS NULL THEN
						loginStatusCode := cERR_notLongerValid;
					END IF;

					RETURN FALSE;
				END IF;
			WHEN common_pck.cOTPTYPE_CHRESPPIN THEN
				checkResult := checkCHRESP(challenge => challenge, response => otp);
				slog.debug(pkgCtxId, myUnit, 'Check result :' || bool2char (checkResult));
				IF NOT checkResult THEN
					slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" incorrect response');
					loginStatusCode := cERR_WrongCredentials;
					checkAndLock(pClientId, 'CHPWD');

					IF vSessionAlreadyExists AND getSUser IS NULL THEN
						loginStatusCode := cERR_notLongerValid;
					END IF;

					RETURN FALSE;
				END IF;
			WHEN common_pck.cOTPTYPE_PKI THEN
				slog.debug(pkgCtxId, myUnit, 'Check result :' || bool2char (TRUE));
				RETURN TRUE; -- Let them pass!
			WHEN common_pck.cOTPTYPE_NULL THEN
				slog.debug(pkgCtxId, myUnit, 'Check result :' || bool2char (TRUE));
				RETURN TRUE; -- Let them pass!
			ELSE
				loginStatusCode := cERR_InternalError;
				loginStatusMessage := SUBSTR(mlang.trans(getLang, cERR_InternalError),1,loginStatusMessageMaxLength);
				slog.error(pkgCtxId, myUnit, 'Unsupported signature method "'||pClientSignatureMethod||'"');
				RETURN FALSE;
		END CASE;
		RETURN TRUE;

	END checkPwdChangeExtAuth;

	FUNCTION changePassword(oldPassword VARCHAR2, newPassword VARCHAR2, otp VARCHAR2 := NULL, challenge VARCHAR2 := NULL, pNOnce IN VARCHAR2 := NULL)
	RETURN BOOLEAN AS
		myunit CONSTANT VARCHAR2(30) := 'changePassword';

		vClientId NUMBER;
		CURSOR cclient is SELECT id, username, password, password_cs, gsm, ph5 FROM client WHERE id = vClientId;
		vclient cclient%ROWTYPE;
		vPh6 app_extauth.ph6%TYPE;
		vfrontAppSendHashOfUCasedPwd BOOLEAN;

	BEGIN
		slog.debug(pkgCtxId, myUnit, oldPassword||':'||newPassword||':'||otp||':'||challenge||':'||pNOnce);

		vfrontAppSendHashOfUCasedPwd := sspkg.readBool(pkgCtxId || '/frontAppSendHashOfUCasedPwd');

		vClientId  := getCID4User(getSUser);

		slog.debug(pkgCtxId, myUnit, 'Change password for user ' || getSUser || ':' || vClientId);

		IF oldPassword IS NULL OR newPassword IS NULL THEN
			loginStatusCode := cERR_NullCredentials;
			slog.error(pkgCtxId, myUnit, loginStatusCode);
			loginStatusMessage := SUBSTR(mlang.trans(getLang, loginStatusCode),1,loginStatusMessageMaxLength);
			writeClientLog('GSMLOGIN', loginStatusMessage, vlogstatus=>'ERROR', vclientid => vClientId, vgsm => NULL, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
			RETURN FALSE;
		END IF;

		IF getSUser IS NULL THEN
			sspkg.raiseError(cERR_NotPermited, 'Cannot change password without session established!', pkgCtxId, myUnit);
		END IF;

		loginStatusCode := NULL;
		loginStatusMessage := NULL;

		OPEN cclient;
		FETCH cclient INTO vclient;
		IF cclient%NOTFOUND THEN
			slog.warn(pkgCtxId, myUnit, 'User NOT loged in!');
			loginStatusCode := cERR_NoSession;
			loginStatusMessage := SUBSTR(mlang.trans(getLang, loginStatusCode),1,loginStatusMessageMaxLength);
			CLOSE cclient;
			RETURN FALSE;
		END IF;
		CLOSE cclient;

		slog.debug(pkgCtxId, myUnit, 'Check license ...');
		BEGIN
			checkLicense(pApplicationId => getApplicationId(), pClientId => vClientId);
		EXCEPTION
			WHEN sspkg.sysexception THEN
				loginStatusCode := sspkg.getErrorCode;
				loginStatusMessage := SUBSTR(mlang.trans(getLang, loginStatusCode),1,loginStatusMessageMaxLength);

				writeClientLog('CHPWD', 'No licence', vlogstatus=>'ERROR', vclientid => vclient.id, vgsm => vclient.gsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
				RETURN FALSE;
		END;

		slog.debug(pkgCtxId, myUnit, 'Check old password ...');

			-- mobile app send plain text for password not digest
        IF getApplicationId = common_pck.cAPP_MOBILE THEN

			SELECT ph6 INTO vPh6
			FROM app_extauth
			WHERE dev_id = getDeviceId
				AND client_id = vclient.id
				AND application_id = common_pck.cAPP_MOBILE
				AND valid = 1;

            IF (vCaseSensitivePwd AND vPh6 = cMobileDeviceWithPwdHashed) OR NOT vfrontAppSendHashOfUCasedPwd THEN
                IF NOT checkPwdMatchOld(pUsername => vclient.username, pOldPassword => oldPassword, pOldPasswordHash => NVL(vclient.password_cs, vclient.password), pNOnce => pNOnce) THEN
                    RETURN FALSE;
                END IF;
            ELSE
                IF NOT checkPwdMatchOld(pUsername => vclient.username, pOldPassword => oldPassword, pOldPasswordHash =>  vclient.password, pNOnce => pNOnce) THEN
                    RETURN FALSE;
                END IF;
            END IF;
        ELSE
            IF vCaseSensitivePwd THEN
                IF NOT checkPwdMatchOld(pUsername => vclient.username, pOldPassword => oldPassword, pOldPasswordHash => NVL(vclient.password_cs, vclient.password), pNOnce => pNOnce) THEN
                    RETURN FALSE;
                END IF;
            ELSE
                IF NOT checkPwdMatchOld(pUsername => vclient.username, pOldPassword => oldPassword, pOldPasswordHash =>  vclient.password, pNOnce => pNOnce) THEN
                    RETURN FALSE;
                END IF;
            END IF;
        END IF;


		slog.debug(pkgCtxId, myUnit, 'Check password reusage ...');

		IF isPasswordAlreadyUsed(pUserId => vclient.id, pNewPassword => newPassword) = 1 THEN
			loginStatusCode := cERR_PasswordReuseNotAllowed;
			loginStatusMessage := SUBSTR(mlang.trans(getLang, cERR_PasswordReuseNotAllowed, vPasswordMaxRetentionNo),1,loginStatusMessageMaxLength);
			RETURN FALSE;
		END IF;

		slog.debug(pkgCtxId, myUnit, 'Check password min length ...');

		IF NOT checkPwdMinLength(pUsername => vclient.username, pNewPassword => newPassword) THEN
			loginStatusMessage := SUBSTR(mlang.trans(getLang, loginStatusCode), 1,loginStatusMessageMaxLength);
			RETURN FALSE;
		END IF;

		slog.debug(pkgCtxId, myUnit, 'Check password max length ...');

		IF NOT checkPwdMaxLength(pUsername => vclient.username, pNewPassword => newPassword) THEN
			loginStatusMessage := SUBSTR(mlang.trans(getLang, loginStatusCode),1,loginStatusMessageMaxLength);
			RETURN FALSE;
		END IF;

		slog.debug(pkgCtxId, myUnit, 'Check password contains username ...');

		IF NOT checkPwdContUName(pUsername => vclient.username, pNewPassword => newPassword) THEN
			loginStatusMessage := SUBSTR(mlang.trans(getLang, loginStatusCode),1,loginStatusMessageMaxLength);
			RETURN FALSE;
		END IF;

		slog.debug(pkgCtxId, myUnit, 'Check password contains username reversed...');

		IF NOT checkPwdContUNameRev(pUsername => vclient.username, pNewPassword => newPassword) THEN
			loginStatusMessage := SUBSTR(mlang.trans(getLang, loginStatusCode),1,loginStatusMessageMaxLength);
			RETURN FALSE;
		END IF;

		slog.debug(pkgCtxId, myUnit, 'Check password complexity ...');

		IF NOT checkPwdComplexity(pUsername => vclient.username, pNewPassword => newPassword) THEN

			RETURN FALSE;
		END IF;

		slog.debug(pkgCtxId, myUnit, 'Verify by external authentication ...');

		DECLARE
			vExtAuth app_extauth.extauth_id%TYPE;
			vClientSignatureMethod VARCHAR2(1024);
		BEGIN
			vExtAuth := clientExtAuthID();
			slog.debug(pkgCtxId, myUnit, 'ExtAuth : ' || vExtAuth);

			vClientSignatureMethod := clientSignatureMethod(vExtAuth);
			slog.debug(pkgCtxId, myUnit, 'vClientSignatureMethod : ' || vClientSignatureMethod);

			IF vClientSignatureMethod NOT IN (common_pck.cOTPTYPE_NULL, common_pck.cOTPTYPE_PKI) THEN
				IF otp IS NULL THEN
					loginStatusCode := cERR_ExpectOTP;
					loginStatusMessage := NULL;
					RETURN FALSE;
				END IF;

				IF NOT checkPwdChangeExtAuth(
					pClientId => vclient.id, pUsername => vclient.username, pDeviceId => getDeviceId(),
					pApplicationId => getApplicationId(), pClientSignatureMethod => vClientSignatureMethod, otp => otp, challenge => challenge)
				THEN
					loginStatusMessage := SUBSTR(mlang.trans(getLang, loginStatusCode),1,loginStatusMessageMaxLength);

				RETURN FALSE;
				END IF;
			END IF;
		END;

		slog.debug(pkgCtxId, myUnit, 'Update password ...');

		UPDATE client
		SET password = common_pck.hash(upper(newPassword)), password_cs = common_pck.hash(newPassword), password_enabled = 1, ph5 = NULL, password_change_required = 0
		WHERE id = vclient.id;
		RETURN TRUE;

	EXCEPTION
		WHEN sspkg.sysException THEN
			RAISE;
		WHEN OTHERS THEN
			sspkg.raiseOraError(pkgCtxId, myUnit);
			RAISE;
	END changePassword;

	FUNCTION changeGSMPassword(vgsm VARCHAR2, voldpassword VARCHAR2, newPassword VARCHAR2)
	RETURN BOOLEAN is

		myunit CONSTANT VARCHAR2(30) := 'changeGSMPassword';

		CURSOR cclient is SELECT * FROM client WHERE gsm = vgsm AND vgsm is NOT NULL;
		vclient cclient%ROWTYPE;

		lSinceDate DATE;
		lUntilDate DATE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, vgsm);
		GsmSCID := NULL;
		GsmSPhone := NULL;
		loginStatusCode := NULL;
		loginStatusMessage := NULL;

		IF vgsm is NULL THEN
			loginStatusCode := cERR_NullCredentials;
			loginStatusMessage := SUBSTR('No phone number specified',1,loginStatusMessageMaxLength);
			slog.warn(pkgCtxId, myUnit, 'No phone number specified');
			RETURN FALSE;
		END IF;

-- 		Fetch client
		OPEN cclient;
		FETCH cclient INTO vclient;
		IF cclient%NOTFOUND THEN
			loginStatusCode := cERR_NO_SUCH_USER;
			loginStatusMessage := SUBSTR('GSM number "'||vgsm||'" does NOT exist',1,loginStatusMessageMaxLength);
			slog.warn(pkgCtxId, myUnit, 'GSM number "'||vgsm||'" does NOT exist');
			RETURN FALSE;
		END IF;
		CLOSE cclient;

		BEGIN
			checkLicense(pApplicationId => common_pck.cAPP_SMS, pClientId => vclient.id);
		EXCEPTION
			WHEN sspkg.sysexception THEN
				loginStatusCode := sspkg.getErrorCode;
				loginStatusMessage := SUBSTR(mlang.trans('bs', loginStatusCode),1,loginStatusMessageMaxLength);

				writeClientLog('GSMLOGIN', 'No licence', vlogstatus=>'ERROR', vclientid => vclient.id, vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
				RETURN FALSE;
		END;

		IF voldpassword IS NULL OR newPassword IS NULL THEN
			loginStatusCode := cERR_NullCredentials;
			loginStatusMessage := SUBSTR('No phone password specified',1,loginStatusMessageMaxLength);
			slog.warn(pkgCtxId, myUnit, 'No phone password specified');
			writeClientLog('GSMLOGIN', 'No phone password specified', vlogstatus=>'ERROR', vclientid => vclient.id, vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
			RETURN FALSE;
		END IF;

		IF isGsmLocked(vgsm, lSinceDate, lUntilDate) THEN
			loginStatusCode := cERR_LockedAccount;
			loginStatusMessage := SUBSTR('GSM phone is locked until '||to_char(lUntilDate, 'dd.mm.yyyy hh24:mi'),1,loginStatusMessageMaxLength);
			writeClientLog('GSMLOGIN', 'GSM phone "'||vgsm||'" is locked', vclientid => vclient.id, vlogstatus=>'ERROR', vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
			slog.warn(pkgCtxId, myUnit, 'GSM phone "'||vgsm||'" is locked');
			RETURN FALSE;
		END IF;

		/* Is client enabled */
		IF vclient.enabled <> 1 THEN
			loginStatusCode := cERR_DisabledAccount;
			loginStatusMessage := SUBSTR('Account is disabled',1,loginStatusMessageMaxLength);
			writeClientLog('GSMLOGIN', 'GSM phone "'||vgsm||'" is locked', vclientid => vclient.id, vlogstatus=>'ERROR', vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
			slog.warn(pkgCtxId, myUnit, 'Account with phone "'||vgsm||'" is disabled');
			RETURN FALSE;
		END IF;

		IF vclient.smspassword_enabled <> 1 THEN
			loginStatusCode := cERR_DisabledAccount;
			loginStatusMessage := SUBSTR('GSM login feature for that phone is disabled',1,loginStatusMessageMaxLength);
			writeClientLog('GSMLOGIN', 'GSM login feature for that phone is disabled', vclientid => vclient.id, vlogstatus=>'ERROR', vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
			slog.warn(pkgCtxId, myUnit, 'GSM login feature for that phone is disabled');
			RETURN FALSE;
		END IF;

		/* Is client expired */
		IF NOT sysdate between NVL(vclient.valid_from, sysdate-1) AND NVL(vclient.valid_to, sysdate+1) THEN
			loginStatusCode := cERR_ExpiredAccount;
			loginStatusMessage := SUBSTR(mlang.trans('en', cERR_ExpiredAccount),1,loginStatusMessageMaxLength);
			writeClientLog('GSMLOGIN', 'Expired account', vclientid => vclient.id, vlogstatus=>'ERROR', vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
			slog.warn(pkgCtxId, myUnit, 'Account is expired');
			RETURN FALSE;
		END IF;

		IF NOT NVL(sspkg.readBool(pkgCtxId||'/SmsMobileAuthEnabled'), FALSE) THEN
			loginStatusCode := cERR_InternalError;
			loginStatusMessage := SUBSTR('GSM Login is disabled',1,loginStatusMessageMaxLength);
			writeClientLog('GSMLOGIN', 'GSM Login is disabled', vclientid => vclient.id, vlogstatus=>'ERROR', vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
			slog.error(pkgCtxId, myUnit, 'GSM Login is disabled');
			RETURN FALSE;
		END IF;

   -- Check password finally
		IF NVL(vclient.password, 'NULL') IN (hash(voldpassword), hash(UPPER(voldpassword))) THEN
			UPDATE client
			SET smspassword = hash(newPassword)
			WHERE gsm = vgsm;
			RETURN TRUE;

		ELSE
			loginStatusCode := cERR_WrongCredentials;
			loginStatusMessage := SUBSTR('Incorrect gsm phone password',1,loginStatusMessageMaxLength);
			writeClientLog('GSMLOGIN', 'Incorrect gsm phone password', vclientid => vclient.id, vlogstatus=>'ERROR', vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage);
			checkAndLock(getCID4gsm(vgsm), 'GSMLOGIN');
			slog.warn(pkgCtxId, myUnit, 'Incorrect gsm password for phone "'||vgsm||'"');
			RETURN FALSE;
		END IF;

	EXCEPTION
		WHEN sspkg.sysException THEN
			ROLLBACK;
			RAISE;
		WHEN OTHERS THEN
			ROLLBACK;
			sspkg.raiseOraError(pkgCtxId, myUnit);
			RAISE;
	END changeGSMPassword;

	FUNCTION checkOTP(otp VARCHAR2) RETURN BOOLEAN AS
		myunit CONSTANT VARCHAR2(30) := 'checkOTP';
		vExtAuth app_extauth.extauth_id%TYPE;
		ires INTEGER;
		vClientId NUMBER;
	BEGIN
		slog.debug(pkgCtxId, myUnit, otp);

		IF getSUser IS NULL THEN
			sspkg.raiseError(cERR_NotPermited, 'Cannot checkOTP without session established!', pkgCtxId, myUnit);
		END IF;

		vClientId := getCID4User(getSUser);

		vExtAuth := clientExtAuthID();
		dynsql := sspkg.readVChar(extAuthCtx||'/'||vExtAuth||'/CheckOtpApi');
		IF dynsql is NOT NULL THEN
			EXECUTE IMMEDIATE dynSQL USING OUT ires, vClientId, otp;
		ELSE
			writeClientLog('CHECKOTP', 'Internal error', vlogstatus=>'ERROR', vclientid => vClientId, vstatus_code => cERR_InternalError, vstatus_message => 'There is no CheckOtpApi for ExtAuth "'||vExtAuth||'"');
			sspkg.raiseError(cERR_ExtAuthProblem, 'There is no CheckOtpApi for ExtAuth "'||vExtAuth||'"', pkgCtxId, myUnit);
			RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => vClientId, pIpAddress => getIPAddress(), pApplicationId => getApplicationId());
			RETURN FALSE;
		END IF;
		IF ires = 1 THEN
			writeClientLog('CHECKOTP', 'Response OK', vclientid => vClientId);
			RETURN TRUE;
		ELSE
			writeClientLog('CHECKOTP', 'Incorrect OTP: ' || otp, vlogstatus=>'ERROR', vclientid => vClientId, vstatus_code => cERR_WrongCredentials, vstatus_message => 'Incorrect OTP.');
			slog.warn(pkgCtxId, myUnit, 'Incorrect OTP');
			RegisterSplunkNotification(pActionId => common_pck.cFailedLogin,  pClientId => vClientId, pIpAddress => getIPAddress(), pApplicationId => getApplicationId());
			RETURN FALSE;
		END IF;

	EXCEPTION
		WHEN sspkg.sysException THEN
			slog.error(pkgCtxId, myUnit, 'Check OTP failed for client ' || vClientId);
			RAISE;
		WHEN OTHERS THEN
			slog.error(pkgCtxId, myUnit, 'Check OTP failed for client ' || vClientId);
			sspkg.raiseOraError(pkgCtxId, myUnit);
			RAISE;
	END checkOTP;

	PROCEDURE genSendOTP  AS
		myunit CONSTANT VARCHAR2(30) := 'genSendOTP';
		vClientOTPType VARCHAR2(4000);
		vExtAuth app_extauth.extauth_id%TYPE;
		vClientId NUMBER;
	BEGIN
		slog.debug(pkgCtxId, myUnit);

		IF getSUser IS NULL THEN
			sspkg.raiseError(cERR_NotPermited, 'Cannot genSendOTP without session established!', pkgCtxId, myUnit);
		END IF;

		vClientId := getCID4User(getSUser);

		vExtAuth := clientExtAuthID();
		vClientOTPType := clientOTPType(vExtAuth);
		dynsql := sspkg.readVChar(extAuthCtx||'/'||vExtAuth||'/SendOtpApi');

		CASE
		WHEN vClientOTPType in (common_pck.cOTPTYPE_SENDOTP) AND dynSQL is NOT NULL THEN
			EXECUTE IMMEDIATE dynsql using vClientId;
		ELSE
			sspkg.raiseError(cERR_ExtAuthProblem, 'Client OTP type "'||vClientOTPType||'" is NOT suitable for sendOTP or missing SendOtpApi', pkgCtxId, myUnit);
		END CASE;

		EXCEPTION
		WHEN sspkg.sysException THEN
		slog.error(pkgCtxId, myUnit, 'Gen OTP failed for client ' || vClientId);
		RAISE;
		WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, 'Gen OTP failed for client ' || vClientId);
		sspkg.raiseOraError(pkgCtxId, myUnit);
		RAISE;
	END genSendOTP;

	FUNCTION genChallenge(sourceData VARCHAR2 := dbms_random.string('A', 24)) RETURN VARCHAR2 as
		myunit CONSTANT VARCHAR2(30) := 'genChallenge';
		res VARCHAR2(1000) := NULL;
		vClientId client.id%TYPE;
	begin
		slog.debug(pkgCtxId, myUnit, sourceData);

		dynsql := sspkg.readVChar(extAuthCtx||'/'||clientExtAuthID()||'/GenChallengeApi');
		slog.debug(pkgCtxId, myUnit, 'DynSQL:'||dynsql);

		vClientId := getCID4User(getSUser);
		slog.debug(pkgCtxId, myUnit, 'clientId:'||vClientId);
		CASE
		WHEN dynSQL is NOT NULL THEN
			EXECUTE IMMEDIATE dynsql USING OUT res, vClientId, sourceData;
		ELSE
			sspkg.raiseError(cERR_ExtAuthProblem, 'Missing GenChallengeApi', pkgCtxId, myUnit);
		END CASE;

		RETURN res;

	EXCEPTION
		WHEN sspkg.sysException THEN
		slog.error(pkgCtxId, myUnit, 'Gen challenge failed!!!');
		RAISE;
		WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, 'Gen challenge failed!!!');
		sspkg.raiseOraError(pkgCtxId, myUnit);
		RAISE;
	END;

	FUNCTION checkSignature(sourceData VARCHAR2, signature VARCHAR2) RETURN BOOLEAN AS
		myunit CONSTANT VARCHAR2(30) := 'checkSignature';
		ires INTEGER;
		signatureMethod VARCHAR2(200);
		vClientExtAuthId app_extauth.extauth_id%TYPE;
		vClientId NUMBER;
	BEGIN
		slog.debug(pkgCtxId, myUnit);

		IF getSUser IS NULL THEN
			sspkg.raiseError(cERR_NotPermited, 'Cannot check signature without session established!', pkgCtxId, myUnit);
		END IF;

		vClientId := getCID4User(getSUser);
		ires := 0;

		vClientExtAuthId := clientExtAuthID();
		signatureMethod := sspkg.readVChar(extAuthCtx||'/'||vClientExtAuthId||'/SignatureMethod');
		dynsql := sspkg.readVChar(extAuthCtx||'/'||vClientExtAuthId||'/CheckSignatureApi');

		CASE
			WHEN signatureMethod in (common_pck.cOTPTYPE_PKI) AND dynSQL is NOT NULL THEN
				EXECUTE IMMEDIATE dynsql USING OUT ires, vClientId, sourceData, signature;
			ELSE
				writeClientLog('CHECKSIGN', 'Check signature', vlogstatus=>'ERROR', vclientid => vClientId, vstatus_code => cERR_InternalError, vstatus_message => 'SignatureMethod "'||signatureMethod||'" is NOT suitable for checkSignature or missing CheckSignatureApi');
				sspkg.raiseError(cERR_ExtAuthProblem, 'SignatureMethod "'||signatureMethod||'" is NOT suitable for checkSignature or missing CheckSignatureApi', pkgCtxId, myUnit);
				RegisterSplunkNotification(pActionId => common_pck.cFailedLogin,  pClientId => vClientId, pIpAddress => getIPAddress(), pApplicationId => getApplicationId());
		END CASE;

		IF ires = 1 THEN
			writeClientLog('CHECKSIGN', 'Signature OK', vclientid => vClientId);
			slog.info(pkgCtxId, myUnit, 'Signature OK for client ' || vClientId);
			RETURN TRUE;
		ELSE
			writeClientLog('CHECKSIGN', 'Incorrect signature', vlogstatus=>'ERROR', vclientid => vClientId, vstatus_code => cERR_WrongCredentials, vstatus_message => 'Incorrect signature. Try to use proper client certificate.');
			slog.warn(pkgCtxId, myUnit, 'Incorrect signature for client ' || vClientId);
			RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => vClientId, pIpAddress => getIPAddress(), pApplicationId => getApplicationId());
			RETURN FALSE;
		END IF;


	EXCEPTION
    WHEN sspkg.sysException THEN
		slog.error(pkgCtxId, myUnit, 'Signing failed!!!');
		RAISE;
    WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, 'Signing failed!!!');
		sspkg.raiseOraError(pkgCtxId, myUnit);
		RAISE;
	END checkSignature;

	FUNCTION checkCHRESP(challenge VARCHAR2, response VARCHAR2) RETURN BOOLEAN AS
		myunit CONSTANT VARCHAR2(30) := 'checkRESP';
		ires INTEGER;
		signatureMethod VARCHAR2(200);
		vClientExtAuthId app_extauth.extauth_id%TYPE;
		vClientId NUMBER;
	BEGIN
		slog.debug(pkgCtxId, myUnit, challenge || ':' || response);

		IF getSUser IS NULL THEN
			sspkg.raiseError(cERR_NotPermited, 'Cannot check CHRESP without session established!', pkgCtxId, myUnit);
		END IF;

		vClientId := getCID4User(getSUser);

		ires := 0;
		vClientExtAuthId := clientExtAuthID();
		signatureMethod := sspkg.readVChar(extAuthCtx||'/'||vClientExtAuthId||'/SignatureMethod');
		dynsql := sspkg.readVChar(extAuthCtx||'/'||vClientExtAuthId||'/CheckChRespApi');

		slog.debug(pkgCtxId, myUnit, vClientExtAuthId || ':' || signatureMethod);

		CASE
			WHEN signatureMethod in (common_pck.cOTPTYPE_CHRESP, common_pck.cOTPTYPE_CHRESPPIN) AND dynSQL is NOT NULL THEN
				EXECUTE IMMEDIATE dynsql USING OUT ires, getCID4User(getSUser), challenge, response;
			ELSE
				writeClientLog('CHECKCHRESP', 'Internal error', vlogstatus=>'ERROR', vclientid => vClientId, vstatus_code => cERR_InternalError, vstatus_message => 'SignatureMethod "'||signatureMethod||'" is NOT suitable for checkSignature or missing CheckChResp');
				sspkg.raiseError(cERR_ExtAuthProblem, 'SignatureMethod "'||signatureMethod||'" is NOT suitable for checkSignature or missing CheckChResp', pkgCtxId, myUnit);
				RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => vClientId, pIpAddress => getIPAddress(), pApplicationId => getApplicationId());
		END CASE;

		IF ires = 1 THEN
			writeClientLog('CHECKCHRESP', 'Response OK', vclientid => vClientId);
			slog.debug(pkgCtxId, myUnit, 'Response OK for client ' || vClientId);
			RETURN TRUE;
		ELSE
			writeClientLog('CHECKCHRESP', 'Incorrect response', vlogstatus=>'ERROR', vclientid => vClientId, vstatus_code => cERR_WrongCredentials, vstatus_message => 'Incorrect response. Generate new challenge AND try again.');
			slog.warn(pkgCtxId, myUnit, 'Incorrect response for client ' || vClientId);
			RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => vClientId, pIpAddress => getIPAddress(), pApplicationId => getApplicationId());
			RETURN FALSE;
		END IF;

	EXCEPTION
    WHEN sspkg.sysException THEN
		slog.error(pkgCtxId, myUnit, 'CHRESP failed!!!');
		RAISE;
    WHEN OTHERS THEN
		slog.error(pkgCtxId, myUnit, 'CHRESP failed!!!');
		sspkg.raiseOraError(pkgCtxId, myUnit);
		RAISE;
	END checkCHRESP;

	PROCEDURE checkSignature(pChallenge IN VARCHAR2,
							pResponse IN VARCHAR2,
							pOtp IN VARCHAR2,
							pSourceData IN VARCHAR2,
							pSignature IN VARCHAR2,
							pCheckResult OUT BOOLEAN,
							pClientSignatureMethod OUT VARCHAR2,
							pClientOTPType OUT VARCHAR2,
							pErrorCode OUT VARCHAR2)
	IS
		myunit CONSTANT VARCHAR2(30) := 'checkSignature';
		vSessionAlreadyExists BOOLEAN := FALSE;
		--vSessLng VARCHAR2(30);
	BEGIN
        slog.debug(pkgCtxId, myUnit, pChallenge || ':' || pResponse || ':' || pOtp || ':' || pSourceData || ':' || pSignature);


        -- Determine signature check method for current user
        pClientSignatureMethod := clientSignatureMethod();
        pClientOTPType         := clientOTPType(clientExtAuthID());

		-- Provjeri da li je sesija na pocetku bila kako bi se kasnije znalo da li je prekinuta!
		IF getSUser IS NOT NULL THEN
			vSessionAlreadyExists := TRUE;
			--vSessLng := getLang();
		END IF;

        IF nvl(pClientSignatureMethod, common_pck.cOTPTYPE_NULL) = common_pck.cOTPTYPE_NULL THEN
            -- If no signature check method is used
            pCheckResult := TRUE;
        ELSIF pClientSignatureMethod IN (common_pck.cOTPTYPE_CHRESP, common_pck.cOTPTYPE_CHRESPPIN) THEN
            -- If check - response check method is used
            IF checkCHRESP(pChallenge, pResponse) THEN
                -- Provided response was accepted
                pCheckResult := TRUE;
            ELSE
                -- Response does not apply to given challenge
                pCheckResult := FALSE;

				IF vSessionAlreadyExists AND getSUser IS NULL THEN
					pErrorCode := cERR_notLongerValid;
				ELSE
					pErrorCode := cERR_InvalidChallengeResponse;
				END IF;
            END IF;
        ELSIF pClientSignatureMethod IN (common_pck.cOTPTYPE_GENOTP, common_pck.cOTPTYPE_SENDOTP) THEN

                IF checkOTP(pOtp) THEN
                    -- Provided OTP was accepted
                    pCheckResult := TRUE;
                ELSE
                    -- OTP was not accepted
                    pCheckResult := FALSE;
					IF vSessionAlreadyExists AND getSUser IS NULL THEN
						pErrorCode := cERR_notLongerValid;
					ELSE
						pErrorCode := cERR_InvalidOTP;
					END IF;

                END IF;
        ELSIF pClientSignatureMethod = common_pck.cOTPTYPE_PKI THEN
            IF checkSignature(pSourceData, pSignature) THEN
               pCheckResult := TRUE;
            ELSE
                pCheckResult := FALSE;
				IF vSessionAlreadyExists AND getSUser IS NULL THEN
					pErrorCode := cERR_notLongerValid;
				ELSE
					pErrorCode := cERR_InvalidSignature;
				END IF;

            END IF;
        ELSE
            pCheckResult := FALSE;
            pErrorCode := cERR_UnsuportedSignatureMethod;
        END IF;

	END checkSignature;

	FUNCTION getGsmSCID RETURN CLIENT.ID%TYPE AS
	BEGIN
		RETURN gsmSCID;
	END getGsmSCID;

	FUNCTION getGsmSPhone RETURN VARCHAR2 AS
	BEGIN
		RETURN gsmSPhone;
	END getGsmSPhone;

	FUNCTION getGsmApplication RETURN VARCHAR2 IS
	BEGIN
		RETURN gsmApplication;
	END getGsmApplication;

	FUNCTION getGsmAccOwner RETURN varchar2 IS
	BEGIN
		RETURN gsmAccOwner;
	END getGsmAccOwner;

	PROCEDURE gsmLogout AS
		myunit CONSTANT VARCHAR2(30) := 'gsmLogout';
	BEGIN
		slog.debug(pkgCtxId, myUnit, 'GSM logout for client "'||gsmSCID);
		IF GsmSPhone is NOT NULL AND GsmSCID is NOT NULL THEN
			writeClientLog('GSMLOGOUT', 'GSM LOGOUT - internal', vclientid => GsmSCID, vgsm => GsmSPhone);
		END IF;

		GsmSCID := NULL;
		GsmSPhone := NULL;
	EXCEPTION WHEN OTHERS THEN
		GsmSCID := NULL;
		GsmSPhone := NULL;
		RAISE;
	END;

	FUNCTION gsmLogin(vgsm VARCHAR2, vpassword VARCHAR2, vapplication VARCHAR2) RETURN BOOLEAN as
		myunit CONSTANT VARCHAR2(30) := 'gsmLogin';

		CURSOR cclient is SELECT * FROM client WHERE vgsm = gsm AND vgsm is NOT NULL;
		vclient cclient%ROWTYPE;

		lSinceDate DATE;
		lUntilDate DATE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, vgsm || ':' || vapplication);

		GsmSCID          := NULL;
		GsmSPhone        := NULL;
		GsmApplication   := NULL;
		GsmAccOwner      := null;

		loginStatusCode := NULL;
		loginStatusMessage := NULL;

		IF vgsm is NULL THEN
			loginStatusCode := cERR_NullCredentials;
			loginStatusMessage := SUBSTR('No phone number specified',1,loginStatusMessageMaxLength);
			slog.warn(pkgCtxId, myUnit, 'No phone number specified');
			RETURN NULL;
		END IF;

-- 		Fetch client
		OPEN cclient;
		FETCH cclient INTO vclient;
		IF cclient%NOTFOUND THEN
			loginStatusCode := cERR_NO_SUCH_USER;
			loginStatusMessage := SUBSTR('GSM number "'||vgsm||'" does NOT exist',1,loginStatusMessageMaxLength);
			slog.warn(pkgCtxId, myUnit, 'GSM number "'||vgsm||'" does NOT exist');
			RETURN FALSE;
		END IF;
		CLOSE cclient;

		BEGIN
			checkLicense(pApplicationId => vapplication, pClientId => vclient.id);
		EXCEPTION
		WHEN sspkg.sysexception THEN
			loginStatusCode := sspkg.getErrorCode;
			loginStatusMessage := SUBSTR(mlang.trans('bs', loginStatusCode),1,loginStatusMessageMaxLength);

			writeClientLog('GSMLOGIN', 'No licence', vlogstatus=>'ERROR', vclientid => vclient.id, vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>vapplication);
			RETURN FALSE;
		END;

		IF vpassword is NULL THEN
			loginStatusCode := cERR_NullCredentials;
			loginStatusMessage := SUBSTR('No phone password specified',1,loginStatusMessageMaxLength);
			slog.warn(pkgCtxId, myUnit, 'No phone password specified');
			writeClientLog('GSMLOGIN', 'No phone password specified', vlogstatus=>'ERROR', vclientid => vclient.id, vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>vapplication);
			RETURN NULL;
		END IF;

		IF isGsmLocked(vgsm, lSinceDate, lUntilDate) THEN
			loginStatusCode := cERR_LockedAccount;
			loginStatusMessage := SUBSTR('GSM phone is locked until '||to_char(lUntilDate, 'dd.mm.yyyy hh24:mi'),1,loginStatusMessageMaxLength);
			writeClientLog('GSMLOGIN', 'GSM phone "'||vgsm||'" is locked', vclientid => vclient.id, vlogstatus=>'ERROR', vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>vapplication);
			slog.warn(pkgCtxId, myUnit, 'GSM phone "'||vgsm||'" is locked');
			RETURN FALSE;
		END IF;

		/* Is client enabled */
		IF vclient.enabled <> 1 THEN
			loginStatusCode := cERR_DisabledAccount;
			loginStatusMessage := SUBSTR('Account is disabled',1,loginStatusMessageMaxLength);
			writeClientLog('GSMLOGIN', 'GSM phone "'||vgsm||'" is locked', vclientid => vclient.id, vlogstatus=>'ERROR', vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>vapplication);
			slog.warn(pkgCtxId, myUnit, 'Account with phone "'||vgsm||'" is disabled');
			RETURN FALSE;
		END IF;

		IF vclient.smspassword_enabled <> 1 THEN
			loginStatusCode := cERR_DisabledAccount;
			loginStatusMessage := SUBSTR('GSM login feature for that phone is disabled',1,loginStatusMessageMaxLength);
			writeClientLog('GSMLOGIN', 'GSM login feature for that phone is disabled', vclientid => vclient.id, vlogstatus=>'ERROR', vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>vapplication);
			slog.warn(pkgCtxId, myUnit, 'GSM login feature for that phone is disabled');
			RETURN FALSE;
		END IF;

		/* Is client expired */
		IF NOT sysdate between NVL(vclient.valid_from, sysdate-1) AND NVL(vclient.valid_to, sysdate+1) THEN
			loginStatusCode := cERR_ExpiredAccount;
			loginStatusMessage := SUBSTR(mlang.trans('en', cERR_ExpiredAccount),1,loginStatusMessageMaxLength);
			writeClientLog('GSMLOGIN', 'Expired account', vclientid => vclient.id, vlogstatus=>'ERROR', vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>vapplication);
			slog.warn(pkgCtxId, myUnit, 'Account is expired');
			RETURN FALSE;
		END IF;

		IF NOT NVL(sspkg.readBool(pkgCtxId||'/SmsMobileAuthEnabled'), FALSE) THEN
			loginStatusCode := cERR_InternalError;
			loginStatusMessage := SUBSTR('GSM Login is disabled',1,loginStatusMessageMaxLength);
			writeClientLog('GSMLOGIN', 'GSM Login is disabled', vclientid => vclient.id, vlogstatus=>'ERROR', vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>vapplication);
			slog.error(pkgCtxId, myUnit, 'GSM Login is disabled');
			RETURN FALSE;
		END IF;

		-- Check password finally
		IF NVL(vclient.smspassword, 'NULL') IN (hash(vpassword), hash(UPPER(vpassword))) or
			vpassword = NVL(vclient.smspassword, 'NULL')
		THEN
			GsmSPhone      := vclient.gsm;
			gsmSCID        := vclient.id;
			gsmApplication := vapplication;
			gsmAccOwner    := '%';

			writeClientLog('LOGIN', 'Login', vclientId => gsmSCID, vgsm => GsmSPhone);

			RETURN TRUE;
		ELSE
			loginStatusCode := cERR_WrongCredentials;
			loginStatusMessage := SUBSTR('Incorrect gsm phone password',1,loginStatusMessageMaxLength);
			writeClientLog('GSMLOGIN', 'Incorrect gsm phone password', vclientid => vclient.id, vlogstatus=>'ERROR', vgsm => vgsm, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>vapplication);
			checkAndLock(getCID4gsm(vgsm), 'GSMLOGIN');
			slog.warn(pkgCtxId, myUnit, 'Incorrect gsm password for phone "'||vgsm||'"');
			RETURN FALSE;
		END IF;

		EXCEPTION
		WHEN sspkg.sysException THEN
			ROLLBACK;
			RAISE;
		WHEN OTHERS THEN
			ROLLBACK;
			sspkg.raiseOraError(pkgCtxId, myUnit);
			RAISE;
	END;

	-- This FUNCTION RETURN extauth id. If NULL is returned THEN user does NOT use additional OTP.
	-- Extauth_id are configured in branch /Core/Auth/ExtAuth/<ExtAuthID>

	FUNCTION getExtAuthId RETURN VARCHAR2
	AS
		myunit CONSTANT VARCHAR2(30) := 'getExtAuth';
		scid VARCHAR2(100);
	BEGIN

		scid := getSCID;
		IF scid is NULL THEN
			sspkg.raiseError(cERR_NotPermited, 'Cannot RETURN extauth id IF client is NOT logged', pkgCtxId, myUnit);
		END IF;

		RETURN NVL(getAppExtAuthId, clientExtAuthID());
	END getExtAuthId;

	-- Set language
	PROCEDURE setLang(langId VARCHAR2) AS
	BEGIN
		mcsm.setLang(langId);
	END;

	-- Get language
	FUNCTION getLang RETURN VARCHAR2 AS
	BEGIN
		RETURN mcsm.getLang;
	END;

	-- Set account owner
	PROCEDURE setAccountOwner(pAccOwnerId IN VARCHAR2)
	IS
		vPom VARCHAR2(1);
    		vSelectedAccOwner VARCHAR2 (40);
		myunit CONSTANT VARCHAR2(30) := 'setAccountOwner';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pAccOwnerId || ':' || getClientId() || ':' || getApplicationId());

		-- Check IF current session user has access rights on accounts for given acc owner
		SELECT NULL
		INTO vPom
		FROM DUAL
		WHERE EXISTS (SELECT NULL FROM mcore.vw$assigned_acc_owners WHERE account_owner_id = pAccOwnerId);

    		vSelectedAccOwner := mcsm.getSelectedAccOwner();

		mcsm.setAccountOwner(pAccountOwnerId => pAccOwnerId);

    		mcsm.setSelectedAccOwner(pAccountOwnerId => pAccOwnerId);


		-- login za potrebe starog splunka, usljed promjene i setovanja acc_ownera
		-- za novi splunk nije potrebno pratiti setovanje acc_ownera
		    RegisterSplunkNotification(pActionId => common_pck.cLogin, pClientID => getClientId(), pIpAddress => getIPAddress(), pApplicationId => getApplicationId());
    		IF vSelectedAccOwner is null THEN --> if user select account owner during login proccess
        		RegisterAccOwnerAccess('BY LOGIN');
    		ELSE
        		RegisterAccOwnerAccess('BY CHANGE');
    		END IF;

	EXCEPTION
		WHEN NO_DATA_FOUND THEN
			slog.error(pkgCtxId, myUnit, 'Wrong account owner:' || pAccOwnerId);
			sspkg.raiseError(cERR_invalidAccOwner, NULL, pkgCtxId, 'setAccountOwner');

	END setAccountOwner;

	FUNCTION getAccountOwner
	RETURN VARCHAR2 IS
	BEGIN
		RETURN NVL(mcsm.getAccountOwner, getGsmAccOwner());
	END getAccountOwner;

	FUNCTION getPrimaryAccountOwner
    RETURN VARCHAR2 IS
		myUnit CONSTANT VARCHAR2(30) := 'getPrimaryAccountOwner';
        vAccountOwner VARCHAR2 (40);
        vClientId client.id%TYPE := getClientId;

    BEGIN

        vAccountOwner := getAccountOwner;

        IF(vAccountOwner = '%') THEN

            BEGIN

                SELECT ph1
                INTO vAccountOwner
                FROM mcore.end_users
                WHERE id = vClientId;

            EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    vAccountOwner := null;
            END;

            IF(vAccountOwner is null) THEN

                BEGIN
					SELECT account_owner_id
					INTO vAccountOwner
					FROM
					(select distinct account_owner_id from mcore.vw$assigned_acc_owners order by account_owner_id asc)
					WHERE rownum < 2;
				EXCEPTION
					 WHEN NO_DATA_FOUND THEN
						slog.error(pkgCtxId, myUnit, cERR_UnableToFindPrimaryAccOwn || ':' || vClientId);
						sspkg.raiseError(cERR_UnableToFindPrimaryAccOwn, NULL, pkgCtxId, myUnit);
				END;

                RETURN vAccountOwner;

            END IF;

            RETURN vAccountOwner;

        END IF;

        RETURN vAccountOwner;

    END getPrimaryAccountOwner;

	FUNCTION getPreviousLoginInfo RETURN SYS_REFCURSOR
	IS
		rez SYS_REFCURSOR;
		vClientId client.id%TYPE := getSCID;
	BEGIN
		OPEN rez FOR
			SELECT cl.logdate, cl.hostname, cl.ipaddress, cl.gsm FROM
			(SELECT c.id, c.logdate, c.hostname, c.ipaddress, c.gsm FROM
			(SELECT a.id, a.logdate, a.hostname, a.ipaddress, a.gsm
			FROM clientlog a
			WHERE client_id = vClientId
			AND logtype_id = 'LOGIN'
			AND (description = 'EXTENDED_LOGIN' OR (description = 'BASIC_LOGIN' AND logstatus = 'ERROR'))
			order by id desc) c
			WHERE rownum < 3
			ORDER BY id ASC) cl
			WHERE rownum < 2;

		RETURN rez;
	END getPreviousLoginInfo;

	FUNCTION getPreviousLogins(pClientId client.id%TYPE) RETURN SYS_REFCURSOR
	IS
		rez SYS_REFCURSOR;
	BEGIN
		OPEN rez FOR
		SELECT logdate, description, hostname, ipaddress, logstatus, gsm, status_message FROM
		(SELECT logdate, description, hostname, ipaddress, logstatus, gsm, status_message
		FROM clientlog a
		WHERE client_id = pClientId
		AND logtype_id = 'LOGIN'
		AND (description = 'EXTENDED_LOGIN' OR (description = 'BASIC_LOGIN' AND logstatus = 'ERROR'))
		ORDER BY ID DESC)
		WHERE rownum < vDisplayLastLogins;

		RETURN rez;
	END getPreviousLogins;

	FUNCTION isSessionExpired
	RETURN BOOLEAN
	IS
	BEGIN
		RETURN mcsm.isSessionExpired;
	END isSessionExpired;

	FUNCTION getExtAuthTag RETURN VARCHAR2 IS
	BEGIN
		RETURN sspkg.readVChar(extAuthCtx||'/'||clientExtAuthID()||'/Tag');
	END getExtAuthTag;

	FUNCTION isAlmostDepleted(clientID CLIENT.ID%TYPE)
	RETURN VARCHAR2
	IS
		vExtAuthTAG VARCHAR2(40);
		myunit CONSTANT VARCHAR2(30) := 'isAlmostDepleted';
		vClientId client.id%TYPE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, 'Client:' || vClientId);
		vClientId := NVL(clientID, getCID4User(getSUser));
		vExtAuthTAG := getExtAuthTag();
		slog.debug(pkgCtxId, myUnit, 'Tag:' || vExtAuthTAG);
		IF vExtAuthTAG = 'TAN' THEN
			RETURN tanlist_plugin.checkRemainingTANs(vClientId);
		END IF;
		RETURN NULL;
	END isAlmostDepleted;

  FUNCTION resolveUserIdentifier(pUserIdentifier IN VARCHAR2)
  RETURN client_details.client_id%TYPE IS
	vClientId client_details.client_id%TYPE;
	myunit CONSTANT VARCHAR2(30) := 'resolveUserIdentifier';
  BEGIN
	slog.debug(pkgCtxId, myUnit, pUserIdentifier);
	DELETE FROM client_details WHERE attrib_id = cMTokenUserId AND data_vchar = hash(pUserIdentifier)
	RETURN client_id INTO vClientId;

	RETURN vClientId;
  END resolveUserIdentifier;

  -- pActivationKey is in case of mToken only the HASH value used to check if it is valid. User authenticate himself using unique user identifier (OTP) which is compared with saved hash value.
  -- User enters provided activation key in his phone which generates a SEC_KEY. Same happened on server side when activation key was generated. During this step the Device SNo (based on DeviceId) is registered!
  -- pDeviceId is Device Serial number generated using DeviceId
  FUNCTION registerDevice(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2,
      host VARCHAR2, certificate VARCHAR2,
      sessLogLevel INTEGER := NULL, langId VARCHAR2 := NULL, pApplicationId VARCHAR2 := NULL,
	  pInstallationId VARCHAR2 := NULL, pDeviceId VARCHAR2, pDeviceDescription VARCHAR2,
	  pActivationKey VARCHAR2, pExtAuthId VARCHAR2, pStatusMessage OUT VARCHAR2, pOperatingSystem IN VARCHAR2 := NULL,
	  pUserIdentifier IN VARCHAR2 := NULL, pDeviceType IN VARCHAR2 := NULL, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL)
  RETURN VARCHAR2 is
	myunit CONSTANT VARCHAR2(30) := 'registerDevice';

	vSecKey VARCHAR2(1024);

    vClient vClientType;
    vLoginSuccess BOOLEAN;
	vSecKeyLength PLS_INTEGER := 30;
	vStatusMessage VARCHAR2(1000 CHAR);

	vAppDeviceAwareness PLS_INTEGER;

	vPwdExpireInDays PLS_INTEGER;

	vDeviceType VARCHAR2(80);
	vClientId client.id%TYPE;

	cValidAppExtauth CONSTANT PLS_INTEGER := 1;

    PRAGMA AUTONOMOUS_TRANSACTION;
  BEGIN
	slog.debug(pkgCtxId, myUnit, vusername ||':' || pDeviceId || ':' || pActivationKey ||':'||pExtAuthId || ':' || vDeviceType);

	vDeviceType := NVL(pDeviceType, common_pck.cAPP_MOBILE);

	-- TODO: Provjeriti da li ovo ima smisla jer u zaglavlju stoji da će biti NULL ZA MTOKEN !!!!!!!!!!!!!!!!!!!
	IF pActivationKey IS NULL THEN
		slog.error(pkgCtxId, myUnit, 'pActivationKey NULL');
		ROLLBACK;
		sspkg.raiseError(cERR_WrongCredentials, NULL, pkgCtxId, myunit);
	END IF;

	IF pDeviceId IS NULL THEN
		slog.error(pkgCtxId, myUnit, 'pDeviceId NULL');
		ROLLBACK;
		sspkg.raiseError(cERR_InvalidDevice, NULL, pkgCtxId, myunit);
	END IF;

	--vDeviceSNo := substr(to_char(mcore.util.hex2dec(rawtohex(dbms_crypto.hash(src => utl_raw.cast_to_raw(pDeviceId), typ => dbms_crypto.HASH_SH1))),'999999999999999999999999999999999999999999999999'),1,vDeviceSNo_Length);

	IF pApplicationId IS NULL THEN
		slog.error(pkgCtxId, myUnit, 'pApplicationId NULL');
		ROLLBACK;
		sspkg.raiseError(cERR_InvalidDevice, NULL, pkgCtxId, myunit);
	END IF;

	<<DeviceType>>
	IF vDeviceType = common_pck.cAPP_MOBILE THEN


		BEGIN
			BEGIN
				SELECT rowid, id, enabled, password, password_enabled, valid_from, valid_to, ph6, to_date(ph3, 'dd.mm.yyyy hh24:mi:ss'), NULL, ph4, password_change_required, password_cs
				INTO vClient
				FROM client
				WHERE lower(username) = lower(vusername)
				AND vusername is NOT NULL;
			EXCEPTION
				WHEN no_data_found THEN
					ROLLBACK;
					sspkg.raiseError(cERR_WrongCredentials, NULL, pkgCtxId, myunit);
			END;

			vLoginSuccess := basicAccountCheck(
				pUsername =>vusername,
				pPassword =>vpassword,
				ipAddress =>ipAddress,
				host =>host,
				sessLogLevel =>sessLogLevel,
				langId =>langId,
				pApplicationId =>pApplicationId,
				pInstallationId =>pInstallationId,
				pClient => vClient,
				pPasswordExpireInDays => vPwdExpireInDays,
				pNOnce => pNOnce,
				pDeviceId => pDeviceId,
				pClientExtAuthId => pExtAuthId,
				pPasswordHashed => pPasswordHashed,
				pAppVersionId => pAppVersionId,
				pOSVersionId => pOSVersionId,
				pIsFirstLogin => TRUE);

			IF NOT vLoginSuccess AND getLoginStatusCode <> cERR_ExprdPwdChangeAllowed THEN
				ROLLBACK;
				sspkg.raiseError(cERR_WrongCredentials, NULL, pkgCtxId, myunit);
			END IF;

			slog.debug(pkgCtxId, myUnit, 'Basic account check succeedded!');

			slog.debug(pkgCtxId, myUnit, 'Expect authKey: ' || vClient.vAuthKey || ', got: ' || common_pck.hash(REPLACE(REPLACE(pActivationKey,CHR(32)),NVL(sspkg.readVchar(cmElbaActKeySegmentDelimiter),'-'))));

			-- Check activation key validity
			IF vClient.vAuthKey IS NULL OR vClient.vAuthKey NOT IN (NVL(common_pck.hash(REPLACE(REPLACE(pActivationKey,CHR(32)),NVL(sspkg.readVchar(cmElbaActKeySegmentDelimiter),'-'))),'NULL'), NVL(common_pck.hash(REPLACE(REPLACE(LOWER(pActivationKey),CHR(32)),NVL(sspkg.readVchar(cmElbaActKeySegmentDelimiter),'-'))),'NULL'), NVL(common_pck.hash(REPLACE(REPLACE(UPPER(pActivationKey),CHR(32)),NVL(sspkg.readVchar(cmElbaActKeySegmentDelimiter),'-'))),'NULL')) then
				ROLLBACK;
				loginStatusCode := cERR_InvalidActivationKey;
				loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_InvalidActivationKey),1,loginStatusMessageMaxLength);
				writeClientLog('CHECKACTKEY', 'Wrong activation key!', vlogstatus=>'ERROR', vclientid => vclient.id, vhost => host, vipaddress => ipaddress, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);

				sspkg.raiseError(cERR_InvalidActivationKey, NULL, pkgCtxId, myunit);
			ELSIF vClient.vAuthKeyValidUntil < sysdate THEN
				ROLLBACK;
				loginStatusCode := cERR_ActivationKeyExpired;
				loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_ActivationKeyExpired),1,loginStatusMessageMaxLength);
				writeClientLog('CHECKACTKEY', 'Activation key expired!', vlogstatus=>'ERROR', vclientid => vclient.id, vhost => host, vipaddress => ipaddress, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId=>pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);

				sspkg.raiseError(cERR_InvalidActivationKey, NULL, pkgCtxId, myunit);
			END IF;

			-- 3 Generate New SecKey
			DECLARE
				vExtAuthRec ROWID;
			BEGIN
				slog.debug(pkgCtxId, myUnit, 'Generate new sec key');
				vSecKey := SUBSTR(rawtohex(dbms_crypto.hash(src => utl_raw.cast_to_raw(pDeviceId) || dbms_crypto.RandomBytes(8), typ => dbms_crypto.HASH_SH1)),1,vSecKeyLength);
				-- 4. Check if there is already a registration entry for device

				slog.debug(pkgCtxId, myUnit, 'Check for existing registrations ...');

				vAppDeviceAwareness := common_pck.isAppDeviceAware(pApplicationId);

				IF vAppDeviceAwareness = 0 THEN
					SELECT ROWID
					INTO vExtAuthRec
					FROM app_extauth
					where client_id = vClient.id
					and application_id = pApplicationId;
				ELSE
					SELECT ROWID
					INTO vExtAuthRec
					FROM app_extauth
					where client_id = vClient.id
					and application_id = pApplicationId
					and dev_id = pDeviceId;
				END IF;

				slog.debug(pkgCtxId, myUnit, 'Check for existing registrations ... found');
				-- 4.1 if exists, update with new data
				UPDATE app_extauth
				SET extauth_id = pExtAuthId,
					dev_id = pDeviceId,
					date_of_registration = sysdate,
					sec_key = vSecKey,
					valid = cValidAppExtauth,
					ph0 = pOperatingSystem,
					ph3 = NULL,
					ph4 = NULL,
					ph5 = cDevicePasswordAuthorized
				WHERE ROWID = vExtAuthRec;

				UPDATE client
				SET ph6 = NULL,
					ph3 = NULL
				WHERE ROWID = vClient.rowrec;

				slog.debug(pkgCtxId, myUnit, 'Remove previous client_details records');
				DELETE FROM client_details WHERE client_id = vClient.id AND attrib_id IN (cmELBAActKey, cmELBAActKeyValidUntil);

				IF vAppDeviceAwareness = 0 THEN
					vStatusMessage := mlang.trans(langId, cERR_SuccessfullPromotion);
				ELSE
					vStatusMessage := mlang.trans(langId, cERR_SuccessfullRegistration);
				END IF;

			EXCEPTION
				-- Should not happen!
				WHEN TOO_MANY_ROWS THEN
					IF vAppDeviceAwareness = 0 THEN
						slog.error(pkgCtxId, myUnit, 'Check for existing registrations ... found multiple registrations! Clean up ...');
						DELETE FROM app_extauth WHERE client_id = vClient.id AND application_id = pApplicationId;
					ELSE
						sspkg.raiseError(cERR_MltplExtAuthRegistrations, NULL, pkgCtxId, myunit);
					END IF;
					slog.error(pkgCtxId, myUnit, 'Check for existing registrations ... found multiple registration! Clean up ... succedded!');

					INSERT INTO app_extauth(id, client_id, application_id, extauth_id, dev_id, device_desc, date_of_registration, sec_key, valid, ph0, ph1, ph2, ph3, ph4, ph5)
					VALUES (app_extauth_SEQ.NEXTVAL, vClient.id, pApplicationId, pExtAuthId, pDeviceId, SUBSTR(pDeviceDescription,1,200), SYSDATE, vSecKey, cValidAppExtauth, pOperatingSystem, NULL, NULL, NULL, NULL, cDevicePasswordAuthorized);

					UPDATE client
					SET ph6 = NULL,
						ph3 = NULL
					WHERE ROWID = vClient.rowrec;

					slog.debug(pkgCtxId, myUnit, 'Remove previous client_details records');
					DELETE FROM client_details WHERE client_id = vClient.id AND attrib_id IN (cmELBAActKey, cmELBAActKeyValidUntil);

					vStatusMessage := mlang.trans(langId, cERR_SuccessfullPromotion);
					slog.debug(pkgCtxId, myUnit, 'Inserting new auth data ... OK');

				WHEN NO_DATA_FOUND THEN
					slog.debug(pkgCtxId, myUnit, 'Check for existing registrations ... not found! Inserting new auth data ...');
					-- 4.2 if not, insert new registration entry
					INSERT INTO app_extauth(id, client_id, application_id, extauth_id, dev_id, device_desc, date_of_registration, sec_key, valid, ph0, ph1, ph2, ph3, ph4, ph5)
					VALUES (app_extauth_SEQ.NEXTVAL, vClient.id, pApplicationId, pExtAuthId, pDeviceId, SUBSTR(pDeviceDescription,1,200), SYSDATE, vSecKey, cValidAppExtauth, pOperatingSystem, NULL, NULL, NULL, NULL, cDevicePasswordAuthorized);

					UPDATE client
					SET ph6 = NULL,
						ph3 = NULL
					WHERE ROWID = vClient.rowrec;

					slog.debug(pkgCtxId, myUnit, 'Remove previous client_details records');
					DELETE FROM client_details WHERE client_id = vClient.id AND attrib_id IN (cmELBAActKey, cmELBAActKeyValidUntil);

					vStatusMessage := mlang.trans(langId, cERR_SuccessfullRegistration);
					slog.debug(pkgCtxId, myUnit, 'Inserting new auth data ... OK');
			END;


        IF getLoginStatusCode = cERR_ExprdPwdChangeAllowed THEN
          pStatusMessage := vStatusMessage;
          COMMIT;

          quietDestroyS;
          RETURN vSecKey;

        END IF;
		END;
	ELSIF vDeviceType = common_pck.cAPP_MTOKEN THEN
		BEGIN
			IF pUserIdentifier IS NULL THEN
				slog.error(pkgCtxId, myUnit, 'pUserIdentifier NULL');
				ROLLBACK;
				sspkg.raiseError(cERR_WrongCredentials, NULL, pkgCtxId, myunit);
			END IF;

			slog.debug(pkgCtxId, myUnit, 'Retrieve client ID using provided unique user identifier');
			vClientId := resolveUserIdentifier(pUserIdentifier);
			slog.debug(pkgCtxId, myUnit, 'Client ID: ' || vClientId);

			IF vClientId IS NULL THEN
				slog.error(pkgCtxId, myUnit, 'vClientId NULL');
				ROLLBACK;
				sspkg.raiseError(cERR_WrongCredentials, NULL, pkgCtxId, myunit);
			END IF;

			BEGIN
				slog.debug(pkgCtxId, myUnit, 'Retrieve client data');
				SELECT c.rowid, c.id, c.enabled, c.password, c.password_enabled, c.valid_from, c.valid_to,
				-- ph6, to_date(ph3, 'dd.mm.yyyy hh24:mi:ss'),
				MAX (DECODE(cd.attrib_id, cMTokenActKey, cd.data_vchar, NULL)) act_key,
				TO_DATE(MAX (DECODE(cd.attrib_id, cMTokenActKeyValidUntil, cd.data_vchar, NULL)), common_pck.cDATETIME_MASK) act_key_valid_until,
				MAX (DECODE(cd.attrib_id, cMTokenUserId, cd.data_vchar, NULL)) user_id,
				c.ph4, c.password_change_required, password_cs
				INTO vClient
				FROM client c JOIN client_details cd ON (cd.client_id = c.id)
				WHERE c.id = vClientId AND cd.attrib_id IN (cMTokenActKey, cMTokenActKeyValidUntil,cMTokenUserId)
				GROUP BY c.rowid, c.id, c.enabled, c.password, c.password_enabled, c.valid_from, c.valid_to, c.ph4, c.password_change_required, password_cs;
			EXCEPTION
				WHEN no_data_found THEN
					slog.error(pkgCtxId, myUnit, 'No data found for client ' || vClientId ||'!. Act. key not generated?');
					ROLLBACK;
					sspkg.raiseError(cERR_WrongCredentials, NULL, pkgCtxId, myunit);
			END;

			slog.debug(pkgCtxId, myUnit, 'Expect authKey: ' || vClient.vAuthKey || ', got: ' || NVL(pActivationKey,'NULL'));

			-- Check activation key validity
			IF vClient.vAuthKey IS NULL then
				slog.error(pkgCtxId, myUnit, 'vClient.vAuthKey IS NULL for ' || vClientId);
				loginStatusCode := cERR_InvalidActivationKey;
				loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_InvalidActivationKey),1,loginStatusMessageMaxLength);
				writeClientLog('CHECKACTKEY', 'No activation key for match!', vlogstatus=>'ERROR', vclientid => vclient.id, vhost => host, vipaddress => ipaddress, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);
				ROLLBACK;
				sspkg.raiseError(cERR_InvalidActivationKey, NULL, pkgCtxId, myunit);
			ELSIF vClient.vAuthKeyValidUntil < sysdate THEN
				slog.error(pkgCtxId, myUnit, 'vClient.vAuthKeyValidUntil expired for ' || vClientId);
				loginStatusCode := cERR_ActivationKeyExpired;
				loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_ActivationKeyExpired),1,loginStatusMessageMaxLength);
				writeClientLog('CHECKACTKEY', 'Activation key expired!', vlogstatus=>'ERROR', vclientid => vclient.id, vhost => host, vipaddress => ipaddress, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);
				ROLLBACK;
				sspkg.raiseError(cERR_InvalidActivationKey, NULL, pkgCtxId, myunit);
			ELSIF vClient.vAuthKey <> NVL(pActivationKey,'NULL') then
				slog.error(pkgCtxId, myUnit, 'Activation key does not match for ' || vClientId);
				loginStatusCode := cERR_InvalidActivationKey;
				loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_InvalidActivationKey),1,loginStatusMessageMaxLength);
				writeClientLog('CHECKACTKEY', 'Wrong activation key!', vlogstatus=>'ERROR', vclientid => vclient.id, vhost => host, vipaddress => ipaddress, vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);
				ROLLBACK;
				sspkg.raiseError(cERR_InvalidActivationKey, NULL, pkgCtxId, myunit);
			END IF;

			slog.debug(pkgCtxId, myUnit, 'Determine if application is device avare');
			vAppDeviceAwareness := common_pck.isAppDeviceAware(pApplicationId);

			DECLARE
				vExtAuthRec ROWID;
			BEGIN
				-- 4. Check if there is already a registration entry for device

				slog.debug(pkgCtxId, myUnit, 'Check for existing registrations for user ' || vClient.id);
				IF vAppDeviceAwareness = 0 THEN
					slog.debug(pkgCtxId, myUnit, 'Application is not device avare');
					SELECT ROWID
					INTO vExtAuthRec
					FROM app_extauth
					WHERE client_id = vClient.id
					AND application_id = pApplicationId;
				ELSE
					slog.debug(pkgCtxId, myUnit, 'Application is device avare');
					SELECT ROWID
					INTO vExtAuthRec
					FROM app_extauth
					WHERE client_id = vClient.id
					AND application_id = pApplicationId
					AND dev_id = pDeviceID;
				END IF;
				slog.debug(pkgCtxId, myUnit, 'Check for existing registrations ... found');

				-- 4.1 if exists, update with new data (in case, application is reinstalled on new device or application replaces existing auth method)
				slog.debug(pkgCtxId, myUnit, 'Update existing app_extauth entry');
				update app_extauth
				SET dev_id = pDeviceID,
					date_of_registration = sysdate,
					sec_key = NULL,
					valid = cValidAppExtauth,
					ph0 = pOperatingSystem
				WHERE ROWID = vExtAuthRec;

				slog.debug(pkgCtxId, myUnit, 'Remove previous client_details records');
				DELETE FROM client_details WHERE client_id = vClient.id AND attrib_id IN (cMTokenActKey, cMTokenActKeyValidUntil);
				-- cMTokenUserId already deleted by resolveUserIdentifier call

				IF vAppDeviceAwareness = 0 THEN
					vStatusMessage := mlang.trans(langId, cERR_SuccessfullPromotion, vClient.id);
				ELSE
					vStatusMessage := mlang.trans(langId, cERR_SuccessfullRegistration);
				END IF;

			EXCEPTION
				-- Should not happen!
				WHEN TOO_MANY_ROWS THEN
					sspkg.raiseError(cERR_MltplExtAuthRegistrations, NULL, pkgCtxId, myunit);

				WHEN NO_DATA_FOUND THEN
					sspkg.raiseError(cERR_ExtAuthProblem, NULL, pkgCtxId, myunit);
			END;

			slog.debug(pkgCtxId, myUnit, 'Call mToken plugin ...');

			mtoken_plugin.assignDevice(pClientId => vClient.id, pDeviceID => pDeviceID);
			slog.debug(pkgCtxId, myUnit, 'Call mToken plugin ... OK');
		END;

	ELSIF vDeviceType = common_pck.cAPP_mElbaWidget THEN

		DECLARE
			vExtAuthRec ROWID;
			cClientId client.id%TYPE;
			cDeviceId app_extauth.dev_id%TYPE;
		BEGIN
			slog.debug(pkgCtxId, myUnit, 'Generate new sec key');

			cClientId := auth.getClientId;
			cDeviceId := auth.getDeviceId();

			IF cClientId IS NULL THEN
			  sspkg.raiseError(mcore.common_pck.cERR_NoSession, NULL, pkgCtxId, myunit);
			END IF;

			vSecKey := SUBSTR(rawtohex(dbms_crypto.hash(src => utl_raw.cast_to_raw(cDeviceId) || dbms_crypto.RandomBytes(8), typ => dbms_crypto.HASH_SH1)),1,vSecKeyLength);
			-- 4. Check if there is already a registration entry for device

			slog.debug(pkgCtxId, myUnit, 'Check for existing registrations ...');

			vAppDeviceAwareness := common_pck.isAppDeviceAware(pApplicationId);

			IF vAppDeviceAwareness = 0 THEN
				SELECT ROWID
				INTO vExtAuthRec
				FROM app_extauth
				where client_id = cClientId
				and application_id = pApplicationId;
			ELSE
				SELECT ROWID
				INTO vExtAuthRec
				FROM app_extauth
				where client_id = cClientId
				and application_id = pApplicationId
				and dev_id = cDeviceId;
			END IF;

			slog.debug(pkgCtxId, myUnit, 'Check for existing registrations ... found');
			-- 4.1 if exists, update with new data
			UPDATE app_extauth
			SET extauth_id = pExtAuthId,
				dev_id = cDeviceId,
				date_of_registration = sysdate,
				sec_key = vSecKey,
				valid = cValidAppExtauth,
				ph0 = pOperatingSystem,
				ph5 = cDevicePasswordAuthorized
			WHERE ROWID = vExtAuthRec;

			vStatusMessage := mlang.trans(langId, cERR_SuccessfullRegistration);

		EXCEPTION
			-- Should not happen!
			WHEN TOO_MANY_ROWS THEN
				IF vAppDeviceAwareness = 0 THEN
					slog.error(pkgCtxId, myUnit, 'Check for existing registrations ... found multiple registrations! Clean up ...');
					DELETE FROM app_extauth WHERE client_id = cClientId AND application_id = pApplicationId;
				ELSE
					sspkg.raiseError(cERR_MltplExtAuthRegistrations, NULL, pkgCtxId, myunit);
				END IF;
				slog.error(pkgCtxId, myUnit, 'Check for existing registrations ... found multiple registration! Clean up ... succedded!');

				INSERT INTO app_extauth(ID, CLIENT_ID, APPLICATION_ID, EXTAUTH_ID, DEV_ID, DEVICE_DESC, DATE_OF_REGISTRATION, SEC_KEY, VALID, PH0, PH5)
				VALUES (app_extauth_SEQ.NEXTVAL, cClientId, pApplicationId, pExtAuthId, cDeviceId, SUBSTR(pDeviceDescription,1,200), SYSDATE, vSecKey, 1, pOperatingSystem, cDevicePasswordAuthorized);

				vStatusMessage := mlang.trans(langId, cERR_SuccessfullRegistration);
				slog.debug(pkgCtxId, myUnit, 'Inserting new auth data ... OK');

			WHEN NO_DATA_FOUND THEN
				slog.debug(pkgCtxId, myUnit, 'Check for existing registrations ... not found! Inserting new auth data ...');
				-- 4.2 if not, insert new registration entry
				INSERT INTO app_extauth(ID, CLIENT_ID, APPLICATION_ID, EXTAUTH_ID, DEV_ID, DEVICE_DESC, DATE_OF_REGISTRATION, SEC_KEY, VALID, PH0, PH5)
				VALUES (app_extauth_SEQ.NEXTVAL, cClientId, pApplicationId, pExtAuthId, cDeviceId, SUBSTR(pDeviceDescription,1,200), SYSDATE, vSecKey, 1, pOperatingSystem, cDevicePasswordAuthorized);

				vStatusMessage := mlang.trans(langId, cERR_SuccessfullRegistration);
				slog.debug(pkgCtxId, myUnit, 'Inserting new auth data ... OK');
		END;
  END IF;

	pStatusMessage := vStatusMessage;
	COMMIT;

	RETURN vSecKey;

  EXCEPTION
   WHEN OTHERS THEN
	ROLLBACK;
	RAISE;
  END registerDevice;

  PROCEDURE unregisterDevice(pAppExtAuthId app_extauth.id%TYPE) IS
	myunit CONSTANT VARCHAR2(30) := 'unregisterDevice';
    vRowRec rowid;
	vApplicationId app_extauth.application_id%TYPE;
	vDeviceId app_extauth.dev_id%TYPE;

  BEGIN
	slog.debug(pkgCtxId, myUnit, pAppExtAuthId);

    IF NOT isLogged THEN
        sspkg.raiseError(cERR_NotPermited, NULL, pkgCtxId, myUnit);
    END IF;

	SELECT ROWID, application_id, dev_id
	INTO vRowRec, vApplicationId, vDeviceId
	FROM app_extauth
	WHERE id = pAppExtAuthId
	AND client_id = getSCID();
	/*
	In case of master application (like MERKAT-MOBILE-V1), vParentApplicationId is NULL.
	In case of dependent application (like mElbaWidget), vParentApplicationId is MERKAT-MOBILE-V1.
	*/
	IF common_pck.isAppDeviceAware(vApplicationId) = 1 THEN
		-- Either master or dependent application can be device aware
		-- Only dependent applications should be removed. Targeted registration (regardless if master or depdendent application) will be removed by following DELETE statements
		DELETE FROM app_extauth ae
		WHERE ae.client_id = getSCID()
		AND ae.dev_id = vDeviceId
		AND ae.application_id IN (SELECT ap.id from mcore.applications ap WHERE ap.ph2 = vApplicationId);
	END IF;

	DELETE FROM app_extauth WHERE ROWID = vRowRec;

	mcsm.expireSession(pApplicationId => common_pck.cAPP_MOBILE, pUserId => getSCID());

  EXCEPTION
	WHEN no_data_found THEN
		sspkg.raiseError(cERR_InvalidDevice, NULL, pkgCtxId, myUnit);
  END unregisterDevice;

	PROCEDURE regNewActionKey(pActKey VARCHAR2, pValidUntil VARCHAR2, pClientId NUMBER)
	IS
		myunit CONSTANT VARCHAR2(30) := 'regNewActionKey';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId || ':' || pValidUntil);
		UPDATE client set ph6 = common_pck.hash(pActKey), ph3 = pValidUntil where id = pClientId;
	END regNewActionKey;

	PROCEDURE regNewActionKeyAT(pActKey VARCHAR2, pValidUntil VARCHAR2, pClientId NUMBER)
	IS
		myunit CONSTANT VARCHAR2(30) := 'regNewActionKeyAT';
		PRAGMA AUTONOMOUS_TRANSACTION;
	BEGIN
		regNewActionKey(pActKey, pValidUntil, pClientId);
		COMMIT;
	EXCEPTION
		WHEN sspkg.sysException THEN
			ROLLBACK;
			RAISE;
		WHEN OTHERS THEN
			ROLLBACK;
			sspkg.raiseOraError(pkgCtxId, myUnit);
	END regNewActionKeyAT;

	/*
	regNewActivationData - Generate new (external) authentication data and store it in persistent storage

	pClientID - Unique (persistent) user identifier - client.id
		pDeviceType - Device type for which authentication data is generated (mToken, Elba Mobile, ...)
		pActKey - Activation key which is registered
		pValidUntil - Date until activation key is valid
		pUserId - Alternative unique (nor persistent) user identifier which is used to autenticate user only once */
	PROCEDURE regNewActivationData(pClientId NUMBER, pDeviceType IN VARCHAR2, pActKey IN VARCHAR2, pUserId IN VARCHAR2, pValidUntil IN DATE, pExtAuthMethod IN VARCHAR2 DEFAULT NULL)
	IS
		myunit CONSTANT VARCHAR2(30) := 'regNewActivationData';
		vApplicationId app_extauth.application_id%TYPE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId || ':' || pDeviceType || ':' || pActKey || ':' || pUserId || ':' || pValidUntil || ':' || pExtAuthMethod);
		IF pDeviceType = common_pck.cAPP_MTOKEN THEN
			vApplicationId := common_pck.cAPP_THIN;

			DECLARE
				vActKey VARCHAR2(240);
				vAttribId CONSTANT VARCHAR2(40) := cMTokenActKey;
			BEGIN
				vActKey := common_pck.hash(UPPER(pActKey));
				slog.debug(pkgCtxId, myUnit, common_pck.cAPP_MTOKEN || ':' || 'INSERT ' || vAttribId);
				INSERT INTO client_details (client_id, attrib_id, data_vchar) VALUES (pClientId, cMTokenActKey, vActKey);
			EXCEPTION
				WHEN dup_val_on_index THEN
					slog.debug(pkgCtxId, myUnit, common_pck.cAPP_MTOKEN || ':' || 'UPDATE ' || vAttribId);
					UPDATE client_details
					   SET data_vchar = vActKey
					 WHERE client_id = pClientId AND attrib_id = vAttribId;
			END;

			DECLARE
				vValidUntil VARCHAR2(40);
				vAttribId CONSTANT VARCHAR2(40) := cMTokenActKeyValidUntil;
			BEGIN
				slog.debug(pkgCtxId, myUnit, common_pck.cAPP_MTOKEN || ':' || 'INSERT ' || vAttribId);
				vValidUntil := TO_CHAR(pValidUntil, common_pck.cDATETIME_MASK);
				INSERT INTO client_details (client_id, attrib_id, data_vchar) VALUES (pClientId, cMTokenActKeyValidUntil, vValidUntil);
			EXCEPTION
				WHEN dup_val_on_index THEN
					slog.debug(pkgCtxId, myUnit, common_pck.cAPP_MTOKEN || ':' || 'UPDATE ' || vAttribId);
					UPDATE client_details
					   SET data_vchar = vValidUntil
					 WHERE client_id = pClientId AND attrib_id = vAttribId;
			END;

			DECLARE
				vUserId VARCHAR2(240);
				vAttribId CONSTANT VARCHAR2(40) := cMTokenUserId;
			BEGIN
				slog.debug(pkgCtxId, myUnit, common_pck.cAPP_MTOKEN || ':' || 'INSERT ' || vAttribId);
				vUserId := common_pck.hash(pUserId);
				INSERT INTO client_details (client_id, attrib_id, data_vchar) VALUES (pClientId, cMTokenUserId, vUserId);
			EXCEPTION
				WHEN dup_val_on_index THEN
					slog.debug(pkgCtxId, myUnit, common_pck.cAPP_MTOKEN || ':' || 'UPDATE ' || vAttribId);
					UPDATE client_details
					   SET data_vchar = vUserId
					 WHERE client_id = pClientId AND attrib_id = vAttribId;
			END;

			slog.debug(pkgCtxId, myUnit, 'Populate client details done');

			IF pExtAuthMethod IS NOT NULL THEN
				slog.debug(pkgCtxId, myUnit, 'Register ExtAuthMethod for given one');
				DECLARE
					vExtAuthRecord ROWID;
					vExtAuthGroup CONSTANT mcore.generic_code_books.cb_val_2%TYPE := mcore.generic_code_books_pck.getExtAuthGroup(pExtAuthId => pExtAuthMethod);
				BEGIN
					slog.debug(pkgCtxId, myUnit, 'Retrieve existing app_extauth entry for ' || pClientId);
					SELECT ROWID
					INTO vExtAuthRecord
					FROM app_extauth
					WHERE client_id = pClientId
					AND application_id = vApplicationId;

					slog.debug(pkgCtxId, myUnit, 'Update existing app_extauth entry for ' || pClientId);
					update app_extauth
					SET extauth_id = pExtAuthMethod,
						dev_id = NULL,
						device_desc = NULL,
						date_of_registration = NULL,
						sec_key = NULL,
						valid = 0,
						ph0 = NULL,
						ph1 = vExtAuthGroup
					WHERE ROWID = vExtAuthRecord;

				EXCEPTION
					WHEN TOO_MANY_ROWS THEN
						DELETE FROM app_extauth WHERE client_id = pClientId AND application_id = vApplicationId;
						slog.debug(pkgCtxId, myUnit, 'Check for existing registrations ... found multiple registration! Clean up ... succedded!');

						slog.debug(pkgCtxId, myUnit, 'INSERT new app_extauth entry');
						INSERT INTO app_extauth (id, client_id, application_id, extauth_id, valid, ph1)
						VALUES (app_extauth_SEQ.NEXTVAL, pClientId, vApplicationId, pExtAuthMethod, 0, vExtAuthGroup);

					WHEN NO_DATA_FOUND THEN
						slog.debug(pkgCtxId, myUnit, 'Check for existing registrations ... not found! Inserting new auth data ...');
						-- 4.2 if not, insert new registration entry
						INSERT INTO app_extauth(id, client_id, application_id, extauth_id, valid, ph1)
						VALUES (app_extauth_SEQ.NEXTVAL, pClientId, vApplicationId, pExtAuthMethod, 0, vExtAuthGroup);
				END;
			END IF;

			slog.debug(pkgCtxId, myUnit, 'Try register new key ...');
			mtoken_plugin.regNewKey(pClientId => pClientId, pDeviceID => NULL, pSeedData => pActKey);
			slog.debug(pkgCtxId, myUnit, 'Try register new key ... done');

		ELSIF pDeviceType = common_pck.cAPP_MOBILE THEN
			vApplicationId := common_pck.cAPP_MOBILE;

			slog.debug(pkgCtxId, myUnit, common_pck.cAPP_MOBILE || ':' || 'DELETE cmELBAActKey, cmELBAActKeyValidUntil');
			DELETE FROM client_details WHERE client_id = pClientId AND ATTRIB_ID IN (cmELBAActKey, cmELBAActKeyValidUntil);
			slog.debug(pkgCtxId, myUnit, common_pck.cAPP_MTOKEN || ':' || 'INSERT cmELBAActKey, cMTokenUserId');
			INSERT INTO client_details (client_id, attrib_id, data_vchar) VALUES (pClientId, cmELBAActKey, common_pck.hash(pActKey));
			slog.debug(pkgCtxId, myUnit, common_pck.cAPP_MTOKEN || ':' || 'INSERT cMTokenActKey, cmELBAActKeyValidUntil');
			INSERT INTO client_details (client_id, attrib_id, data_vchar) VALUES (pClientId, cmELBAActKeyValidUntil, TO_CHAR(pValidUntil, common_pck.cDATETIME_MASK));
		END IF;

		slog.debug(pkgCtxId, myUnit, 'Completed');
	END regNewActivationData;

	PROCEDURE regNewActivationDataAT(pClientId NUMBER, pDeviceType IN VARCHAR2, pActKey IN VARCHAR2, pUserId IN VARCHAR2, pValidUntil IN DATE, pExtAuthMethod IN VARCHAR2 DEFAULT NULL)
	IS
		myunit CONSTANT VARCHAR2(30) := 'regNewActivationDataAT';
		PRAGMA AUTONOMOUS_TRANSACTION;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId || ':' || pDeviceType);
		regNewActivationData(pClientId, pDeviceType, pActKey, pUserId, pValidUntil, pExtAuthMethod);
		COMMIT;
		slog.debug(pkgCtxId, myUnit, 'Completed');
	EXCEPTION
		WHEN sspkg.sysException THEN
			ROLLBACK;
			RAISE;
		WHEN OTHERS THEN
			ROLLBACK;
			sspkg.raiseOraError(pkgCtxId, myUnit);
	END regNewActivationDataAT;
    
    FUNCTION random_string (
        p_length        IN NUMBER,
        p_custom_chars  IN VARCHAR2 DEFAULT 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    ) RETURN VARCHAR2 IS
        v_result VARCHAR2(4000);
        v_index NUMBER;
    BEGIN
        -- Generate random string
        FOR i IN 1 .. p_length LOOP
            v_index := DBMS_RANDOM.VALUE(1, LENGTH(p_custom_chars));
            v_result := v_result || SUBSTR(p_custom_chars, v_index, 1);
        END LOOP;
    
        RETURN v_result;
    END random_string;

	/* OLD VERSION, STILL USED FOR MOBILE APPLICATION. WILL BE DEPRECATED WHEN MOBILE AUTHENTICATION INTEGRATED WITH NEW CONCEPT */
	PROCEDURE generateNewActivationKey(
		pClientId client.id%TYPE,
		pActKey OUT VARCHAR2,
		pValidUntil OUT DATE,
		pAutonomousTransaction BOOLEAN DEFAULT TRUE,
		pSendKeyPerSMS IN BOOLEAN DEFAULT FALSE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'generateNewActivationKey';
		vUserId VARCHAR2(30);
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId);

		generateNewActivationKey(pClientId, pActKey, vUserId, pValidUntil, pAutonomousTransaction, pSendKeyPerSMS, common_pck.cAPP_MOBILE);

		slog.debug(pkgCtxId, myUnit, pActKey || ':' || TO_CHAR(pValidUntil,  common_pck.cDATETIME_MASK));

	END generateNewActivationKey;

	/* NEW VERSION - USE CLIENT_DETAILS TO STORE ACT. KEY and USER_ID */
	PROCEDURE generateNewActivationKey(
		pClientId client.id%TYPE,
		pActKey OUT VARCHAR2,
		pUserId OUT VARCHAR2,
		pValidUntil OUT DATE,
		pAutonomousTransaction BOOLEAN DEFAULT TRUE,
		pSendKeyPerSMS IN BOOLEAN DEFAULT FALSE,
		pDeviceType IN VARCHAR2)
	IS
		myunit CONSTANT VARCHAR2(30) := 'generateNewActivationKey2';
		vActKey VARCHAR2(30);
		vUserId VARCHAR2(30);

		vValidUntil DATE;

		vUserIdLength PLS_INTEGER;

	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId || ':' || pDeviceType);

		IF pClientId IS NULL THEN
            		sspkg.raiseError(common_pck.cERR_InvalidEndUserID, NULL, pkgCtxId, myunit);
        	END IF;

        	DECLARE
            		vEnabled NUMBER;
        	BEGIN

			SELECT enabled
            		INTO vEnabled
            		FROM client
            		WHERE id = pClientId;

            	IF vEnabled = 0 THEN
                	sspkg.raiseError('/Core/Auth/err/DisabledAccount', NULL, pkgCtxId, myunit);
            	END IF;

		EXCEPTION
			WHEN no_data_found THEN
				sspkg.raiseError(common_pck.cERR_UnknownEndUser, NULL, pkgCtxId, myunit);
		END;

		IF pDeviceType = common_pck.cAPP_MOBILE THEN
		BEGIN

			-- TODO: Parametrize ActKey length
            vActKey := random_string(NVL(sspkg.ReadInt(pkgCtxId || '/mElbaActKeyLength'),20), sspkg.ReadVChar(pkgCtxId || '/mElbaActKeyCharSet'));
            
			IF vmELBAActKeyDuration IS NULL THEN
				vmELBAActKeyDuration := NVL(sspkg.ReadInt(cAuthKeyDurationMADMAP), 10080);
			END IF;

			-- Specified by minutes!
			vValidUntil := SYSDATE + (vmELBAActKeyDuration/1440);

			IF pAutonomousTransaction THEN
				regNewActionKeyAT(vActKey, TO_CHAR(vValidUntil, common_pck.cDATETIME_MASK), pClientId);
				regNewActivationDataAT(pClientId => pClientId, pDeviceType => pDeviceType, pActKey => vActKey, pUserId => NULL, pValidUntil => vValidUntil);
			ELSE
				regNewActionKey(vActKey, TO_CHAR(vValidUntil,  common_pck.cDATETIME_MASK), pClientId);
				regNewActivationData(pClientId => pClientId, pDeviceType => pDeviceType, pActKey => vActKey, pUserId => NULL, pValidUntil => vValidUntil);
			END IF;

			IF sspkg.readBool('/Core/Auth/mElbaActKeyFormatOutput') THEN
				vActKey := mcore.util.formatString(
					pString => vActKey,
					pSegmentSize => NVL(sspkg.readInt('/Core/Auth/mElbaActKeySegmentSize'),4),
					pDelimiter => NVL(sspkg.readVchar(cmElbaActKeySegmentDelimiter),'-'));
			END IF;

			IF pSendKeyPerSMS THEN
				-- Remark: Comment out folowing code when SMSOTP plugin unavailable!
				DECLARE
					vLangId VARCHAR2(2) := 'bs';
				BEGIN
					-- SELECT lang INTO vUserLang FROM mcore.end_users WHERE id = pClientId;
					sendSMSMessage (
						clientID => pClientId,
						pMessage => mlang.trans(
										NVL(vLangId, 'bs'), 	-- User language
										cActivationCodeMsg, 	-- Message
										NVL(sspkg.readVchar(common_pck.cmElbaServiceName), common_pck.cmElbaServiceDefName), 	-- Value for <s0>
										vActKey, 	-- Value for <s1>
										TO_CHAR(vValidUntil,  common_pck.cDATETIME_MASK) -- Value for <s2>
									),
						pSendBefore => vValidUntil);
					slog.debug(pkgCtxId, myUnit, 'Data sent successfully');
					setClientDetails(pClientId => pClientId, pAttribId => 'MELBA_ACT_KEY_SENT', pData => TO_CHAR(SYSDATE, common_pck.cDATETIME_MASK));

				END;
			END IF;

		END;
		ELSIF pDeviceType = common_pck.cAPP_MTOKEN THEN
		BEGIN

			slog.debug(pkgCtxId, myUnit, 'ActKey parameters: ' || sspkg.ReadVChar(pkgCtxId || '/mTokenActKeyCharSet') || ':' || NVL(sspkg.ReadInt(pkgCtxId || '/mTokenActKeyLength'),20) || ':' || NVL(sspkg.ReadInt(pkgCtxId || '/mTokenUserIdLength'), 6));

            vActKey := random_string(NVL(sspkg.ReadInt(pkgCtxId || '/mTokenActKeyLength'),20), sspkg.ReadVChar(pkgCtxId || '/mTokenActKeyCharSet'));

			dbms_random.seed(DBMS_CRYPTO.RANDOMBYTES(16));
			vUserIdLength := NVL(sspkg.ReadInt(pkgCtxId || '/mTokenUserIdLength'), 6);

			vUserId := TRUNC(dbms_random.value(POWER(10, vUserIdLength-1), POWER(10, vUserIdLength)-1));

			slog.debug(pkgCtxId, myUnit, 'ActKey: ' || vActKey || ':' || vUserId);

			IF vmTokenActKeyDuration IS NULL THEN
				vmTokenActKeyDuration := NVL(sspkg.ReadInt(cmTokenAuthKeyDurationMADMAP), 10080);
			END IF;

			-- Specified by minutes!
			vValidUntil := SYSDATE + (vmTokenActKeyDuration/1440);

			-- Check license
			slog.debug(pkgCtxId, myUnit, 'Check license ...');
			checkLicense(pApplicationId => common_pck.cAPP_MTOKEN, pClientId => pClientId);

			IF pAutonomousTransaction THEN
				slog.debug(pkgCtxId, myUnit, 'regNewActivationDataAT ...');
				regNewActivationDataAT(pClientId => pClientId, pDeviceType => pDeviceType, pActKey => vActKey, pUserId => vUserId, pValidUntil => vValidUntil);
				slog.debug(pkgCtxId, myUnit, 'regNewActivationDataAT ... done');
			ELSE
				slog.debug(pkgCtxId, myUnit, 'regNewActivationData ...');
				regNewActivationData(pClientId => pClientId, pDeviceType => pDeviceType, pActKey => vActKey, pUserId => vUserId, pValidUntil => vValidUntil);
				slog.debug(pkgCtxId, myUnit, 'regNewActivationData ... done');
			END IF;

			IF sspkg.readBool('/Core/Auth/mTokenActKeyFormatOutput') THEN
				vActKey := mcore.util.formatString(
					pString => vActKey,
					pSegmentSize => NVL(sspkg.readInt('/Core/Auth/mTokenActKeySegmentSize'),4),
					pDelimiter => NVL(sspkg.readVchar(cmTokenActKeySegmentDelimiter),'-'));
			END IF;


			IF pSendKeyPerSMS THEN
				-- Remark: Comment out folowing code when SMSOTP plugin unavailable!
				DECLARE
					vLangId VARCHAR2(2) := 'bs';
					vSMSMessage VARCHAR2(512);
				BEGIN
					CASE sspkg.ReadVChar('/Core/Auth/Plugin/mTOKEN/SMS_Notification_TYPE')
						WHEN 'ACT_KEY' THEN vSMSMessage := '/Core/Auth/Plugin/mTOKEN/msg/YourActivationCode';
						WHEN 'USER_ID' THEN vSMSMessage := '/Core/Auth/Plugin/mTOKEN/msg/YourUserId';
						WHEN 'DEAR' THEN vSMSMessage := '/Core/Auth/Plugin/mTOKEN/msg/DearUser';
						WHEN 'ALL' THEN vSMSMessage := '/Core/Auth/Plugin/mTOKEN/msg/YourActivationCodeAndUserId';
					END CASE;

					IF vSMSMessage IS NOT NULL THEN
						sendSMSMessage (
							clientID => pClientId,
							pMessage => mlang.trans(
											NVL(vLangId, 'bs'), 	-- User language
											vSMSMessage, 	-- Message
											NVL(sspkg.readVchar('/Core/Auth/Plugin/mTOKEN/Name'), common_pck.cAPP_MTOKEN), 	-- Value for <s0>
											vActKey, 	-- Value for <s1>
											TO_CHAR(vValidUntil,  common_pck.cDATETIME_MASK), -- Value for <s2>
											vUserId-- Value for <s3>
											),
							pSendBefore => vValidUntil);
						slog.debug(pkgCtxId, myUnit, 'Data sent successfully');
						setClientDetails(pClientId => pClientId, pAttribId => 'MTOKEN_ACT_KEY_SENT', pData => TO_CHAR(SYSDATE, common_pck.cDATETIME_MASK));

					END IF;
				END;
			END IF;
		END;
		ELSE
			sspkg.raiseError(cERR_UnknownDeviceType, NULL, pkgCtxId, myUnit);
		END IF;

		slog.debug(pkgCtxId, myUnit, 'Key generated. Populate OUT values ...');

		pActKey := vActKey;
		pUserId := vUserId;
		pValidUntil := vValidUntil;

		slog.debug(pkgCtxId, myUnit, 'Key generated. Populate OUT values ... done');

	END generateNewActivationKey;



   	PROCEDURE generateNewActivationKey(
		pClientId client.id%TYPE,
		pActKey OUT VARCHAR2,
		pUserId OUT VARCHAR2,
		pValidUntil OUT DATE,
		pAutonomousTransaction INTEGER DEFAULT 1,
		pSendKeyPerSMS IN INTEGER DEFAULT 0,
		pDeviceType IN VARCHAR2)

        IS
        pAutonomousTransaction_bool BOOLEAN;
        pSendKeyPerSMS_bool BOOLEAN;

        BEGIN

         IF pAutonomousTransaction = 1 THEN
           pAutonomousTransaction_bool := TRUE;
         ELSE  pAutonomousTransaction_bool :=FALSE;
         END IF;

         IF pSendKeyPerSMS = 1 THEN
            pSendKeyPerSMS_bool :=TRUE;
         ELSE  pSendKeyPerSMS_bool :=FALSE;
         END IF;

        generateNewActivationKey(
            pClientId=>pClientId,
            pActKey=>pActKey,
            pUserId=>pUserId,
            pValidUntil=>pValidUntil,
            pAutonomousTransaction=>pAutonomousTransaction_bool,
            pSendKeyPerSMS=>pSendKeyPerSMS_bool,
            pDeviceType=>pDeviceType);

    END;


  /* ONLY applicable for mELBA activation key, generated using active app. session */
  PROCEDURE generateNewActivationKey(pActKey OUT VARCHAR2, pValidUntil OUT DATE)
  IS
    myunit CONSTANT VARCHAR2(30) := 'generateNewActivationKey3';
	vUserId VARCHAR2(30);
  BEGIN
	slog.debug(pkgCtxId, myUnit, getSCID() || ':' || common_pck.cAPP_MOBILE);
    IF NOT isLogged THEN
        sspkg.raiseError(cERR_NotPermited, NULL, pkgCtxId, myUnit);
    END IF;

	IF getExistingActivationKey() IS NOT NULL THEN
		sspkg.raiseError(cERR_ValidActKeyAlreadyExists, NULL, pkgCtxId, myUnit);
	END IF;

	vmELBAActKeyDuration := NVL(sspkg.ReadInt(cAuthKeyDurationThin), 15);

	generateNewActivationKey(getSCID(), pActKey, vUserId, pValidUntil, TRUE, FALSE, common_pck.cAPP_MOBILE);
	vmELBAActKeyDuration := NULL;
  END generateNewActivationKey;

  FUNCTION getExistingActivationKey(pClientId IN client.id%TYPE, pDeviceType IN VARCHAR2)
  RETURN DATE
  IS
    myunit CONSTANT VARCHAR2(30) := 'getExistingActivationKey';
	vValidUntil DATE;
  BEGIN
	IF pDeviceType = common_pck.cAPP_MOBILE THEN
		SELECT MAX(valid_until)
		INTO vValidUntil
		FROM
		(SELECT TO_DATE(ph3, common_pck.cDATETIME_MASK) valid_until
		FROM client
		WHERE id = pClientId
		UNION
		SELECT TO_DATE(data_vchar, common_pck.cDATETIME_MASK)
		FROM client_details
		WHERE client_id = pClientId AND attrib_id = cmELBAActKeyValidUntil);
	ELSIF pDeviceType = common_pck.cAPP_MTOKEN THEN
		SELECT TO_DATE(data_vchar, common_pck.cDATETIME_MASK)
		INTO vValidUntil
		FROM client_details
		WHERE client_id = pClientId AND attrib_id = cMTokenActKeyValidUntil;
	ELSE
		sspkg.raiseError(cERR_UnknownDeviceType, NULL, pkgCtxId, myUnit);
	END IF;

	IF NVL(vValidUntil, common_pck.cDATE_PAST) < SYSDATE THEN
		RETURN NULL;
	END IF;
	RETURN vValidUntil;

  EXCEPTION
	WHEN no_data_found THEN
		RETURN NULL;
  END getExistingActivationKey;

  FUNCTION getExistingActivationKey
  RETURN DATE
  IS
    myunit CONSTANT VARCHAR2(30) := 'getExistingActivationKey2';
  BEGIN
    IF NOT isLogged THEN
        sspkg.raiseError(cERR_NotPermited, NULL, pkgCtxId, myUnit);
    END IF;

	RETURN getExistingActivationKey(getSCID(), common_pck.cAPP_MOBILE);
  END getExistingActivationKey;

  -- Require session
  FUNCTION getListOfAuthorizedDevices
  RETURN sys_refcursor IS
	myunit CONSTANT VARCHAR2(30) := 'getListOfAuthorizedDevices';
	res sys_refcursor;
  BEGIN
    IF NOT isLogged THEN
        sspkg.raiseError(cERR_NotPermited, NULL, pkgCtxId, myUnit);
    END IF;

	OPEN res FOR
	 SELECT ae.id,
	 DECODE(ae.application_id, common_pck.cAPP_MOBILE, sspkg.readVChar('/App-DB/MMobile/ServiceName'), ae.application_id) application_id,
	 ae.dev_id, ae.device_desc, ae.date_of_registration
	 FROM app_extauth ae JOIN mcore.applications a ON (ae.application_id = a.id)
	 WHERE ae.client_id = getSCID()
	 AND ae.valid = 1
	 AND NVL(a.ph0, '0') = '1'
	 ORDER BY ae.date_of_registration DESC;
	RETURN res;
  END getListOfAuthorizedDevices;

  -- Does not require session
  FUNCTION getListOfAuthorizedDevices(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
      sessLogLevel INTEGER := NULL, langId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pDeviceId IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL)
  RETURN sys_refcursor IS
	myunit CONSTANT VARCHAR2(30) := 'getListOfAuthorizedDevices2';
	res sys_refcursor;
	vLoginSuccess BOOLEAN;
	vClient vClientType;
	vPwdExpireInDays PLS_INTEGER;
	vLangId CONSTANT VARCHAR2(40) := langId;
  BEGIN
	slog.debug(pkgCtxId, myUnit, vusername ||':' || ipAddress || ':' || langId);

	BEGIN
		SELECT rowid, id, enabled, password, password_enabled, valid_from, valid_to, NULL, NULL, NULL, ph4, password_change_required, password_cs
		INTO vClient
		FROM client
		WHERE lower(username) = lower(vusername)
		AND vusername is NOT NULL;
	EXCEPTION
		WHEN no_data_found THEN
			sspkg.raiseError(cERR_WrongCredentials, mlang.trans(vLangId, cERR_WrongCredentials), pkgCtxId, myunit);
	END;

	vLoginSuccess := basicAccountCheck(
		pUsername => vusername,
		pPassword => vpassword,
		ipAddress => ipAddress,
		host => host,
		sessLogLevel => sessLogLevel,
		langId => langId,
		pApplicationId => common_pck.cAPP_MOBILE,
		pInstallationId => pInstallationId,
		pClient => vClient,
		pPasswordExpireInDays => vPwdExpireInDays,
		pNOnce => pNOnce,
		pDeviceId => pDeviceId,
		pClientExtAuthId => NULL,
		pPasswordHashed => pPasswordHashed,
		pAppVersionId => pAppVersionId,
		pOSVersionId => pOSVersionId);

    IF NOT vLoginSuccess THEN
      sspkg.raiseError(cERR_WrongCredentials, mlang.trans(vLangId, cERR_WrongCredentials), pkgCtxId, myunit);
    END IF;

	slog.debug(pkgCtxId, myUnit, 'Basic account check succeedded!');

	OPEN res FOR
	SELECT ae.id,
		DECODE(ae.application_id, common_pck.cAPP_MOBILE, sspkg.readVChar('/App-DB/MMobile/ServiceName'), ae.application_id) application_id,
		ae.dev_id, ae.device_desc, ae.date_of_registration
	 FROM app_extauth ae JOIN mcore.applications a ON (ae.application_id = a.id)
	 WHERE ae.client_id = vClient.id
	 AND ae.valid = 1
	 AND NVL(a.ph0, '0') = '1'
	 ORDER BY ae.date_of_registration DESC;

	RETURN res;
  END getListOfAuthorizedDevices;

  PROCEDURE unregisterDevice(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
      sessLogLevel INTEGER := NULL, langId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL, pAppExtAuthId app_extauth.id%TYPE,
	  pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pDeviceId IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL) IS
	myunit CONSTANT VARCHAR2(30) := 'unregisterDevice';
    vRowRec rowid;
	vLoginSuccess BOOLEAN;
	vClient vClientType;
	vPwdExpireInDays PLS_INTEGER;
	vLangId CONSTANT VARCHAR2(40) := langId;

	vApplicationId app_extauth.application_id%TYPE;
	vDeviceId app_extauth.dev_id%TYPE;

	PRAGMA AUTONOMOUS_TRANSACTION;
  BEGIN
	slog.debug(pkgCtxId, myUnit, vusername ||':' || ipAddress || ':' || pInstallationId ||':'||pAppExtAuthId || ':' || langId);

	BEGIN
		SELECT rowid, id, enabled, password, password_enabled, valid_from, valid_to, NULL, NULL, NULL, ph4, password_change_required, password_cs
		INTO vClient
		FROM client
		WHERE lower(username) = lower(vusername)
		AND vusername is NOT NULL;
	EXCEPTION
		WHEN no_data_found THEN
			ROLLBACK;
			sspkg.raiseError(cERR_WrongCredentials, mlang.trans(vLangId, cERR_WrongCredentials), pkgCtxId, myunit);
	END;

	vLoginSuccess := basicAccountCheck(
		pUsername =>vusername,
		pPassword =>vpassword,
		ipAddress =>ipAddress,
		host =>host,
		sessLogLevel =>sessLogLevel,
		langId =>vLangId,
		pApplicationId =>common_pck.cAPP_THIN,
		pInstallationId =>pInstallationId,
		pClient => vClient,
		pPasswordExpireInDays => vPwdExpireInDays,
		pNOnce => pNOnce,
		pDeviceId => pDeviceId,
		pClientExtAuthId => NULL,
		pPasswordHashed => pPasswordHashed,
		pAppVersionId => pAppVersionId,
		pOSVersionId => pOSVersionId);

    IF NOT vLoginSuccess THEN
	  ROLLBACK;
      sspkg.raiseError(cERR_WrongCredentials, mlang.trans(vLangId, cERR_WrongCredentials), pkgCtxId, myunit);
    END IF;

	slog.debug(pkgCtxId, myUnit, 'Basic account check succeedded!');

	SELECT ROWID, application_id, dev_id
	INTO vRowRec, vApplicationId, vDeviceId
	FROM app_extauth
	where id = pAppExtAuthId
	AND client_id = vClient.id;

	IF common_pck.isAppDeviceAware(vApplicationId) = 1 THEN
		DELETE FROM app_extauth WHERE client_id = vClient.id AND dev_id = vDeviceId;
	END IF;


	DELETE FROM app_extauth WHERE ROWID = vRowRec;

	COMMIT;

	mcsm.expireSession(pApplicationId => common_pck.cAPP_MOBILE, pUserId => vClient.id);
  EXCEPTION
	WHEN no_data_found THEN
		ROLLBACK;
		sspkg.raiseError(cERR_InvalidDevice, mlang.trans(vLangId, cERR_InvalidDevice), pkgCtxId, myUnit);
  END unregisterDevice;

	PROCEDURE checkLicense(pApplicationId VARCHAR2 := getApplicationId(), pClientId client.id%TYPE := getClientId())
	IS
		myunit CONSTANT VARCHAR2(30) := 'checkLicense';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pApplicationId || ':' || pClientId);
		IF common_pck.isLicenseRequired(pApplicationID=>pApplicationId) = 1 THEN
			slog.debug(pkgCtxId, myUnit, 'Application require license');
			IF NOT hasLicense(	pApplicationId => pApplicationId, pClientId => pClientId) THEN
				sspkg.raiseError(cERR_NoLicense, NULL, pkgCtxId, myunit);
			END IF;
		END IF;
	END checkLicense;

	PROCEDURE registerAppAuthMethod(
		pClientId app_extauth.client_id%TYPE,
		pApplicationId app_extauth.application_id%TYPE,
		pExtAuthId app_extauth.extauth_id%TYPE,
		pDeviceId app_extauth.dev_id%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'registerAppAuthMethod';
		vExtAuthRec ROWID;
		vAppDeviceAwareness PLS_INTEGER;
		vExtAuthGroup CONSTANT mcore.generic_code_books.cb_val_2%TYPE := mcore.generic_code_books_pck.getExtAuthGroup(pExtAuthId => pExtAuthId);
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId || ':' || pApplicationId || ':' || pExtAuthId || ':' || pDeviceId);

		vAppDeviceAwareness := common_pck.isAppDeviceAware(pApplicationId);
		slog.debug(pkgCtxId, myUnit, 'vAppDeviceAwareness:' || vAppDeviceAwareness);

		IF vAppDeviceAwareness <> 0 THEN
			sspkg.raiseError(cERR_InternalError, NULL, pkgCtxId, myunit);
		END IF;

		SELECT ROWID
		INTO vExtAuthRec
		FROM app_extauth
		where client_id = pClientId
		and application_id = pApplicationId;

		slog.debug(pkgCtxId, myUnit, 'Current vExtAuthRec:' || vExtAuthRec);

		-- update existing
		UPDATE app_extauth SET extauth_id = pExtAuthId, valid = 1, ph1 = vExtAuthGroup WHERE ROWID = vExtAuthRec;

		slog.debug(pkgCtxId, myUnit, 'app_extauth updated');
	EXCEPTION
		WHEN no_data_found THEN
			-- new user
			INSERT INTO app_extauth(id, client_id, application_id, extauth_id, dev_id, date_of_registration, valid, ph1, ph5)
			VALUES (app_extauth_SEQ.NEXTVAL, pClientId, pApplicationId, pExtAuthId, pDeviceId, SYSDATE, 1, vExtAuthGroup, cDevicePasswordAuthorized);
		WHEN too_many_rows THEN
			-- clean up
			DELETE FROM app_extauth WHERE client_id = pClientId AND application_id = pApplicationId;
			-- register new
			INSERT INTO app_extauth(id, client_id, application_id, extauth_id, dev_id, date_of_registration, valid, ph1, ph5)
			VALUES (app_extauth_SEQ.NEXTVAL, pClientId, pApplicationId, pExtAuthId, pDeviceId, SYSDATE, 1, vExtAuthGroup, cDevicePasswordAuthorized);
	END registerAppAuthMethod;

	PROCEDURE deregisterAppAuthMethod(
		pClientId app_extauth.client_id%TYPE,
		pApplicationId app_extauth.application_id%TYPE)
	IS
		myunit CONSTANT VARCHAR2(30) := 'deregisterAppAuthMethod';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId || ':' || pApplicationId);

		update app_extauth
		   set valid = 0
		 where client_id = pClientId
		   and application_id = pApplicationId;

	END deregisterAppAuthMethod;

	/* Required for nOnce functionality */
	FUNCTION getPassword(pUsername client.username%TYPE)
	RETURN client.password%TYPE IS
		myunit CONSTANT VARCHAR2(30) := 'getPassword';
		vPassword client.password%TYPE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pUsername);

		IF pUsername IS NULL THEN
			RETURN NULL;
		END IF;

		SELECT PASSWORD
		INTO vPassword
		  FROM client
		 WHERE lower(username) = lower(pUsername);

		RETURN vPassword;
	EXCEPTION
		WHEN no_data_found THEN
		    slog.error(pkgCtxId, myunit, cERR_NO_SUCH_USER || ':' || pUsername);
			sspkg.raiseError(cERR_NO_SUCH_USER, 'User "'||pUsername||'" does NOT exist', pkgCtxId, myunit);
	END getPassword;

	PROCEDURE setClientDetails(pClientId NUMBER, pAttribId VARCHAR2, pData VARCHAR2)
	IS
		cg$rec     cg$client_details.cg$row_type;
		cg$ind     cg$client_details.cg$ind_type;

		pMSG VARCHAR2(512);
		pERROR VARCHAR2(1);
		pMSG_TYPE VARCHAR2(3);
		pMSGID INTEGER;
		pLOC VARCHAR2(2000 BYTE);
		v_cg_result BOOLEAN;
		myunit CONSTANT VARCHAR2(30) := 'setClientDetails';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId || ':' || pAttribId || ':' || pData);

		cg$rec.CLIENT_ID := pClientId;
		cg$rec.ATTRIB_ID := pAttribId;
		cg$client_details.slct(cg$rec);

		cg$rec.data_vchar := pData;
		cg$ind.data_vchar := TRUE;

		cg$client_details.upd(cg$rec, cg$ind, TRUE);
		slog.debug(pkgCtxId, myUnit, 'Completed');
	EXCEPTION
		-- Fetched also when record does not exists -- should try insert !
		WHEN cg$errors.cg$error THEN
		-- Retrieve error message to determine error cause !
			v_cg_result := cg$errors.pop(msg => pMSG
				,error => pERROR
				,msg_type => pMSG_TYPE
				,msgid => pMSGID
				,loc => pLOC);
			-- If API ERROR with code 100 - no_data_found, then ...
			IF pERROR = 'E' AND pMSG_TYPE = 'ORA' AND pMSGID = 100 THEN
				slog.debug(pkgCtxId, myUnit, 'No data found');
				-- Clear errors and ...
				cg$errors.clear;
				-- Try to insert new one !
				cg$rec.data_vchar := pData;
				slog.debug(pkgCtxId, myUnit, 'Insert new data');
				cg$client_details.ins(cg$rec => cg$rec, cg$ind => cg$ind, do_ins => TRUE);
				slog.debug(pkgCtxId, myUnit, 'Completed');
			ELSE
				cg$errors.raise_failure;
			END IF;
	END setClientDetails;

	FUNCTION getClientDetail(pClientId NUMBER, pAttribId VARCHAR2) RETURN VARCHAR2 IS
		cg$rec     cg$client_details.cg$row_type;

		pMSG VARCHAR2(512);
		pERROR VARCHAR2(1);
		pMSG_TYPE VARCHAR2(3);
		pMSGID INTEGER;
		pLOC VARCHAR2(2000 BYTE);
		v_cg_result BOOLEAN;
		myunit CONSTANT VARCHAR2(30) := 'getClientDetail';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId || ':' || pAttribId);
		cg$rec.CLIENT_ID := pClientId;
		cg$rec.ATTRIB_ID := pAttribId;
		cg$client_details.slct(cg$rec);
		slog.debug(pkgCtxId, myUnit, 'Found data');
		RETURN cg$rec.data_vchar;
	EXCEPTION
		-- Fetched also when record does not exists -- should try insert !
		WHEN cg$errors.cg$error THEN
		-- Retrieve error message to determine error cause !
			v_cg_result := cg$errors.pop(msg => pMSG
				,error => pERROR
				,msg_type => pMSG_TYPE
				,msgid => pMSGID
				,loc => pLOC);
			-- If API ERROR with code 100 - no_data_found, then ...
			IF pERROR = 'E' AND pMSG_TYPE = 'ORA' AND pMSGID = 100 THEN
				slog.debug(pkgCtxId, myUnit, 'No data found');
				-- Clear errors and ...
				cg$errors.clear;
				-- Try to insert new one !
				RETURN NULL;
			ELSE
				cg$errors.raise_failure;
			END IF;
	END getClientDetail;

	PROCEDURE MarkForAutoDataSend(pClientId NUMBER) IS
		PRAGMA AUTONOMOUS_TRANSACTION;
		myunit CONSTANT VARCHAR2(30) := 'MarkForAutoDataSend';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId);
		IF getClientDetail(pClientId, 'AUTOSENT_AUTH_DATA') = '0' THEN
			slog.debug(pkgCtxId, myUnit, 'Enable data sent');
			setClientDetails(pClientId => pClientId, pAttribId => 'AUTOSENT_AUTH_DATA', pData => '1');
			setClientDetails(pClientId => pClientId, pAttribId => 'AUTOSENT_AUTH_DATA_DATE_REQUESTED', pData => TO_CHAR(SYSDATE, common_pck.cDATETIME_MASK));
		ELSE
			slog.debug(pkgCtxId, myUnit, 'Disable data sent');
			setClientDetails(pClientId => pClientId, pAttribId => 'AUTOSENT_AUTH_DATA', pData => '0');
		END IF;
		slog.debug(pkgCtxId, myUnit, 'Completed');
		COMMIT;
	EXCEPTION
		WHEN OTHERS THEN
			ROLLBACK;
			RAISE;
	END MarkForAutoDataSend;

 PROCEDURE SendAuthenticationData(pClientId IN VARCHAR2 DEFAULT '%') IS
	CURSOR c IS
		SELECT client_id, username, username_sent, password_sent, melba_act_key_sent, mtoken_act_key_sent, autosent_auth_data_req FROM
		(SELECT c.id client_id, c.username,
		NVL(MAX(DECODE(td.attrib_id, 'AUTOSENT_AUTH_DATA', td.data_vchar, NULL)), '0') AUTOSENT_AUTH_DATA,
		NVL(MAX(DECODE(td.attrib_id, 'AUTOSENT_AUTH_DATA_DATE_REQUESTED', TO_DATE(td.data_vchar, common_pck.cDATETIME_MASK), NULL)), common_pck.cDATE_FUTURE)  AUTOSENT_AUTH_DATA_REQ,
		NVL(MAX(DECODE(td.attrib_id, 'USERNAME_SENT', TO_DATE(td.data_vchar, common_pck.cDATETIME_MASK), NULL)), common_pck.cDATE_PAST) USERNAME_SENT,
		NVL(MAX(DECODE(td.attrib_id, 'PASSWORD_SENT', TO_DATE(td.data_vchar, common_pck.cDATETIME_MASK), NULL)), common_pck.cDATE_PAST) PASSWORD_SENT,
		NVL(MAX(DECODE(td.attrib_id, 'MELBA_ACT_KEY_SENT', TO_DATE(td.data_vchar, common_pck.cDATETIME_MASK), NULL)), common_pck.cDATE_PAST) MELBA_ACT_KEY_SENT,
		NVL(MAX(DECODE(td.attrib_id, 'MTOKEN_ACT_KEY_SENT', TO_DATE(td.data_vchar, common_pck.cDATETIME_MASK), NULL)), common_pck.cDATE_PAST) MTOKEN_ACT_KEY_SENT
		FROM client c
		LEFT JOIN client_details td on (td.client_id = c.id)
		WHERE TO_CHAR(c.id) LIKE TRIM(pClientId)
		AND EXISTS (SELECT NULL FROM mcore.action_grants where user_id = c.id and valid = 1)
		GROUP BY c.id, c.username)
		WHERE autosent_auth_data = '1';

	vDateSent DATE;
	myunit CONSTANT VARCHAR2(30) := 'SendAuthenticationData';

	isSuccess BOOLEAN := TRUE;
    vPreferredComunication VARCHAR2(80);
	vCheckPreferredCommunication BOOLEAN;

 BEGIN
	slog.debug(pkgCtxId, myUnit, pClientId);
	vCheckPreferredCommunication := sspkg.readBool(pkgCtxId || '/CheckPreferredCommunicationWithClient');
	FOR i IN c LOOP
		isSuccess := TRUE;
		IF i.username_sent < i.autosent_auth_data_req THEN

            slog.debug(pkgCtxId, myUnit, 'Sent username for client ' || i.client_id || ':' || i.username);

            IF vCheckPreferredCommunication THEN

                vPreferredComunication := auth.getClientDetail(i.client_id, 'PREFERRED_CLIENT_COMMUNICATION');

                IF vPreferredComunication = 'SMS' THEN

                    slog.debug(pkgCtxId, myUnit, 'Send username per SMS');

                    DECLARE
                        vLangId VARCHAR2(2) := 'bs';
                    BEGIN
                        sendSMSMessage (
                        clientID => i.client_id,
                        pMessage => mlang.trans(
                                        vLangId, 	-- User language
                                        cSendUsernameMsg, i.username)	-- Message
                                    );
                        slog.debug(pkgCtxId, myUnit, 'Data sent successfully');
                        setClientDetails(pClientId => i.client_id, pAttribId => 'USERNAME_SENT', pData => TO_CHAR(SYSDATE, common_pck.cDATETIME_MASK));

                    EXCEPTION
                        WHEN OTHERS THEN
                            slog.error(pkgCtxId, myUnit, 'Exception during data (username) sent for client ' || i.client_id);
                            isSuccess := FALSE;
                    END;

                ELSE
                    BEGIN
                        slog.debug(pkgCtxId, myUnit, 'Send username per EMAIL');

                        vDateSent := mcore.SendUserNamePerMail(pClientId => i.client_id, pUsername => i.username);
                        IF vDateSent IS NOT NULL THEN
                            -- all ok
                            slog.debug(pkgCtxId, myUnit, 'Data sent successfully');
                            setClientDetails(pClientId => i.client_id, pAttribId => 'USERNAME_SENT', pData => TO_CHAR(vDateSent, common_pck.cDATETIME_MASK));
                        ELSE
                            slog.error(pkgCtxId, myUnit, 'Error during data (username) sent for client ' || i.client_id);
                            isSuccess := FALSE;
                        END IF;
                    EXCEPTION
                        WHEN OTHERS THEN
                            slog.error(pkgCtxId, myUnit, 'Exception during data (username) sent for client ' || i.client_id);
                            isSuccess := FALSE;
                    END;
                END IF;

            ELSE

                BEGIN
                    slog.debug(pkgCtxId, myUnit, 'Send username per EMAIL');

                    vDateSent := mcore.SendUserNamePerMail(pClientId => i.client_id, pUsername => i.username);
                    IF vDateSent IS NOT NULL THEN
                        -- all ok
                        slog.debug(pkgCtxId, myUnit, 'Data sent successfully');
                        setClientDetails(pClientId => i.client_id, pAttribId => 'USERNAME_SENT', pData => TO_CHAR(vDateSent, common_pck.cDATETIME_MASK));
                    ELSE
                        slog.error(pkgCtxId, myUnit, 'Error during data (username) sent for client ' || i.client_id);
                        isSuccess := FALSE;
                    END IF;
                EXCEPTION
                    WHEN OTHERS THEN
                        slog.error(pkgCtxId, myUnit, 'Exception during data (username) sent for client ' || i.client_id);
                        isSuccess := FALSE;
                END;

            END IF;


		ELSE
			slog.debug(pkgCtxId, myUnit, 'Username for client ' || i.client_id || ':' || i.username || ' already sent. Skip!');
		END IF;

		IF i.password_sent < i.autosent_auth_data_req THEN
			BEGIN
				slog.debug(pkgCtxId, myUnit, 'Sent password for client ' || i.client_id || ':' || i.username);
				vDateSent := SendPasswordPerSMS(pClientId => i.client_id);
				IF vDateSent IS NOT NULL THEN
					-- all ok
					slog.debug(pkgCtxId, myUnit, 'Data sent successfully');
					setClientDetails(pClientId => i.client_id, pAttribId => 'PASSWORD_SENT', pData => TO_CHAR(vDateSent, common_pck.cDATETIME_MASK));
				ELSE
					slog.error(pkgCtxId, myUnit, 'Error during data (password) sent for client ' || i.client_id);
					isSuccess := FALSE;
				END IF;
			EXCEPTION
				WHEN OTHERS THEN
					slog.error(pkgCtxId, myUnit, 'Exception during data (password) sent for client ' || i.client_id);
					isSuccess := FALSE;
			END;
		ELSE
			slog.debug(pkgCtxId, myUnit, 'Password for client ' || i.client_id || ':' || i.username || ' already sent. Skip!');
		END IF;

		slog.debug(pkgCtxId, myUnit, 'Check license for ' || i.client_id || ':' || common_pck.cAPP_MOBILE);
		IF hasLicense(pApplicationId => common_pck.cAPP_MOBILE, pClientId => i.client_id)
		THEN
			slog.debug(pkgCtxId, myUnit, 'Check license for ' || i.client_id || ':' || common_pck.cAPP_MOBILE || ' ... has license');

			IF i.melba_act_key_sent < i.autosent_auth_data_req THEN
				DECLARE
					vUserTemplateAppSelected BOOLEAN;
				BEGIN
					vUserTemplateAppSelected := mcore.authorization_pck.isUserTemplateAppSelected(pClientId => i.client_id, pApplicationId => common_pck.cAPP_MOBILE);

					IF vUserTemplateAppSelected THEN
						slog.debug(pkgCtxId, myUnit, 'User has mELBA checkbox selected');

						DECLARE
							vActKey VARCHAR2(80);
							vUserId VARCHAR2(80);
							vValidUntil DATE;
						BEGIN
							slog.debug(pkgCtxId, myUnit, 'Sent activation key (mELBA) for client ' || i.client_id || ':' || i.username);
							generateNewActivationKey(
								pClientId => i.client_id,
								pActKey => vActKey,
								pUserId => vUserId,
								pValidUntil => vValidUntil,
								pAutonomousTransaction => TRUE,
								pSendKeyPerSMS => TRUE,
								pDeviceType => common_pck.cAPP_MOBILE);

						EXCEPTION
							WHEN OTHERS THEN
								slog.error(pkgCtxId, myUnit, 'Error during data sent for client ' || i.client_id);
								isSuccess := FALSE;
						END;
					END IF;
				END;
			ELSE
				slog.debug(pkgCtxId, myUnit, 'Act. key for mELBA for client ' || i.client_id || ':' || i.username || ' already sent. Skip!');
			END IF;
		END IF;

		slog.debug(pkgCtxId, myUnit, 'Check license for ' || i.client_id || ':' || common_pck.cAPP_MTOKEN);
		IF hasLicense(pApplicationId => common_pck.cAPP_MTOKEN, pClientId => i.client_id)
		THEN
			slog.debug(pkgCtxId, myUnit, 'Check license for ' || i.client_id || ':' || common_pck.cAPP_MTOKEN || ' ... has license');
			IF i.mtoken_act_key_sent < i.autosent_auth_data_req THEN
				DECLARE
					vUserTemplateAppSelected BOOLEAN;
				BEGIN
					vUserTemplateAppSelected := mcore.authorization_pck.isUserTemplateAppSelected(pClientId => i.client_id, pApplicationId => common_pck.cAPP_THIN);

					IF vUserTemplateAppSelected THEN
						slog.debug(pkgCtxId, myUnit, 'User has mTOKEN checkbox selected');

						DECLARE
							vActKey VARCHAR2(80);
							vUserId VARCHAR2(80);
							vValidUntil DATE;
						BEGIN
							slog.debug(pkgCtxId, myUnit, 'Sent activation key (mTOKEN) for client ' || i.client_id || ':' || i.username);
							generateNewActivationKey(
								pClientId => i.client_id,
								pActKey => vActKey,
								pUserId => vUserId,
								pValidUntil => vValidUntil,
								pAutonomousTransaction => TRUE,
								pSendKeyPerSMS => TRUE,
								pDeviceType => common_pck.cAPP_MTOKEN);

						EXCEPTION
							WHEN OTHERS THEN
								slog.error(pkgCtxId, myUnit, 'Error during data sent for client ' || i.client_id);
								isSuccess := FALSE;
						END;
					END IF;
				END;
			ELSE
				slog.debug(pkgCtxId, myUnit, 'Act. key for mToken for client ' || i.client_id || ':' || i.username || ' already sent. Skip!');
			END IF;
		END IF;

		-- Sve je bilo uspješno što zna?i da se automatsko slanje pr. podataka za klijenta može deaktivirati kako ga prilikom sljede?eg izvršenja job uopšte ne bi
		-- uzeo u razmatranje
		IF isSuccess THEN
			slog.debug(pkgCtxId, myUnit, 'Disable automatic credentials provisioning for client ' || i.client_id);
			MarkForAutoDataSend(pClientId => i.client_id);
		END IF;

	END LOOP;
	slog.debug(pkgCtxId, myUnit, 'Completed');
 END SendAuthenticationData;



 PROCEDURE SendAuthenticationData(pEndUsersId IN NUMBER, pAuthDataCategory VARCHAR2) IS

	vDateSent DATE;
	myunit CONSTANT VARCHAR2(30) := 'SendAuthenticationData1';

	vUsername VARCHAR2(40);
    vPassword VARCHAR2(40);
    vPreferredComunication VARCHAR2(80);

 BEGIN
	slog.debug(pkgCtxId, myUnit, pEndUsersId);

    BEGIN
		SELECT username, password
		INTO vUsername, vPassword
		FROM client
		WHERE id = pEndUsersId;
    EXCEPTION
		WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, cERR_NO_SUCH_USER  || ':' || pEndUsersId);
			sspkg.raiseError(cERR_NO_SUCH_USER, NULL, pkgCtxId, myunit);
	END;

		IF pAuthDataCategory = 'USERNAME' THEN

            slog.debug(pkgCtxId, myUnit, 'Sent username for client ' || pEndUsersId || ':' || vUsername);

            vPreferredComunication := auth.getClientDetail(pEndUsersId, 'PREFERRED_CLIENT_COMMUNICATION');

            IF vPreferredComunication = 'SMS' THEN

                slog.debug(pkgCtxId, myUnit, 'Send username per SMS');

                DECLARE
                    vLangId VARCHAR2(2) := 'bs';
                BEGIN
                    sendSMSMessage (
                        clientID => pEndUsersId,
                        pMessage => mlang.trans(
                                        vLangId, 	-- User language
                                        cSendUsernameMsg, vUsername)	-- Message
                                    );
                    slog.debug(pkgCtxId, myUnit, 'Data sent successfully');
                    setClientDetails(pClientId => pEndUsersId, pAttribId => 'USERNAME_SENT', pData => TO_CHAR(SYSDATE, common_pck.cDATETIME_MASK));

                EXCEPTION
                    WHEN OTHERS THEN
                        slog.error(pkgCtxId, myUnit, 'Exception during data (username) sent for client ' || pEndUsersId);
                END;

            ELSE
                BEGIN
                    slog.debug(pkgCtxId, myUnit, 'Send username per EMAIL');

                    vDateSent := mcore.SendUserNamePerMail(pClientId => pEndUsersId, pUsername => vUsername);
                    IF vDateSent IS NOT NULL THEN
                        -- all ok
                        slog.debug(pkgCtxId, myUnit, 'Data sent successfully');
                        setClientDetails(pClientId => pEndUsersId, pAttribId => 'USERNAME_SENT', pData => TO_CHAR(vDateSent, common_pck.cDATETIME_MASK));
                    ELSE
                        slog.error(pkgCtxId, myUnit, 'Error during data (username) sent for client ' || pEndUsersId);
                    END IF;
                EXCEPTION
                    WHEN OTHERS THEN
                        slog.error(pkgCtxId, myUnit, 'Exception during data (username) sent for client ' || pEndUsersId);
                END;
            END IF;

      END IF;

      IF pAuthDataCategory = 'PASSWORD' THEN

            BEGIN
              slog.debug(pkgCtxId, myUnit, 'Sent password for client ' || pEndUsersId || ':' || vUsername);
              vDateSent := SendPasswordPerSMS(pClientId => pEndUsersId);
              IF vDateSent IS NOT NULL THEN
                -- all ok
                slog.debug(pkgCtxId, myUnit, 'Data sent successfully');
                setClientDetails(pClientId => pEndUsersId, pAttribId => 'PASSWORD_SENT', pData => TO_CHAR(vDateSent, common_pck.cDATETIME_MASK));
              ELSE
                slog.error(pkgCtxId, myUnit, 'Error during data (password) sent for client ' || pEndUsersId);
              END IF;
            EXCEPTION
              WHEN OTHERS THEN
                slog.error(pkgCtxId, myUnit, 'Exception during data (password) sent for client ' || pEndUsersId);
            END;

      END IF;

      IF pAuthDataCategory = 'AUTHKEY_MBANKING' THEN

          slog.debug(pkgCtxId, myUnit, 'Check license for ' || pEndUsersId || ':' || common_pck.cAPP_MOBILE);
          IF hasLicense(pApplicationId => common_pck.cAPP_MOBILE, pClientId => pEndUsersId)
          THEN
              slog.debug(pkgCtxId, myUnit, 'Check license for ' || pEndUsersId || ':' || common_pck.cAPP_MOBILE || ' ... has license');

              DECLARE
                  vUserTemplateAppSelected BOOLEAN;
              BEGIN
				  vUserTemplateAppSelected  := mcore.authorization_pck.isUserTemplateAppSelected(pClientId => pEndUsersId, pApplicationId => common_pck.cAPP_MOBILE);

                  IF vUserTemplateAppSelected THEN
                      slog.debug(pkgCtxId, myUnit, 'User has mELBA checkbox selected');

                      DECLARE
                          vActKey VARCHAR2(80);
                          vUserId VARCHAR2(80);
                          vValidUntil DATE;
                      BEGIN
                          slog.debug(pkgCtxId, myUnit, 'Sent activation key (mELBA) for client ' || pEndUsersId || ':' || vUsername);
                          generateNewActivationKey(
                              pClientId => pEndUsersId,
                              pActKey => vActKey,
                              pUserId => vUserId,
                              pValidUntil => vValidUntil,
                              pAutonomousTransaction => TRUE,
                              pSendKeyPerSMS => TRUE,
                              pDeviceType => common_pck.cAPP_MOBILE);

                      EXCEPTION
                          WHEN OTHERS THEN
                              slog.error(pkgCtxId, myUnit, 'Error during data sent for client ' || pEndUsersId);
                      END;
                  END IF;
              END;

          END IF;

      END IF;

      IF pAuthDataCategory = 'AUTHKEY_MTOKEN' THEN

          slog.debug(pkgCtxId, myUnit, 'Check license for ' || pEndUsersId || ':' || common_pck.cAPP_MTOKEN);

          IF hasLicense(pApplicationId => common_pck.cAPP_MTOKEN, pClientId => pEndUsersId)
          THEN
              slog.debug(pkgCtxId, myUnit, 'Check license for ' || pEndUsersId || ':' || common_pck.cAPP_MTOKEN || ' ... has license');

              DECLARE
                  vUserTemplateAppSelected BOOLEAN;
              BEGIN
				  vUserTemplateAppSelected := mcore.authorization_pck.isUserTemplateAppSelected(pClientId => pEndUsersId, pApplicationId => common_pck.cAPP_THIN);

                  IF vUserTemplateAppSelected THEN
                      slog.debug(pkgCtxId, myUnit, 'User has mTOKEN checkbox selected');

                      DECLARE
                          vActKey VARCHAR2(80);
                          vUserId VARCHAR2(80);
                          vValidUntil DATE;
                      BEGIN
                          slog.debug(pkgCtxId, myUnit, 'Sent activation key (mTOKEN) for client ' || pEndUsersId || ':' || vUsername);
                          generateNewActivationKey(
                              pClientId => pEndUsersId,
                              pActKey => vActKey,
                              pUserId => vUserId,
                              pValidUntil => vValidUntil,
                              pAutonomousTransaction => TRUE,
                              pSendKeyPerSMS => TRUE,
                              pDeviceType => common_pck.cAPP_MTOKEN);

                      EXCEPTION
                          WHEN OTHERS THEN
                              slog.error(pkgCtxId, myUnit, 'Error during data sent for client ' || pEndUsersId);
                      END;
                  END IF;
              END;

          END IF;

      END IF;
	slog.debug(pkgCtxId, myUnit, 'Completed');
 END SendAuthenticationData;

 FUNCTION normalizeUsername(pInput VARCHAR2)
 RETURN VARCHAR2 IS
 BEGIN
	RETURN TRIM(TRANSLATE(pInput, chr(50593) || chr(50321) || chr(50317) || chr(50311) || chr(50622) || chr(50592) || chr(50320) || chr(50316) || chr(50310) || chr(50621), 'sdcczSDCCZ'));
 END normalizeUsername;

 PROCEDURE sendSMSMessage (clientID CLIENT.ID%TYPE, pMessage VARCHAR2, pSendBefore DATE DEFAULT NULL)
 IS
	myunit CONSTANT VARCHAR2(30) := 'sendSMSMessage';
 BEGIN
	slog.debug(pkgCtxId, myUnit, clientID);
	smsotp_plugin.sendMessage(clientID, pMessage, pSendBefore, common_pck.cConfidentialMsgCategory);
 END sendSMSMessage;

PROCEDURE sendSMSMessage (contactInformation VARCHAR2,  pMessage VARCHAR2, pSendBefore DATE DEFAULT NULL)
 IS
	myunit CONSTANT VARCHAR2(30) := 'sendSMSMessage1';
 BEGIN
	slog.debug(pkgCtxId, myUnit, contactInformation );
	smsotp_plugin.sendMessage(contactInformation, pMessage, pSendBefore, common_pck.cConfidentialMsgCategory);
 END sendSMSMessage;

 FUNCTION setExtAuthMethod (pClientID IN client.id%TYPE, pApplicationId IN app_extauth.application_id%TYPE, pDeviceId IN app_extauth.dev_id%TYPE, pExtAuthId IN app_extauth.extauth_id%TYPE)
 RETURN app_extauth.id%TYPE
 IS
 	myunit CONSTANT VARCHAR2(30) := 'setExtAuthMethod';
	vExtAuthRec ROWID;
	vExtAuthId app_extauth.id%TYPE;
	vAppDeviceAwareness PLS_INTEGER;
 BEGIN
	slog.debug(pkgCtxId, myUnit, pClientID || ':' || pApplicationId || ':' || pDeviceId || ':' || pExtAuthId);

	vAppDeviceAwareness := common_pck.isAppDeviceAware(pApplicationId);

	IF vAppDeviceAwareness = 0 THEN
		SELECT ROWID, id
		INTO vExtAuthRec, vExtAuthId
		FROM app_extauth
		where client_id = pClientID
		and application_id = pApplicationId;
	ELSE
		SELECT ROWID, id
		INTO vExtAuthRec, vExtAuthId
		FROM app_extauth
		where client_id = pClientID
		and application_id = pApplicationId
		and dev_id = pDeviceId;
	END IF;

	slog.debug(pkgCtxId, myUnit, 'Check for existing registrations ... found');
	-- 4.1 if exists, update with new data
	UPDATE app_extauth
	SET extauth_id = pExtAuthId
	WHERE ROWID = vExtAuthRec;

	RETURN vExtAuthId;

EXCEPTION
	-- Should not happen!
	WHEN TOO_MANY_ROWS THEN
		slog.error(pkgCtxId, myUnit, 'TOO_MANY_ROWS');
		sspkg.raiseError(cERR_MltplExtAuthRegistrations, NULL, pkgCtxId, myunit);
END setExtAuthMethod;

	FUNCTION getEncryptionKey(pId app_extauth.id%TYPE)
	RETURN VARCHAR2 IS
		myunit CONSTANT VARCHAR2(30) := 'getEncryptionKey';
		vSecKey app_extauth.sec_key%TYPE;
	BEGIN
		slog.debug(pkgCtxId, myUnit, pId);

		SELECT sec_key
		INTO vSecKey
		FROM app_extauth ae
		WHERE ae.id = pId
		  AND ae.valid = 1;

		RETURN hash(vSecKey);
	EXCEPTION
		WHEN no_data_found THEN
			slog.error(pkgCtxId, myUnit, cERR_NO_SUCH_USER || ':' || pId);
			sspkg.raiseError(cERR_NO_SUCH_USER, 'Invalid installation identifier!', pkgCtxId, myunit);
	END getEncryptionKey;

	FUNCTION isPasswordForSignatureRequired
	RETURN BOOLEAN IS
	BEGIN
		RETURN getExtAuthBoolParameterValue(pClientExtAuthId => NULL, pParameter => 'RequirePassword');
	END isPasswordForSignatureRequired;

	FUNCTION getExtAuthAttributeValue (pClientId IN client.id%TYPE, pDeviceId app_extauth.dev_id%TYPE, pApplicationId app_extauth.application_id%TYPE, pAttribute IN VARCHAR2)
	RETURN VARCHAR2 IS
		myunit CONSTANT VARCHAR2(30) := 'getExtAuthAttributeValue';

		vAttributeValue app_extauth.ph0%TYPE;
		vAppDeviceAwareness PLS_INTEGER;

	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId || ':' || pApplicationId || ':' || pDeviceId || ':' || pAttribute);

		vAppDeviceAwareness := common_pck.isAppDeviceAware(pApplicationId);

		IF vAppDeviceAwareness = 0 THEN
			SELECT CASE pAttribute
				WHEN 'PH0' THEN ph0
				WHEN 'PH1' THEN ph1
				WHEN 'PH2' THEN ph2
				WHEN 'PH3' THEN ph3
				WHEN 'PH4' THEN ph4
				WHEN 'PH5' THEN ph5
				WHEN 'PH6' THEN ph6
				WHEN 'PH7' THEN ph7
				WHEN 'PH8' THEN ph8
				WHEN 'PH9' THEN ph9
				ELSE NULL END
			INTO vAttributeValue
			FROM app_extauth
			where client_id = pClientId
			and application_id = pApplicationId;
		ELSE
			SELECT CASE pAttribute
				WHEN 'PH0' THEN ph0
				WHEN 'PH1' THEN ph1
				WHEN 'PH2' THEN ph2
				WHEN 'PH3' THEN ph3
				WHEN 'PH4' THEN ph4
				WHEN 'PH5' THEN ph5
				WHEN 'PH6' THEN ph6
				WHEN 'PH7' THEN ph7
				WHEN 'PH8' THEN ph8
				WHEN 'PH9' THEN ph9
				ELSE NULL END
			INTO vAttributeValue
			FROM app_extauth
			where client_id = pClientId
			and application_id = pApplicationId
			and dev_id = pDeviceId;
		END IF;

		RETURN vAttributeValue;

	EXCEPTION
		-- Should not happen!
		WHEN TOO_MANY_ROWS THEN
			slog.error(pkgCtxId, myUnit, 'TOO_MANY_ROWS for client id: ' || pClientId || ' and application id: ' || pApplicationId || ' and device id: ' || pDeviceId || ' and attribute: ' || pAttribute || ' and AppDeviceAwareness parameter: ' || vAppDeviceAwareness);
			sspkg.raiseError(cERR_MltplExtAuthRegistrations, NULL, pkgCtxId, myunit);
		WHEN NO_DATA_FOUND THEN
			slog.error(pkgCtxId, myUnit, 'NO_DATA_FOUND for client id: ' || pClientId || ' and application id: ' || pApplicationId || ' and device id: ' || pDeviceId || ' and attribute: ' || pAttribute || ' and AppDeviceAwareness parameter: ' || vAppDeviceAwareness);
			sspkg.raiseError(cERR_ExtAuthProblem, NULL, pkgCtxId, myunit);
	END getExtAuthAttributeValue;

	FUNCTION getExtAuthAttributeValue (pAttribute IN VARCHAR2) RETURN VARCHAR2
	IS
		vClientId client.id%TYPE;
		vDeviceId app_extauth.dev_id%TYPE;
		vApplicationId app_extauth.application_id%TYPE;

		myunit CONSTANT VARCHAR2(30) := 'getExtAuthAttributeValue2';
	BEGIN

		slog.debug(pkgCtxId, myUnit, pAttribute);

		vClientId := auth.getClientId;

		IF vClientId IS NULL THEN
			  sspkg.raiseError(mcore.common_pck.cERR_NoSession, NULL, pkgCtxId, myunit);
		END IF;

		vDeviceId := auth.getDeviceId();
		vApplicationId := auth.getApplicationId();

		RETURN getExtAuthAttributeValue (pClientId => vClientId, pDeviceId => vDeviceId, pApplicationId => vApplicationId, pAttribute => pAttribute);

	END getExtAuthAttributeValue;

	PROCEDURE setExtAuthAttribute (pApplicationId IN VARCHAR2, pAttribute IN VARCHAR2, pAttributeValue IN VARCHAR2)
	IS
		myunit CONSTANT VARCHAR2(30) := 'setExtAuthAttribute';

		vExtAuthRec ROWID;
		vAppDeviceAwareness PLS_INTEGER;

		cClientId client.id%TYPE;
		cDeviceId app_extauth.dev_id%TYPE;
	 BEGIN

		cClientId := auth.getClientId;
		cDeviceId := auth.getDeviceId();
		slog.debug(pkgCtxId, myUnit, cClientId || ':' || pApplicationId || ':' || cDeviceId || ':' || pAttribute || ':' || pAttributeValue);

		IF cClientId IS NULL THEN
			  sspkg.raiseError(mcore.common_pck.cERR_NoSession, NULL, pkgCtxId, myunit);
		END IF;

		vAppDeviceAwareness := common_pck.isAppDeviceAware(pApplicationId);

		IF vAppDeviceAwareness = 0 THEN
			SELECT ROWID
			INTO vExtAuthRec
			FROM app_extauth
			where client_id = cClientId
			and application_id = pApplicationId;
		ELSE
			SELECT ROWID
			INTO vExtAuthRec
			FROM app_extauth
			where client_id = cClientId
			and application_id = pApplicationId
			and dev_id = cDeviceId;
		END IF;

		slog.debug(pkgCtxId, myUnit, 'Check for existing registrations ... found');
		-- 4.1 if exists, update with new data
		IF pAttribute = 'PH0' THEN
			UPDATE app_extauth
			SET ph0 = pAttributeValue
			WHERE ROWID = vExtAuthRec;
		ELSIF pAttribute = 'PH1' THEN
			UPDATE app_extauth
			SET ph1 = pAttributeValue
			WHERE ROWID = vExtAuthRec;
		ELSIF pAttribute = 'PH2' THEN
			UPDATE app_extauth
			SET ph2 = pAttributeValue
			WHERE ROWID = vExtAuthRec;
		ELSIF pAttribute = 'PH3' THEN
			UPDATE app_extauth
			SET ph3 = pAttributeValue
			WHERE ROWID = vExtAuthRec;
		ELSIF pAttribute = 'PH4' THEN
			UPDATE app_extauth
			SET ph4 = pAttributeValue
			WHERE ROWID = vExtAuthRec;
		ELSIF pAttribute = 'PH5' THEN
			UPDATE app_extauth
			SET ph5 = pAttributeValue
			WHERE ROWID = vExtAuthRec;
		ELSIF pAttribute = 'PH6' THEN
			UPDATE app_extauth
			SET ph6 = pAttributeValue
			WHERE ROWID = vExtAuthRec;
		ELSIF pAttribute = 'PH7' THEN
			UPDATE app_extauth
			SET ph7 = pAttributeValue
			WHERE ROWID = vExtAuthRec;
		ELSIF pAttribute = 'PH8' THEN
			UPDATE app_extauth
			SET ph8 = pAttributeValue
			WHERE ROWID = vExtAuthRec;
		ELSIF pAttribute = 'PH9' THEN
			UPDATE app_extauth
			SET ph9 = pAttributeValue
			WHERE ROWID = vExtAuthRec;
		END IF;

	EXCEPTION
		-- Should not happen!
		WHEN TOO_MANY_ROWS THEN
			slog.error(pkgCtxId, myUnit, 'TOO_MANY_ROWS');
			sspkg.raiseError(cERR_MltplExtAuthRegistrations, NULL, pkgCtxId, myunit);
		WHEN NO_DATA_FOUND THEN
					sspkg.raiseError(cERR_ExtAuthProblem, NULL, pkgCtxId, myunit);
	END setExtAuthAttribute;

  FUNCTION tokenDuplicateExists(pTokenId client.ph0%TYPE)
  RETURN BOOLEAN IS
    vClientId client.id%TYPE;
  BEGIN

  SELECT id
  INTO vClientId
  FROM client
  WHERE ph0 = pTokenId and enabled = 1;

  IF vClientId IS NOT NULL THEN
    RETURN TRUE;
  END IF;

  EXCEPTION
      WHEN NO_DATA_FOUND THEN
          RETURN FALSE;
      WHEN TOO_MANY_ROWS THEN
          RETURN TRUE;

  END tokenDuplicateExists;

 PROCEDURE updateDeviceId(pUsername VARCHAR2, pPassword VARCHAR2, pDeviceId VARCHAR2, pSecKey VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
      sessLogLevel INTEGER := NULL, pApplicationId VARCHAR2, langId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL)
    IS

    myunit CONSTANT VARCHAR2(30) := 'updateDeviceId';

    pClient vClientType;
    vPwdExpireInDays PLS_INTEGER;
    vLoginSuccess BOOLEAN;


    BEGIN

      slog.debug(pkgCtxId, myUnit);

      BEGIN
            SELECT rowid, id, enabled, password, password_enabled, valid_from, valid_to, ph6, to_date(ph3, 'dd.mm.yyyy hh24:mi:ss'), NULL, ph4, password_change_required, password_cs
            INTO pClient
            FROM client
            WHERE lower(username) = lower(pUsername)
            AND pUsername is NOT NULL;
        EXCEPTION
            WHEN no_data_found THEN
                sspkg.raiseError(cERR_WrongCredentials, NULL, pkgCtxId, myunit);

        END;

       vLoginSuccess  := basicAccountCheck(
			pUsername =>pUsername,
			pPassword =>pPassword,
			ipAddress =>ipAddress,
			host =>host,
			sessLogLevel =>sessLogLevel,
			langId =>langId,
			pApplicationId => pApplicationId,
			pInstallationId =>pInstallationId,
			pClient => pClient,
			pPasswordExpireInDays => vPwdExpireInDays,
			pNOnce => pNOnce,
			pDeviceId => pDeviceId,
			pClientExtAuthId => NULL,
			pPasswordHashed => pPasswordHashed,
			pAppVersionId => pAppVersionId,
			pOSVersionId => pOSVersionId);

        IF vLoginSuccess OR (NOT vLoginSuccess AND loginStatusCode = cERR_WrongCredentials) THEN
            UPDATE app_extauth ae
            SET ae.dev_id = pDeviceId
            WHERE ae.client_id = pClient.id
              AND rawtohex(dbms_crypto.hash(src => utl_raw.cast_to_raw(ae.sec_key), typ => dbms_crypto.HASH_SH1)) = pSecKey;

            RETURN;
        END IF;

		sspkg.raiseError(loginStatusCode, loginStatusMessage, pkgCtxId, myunit);

    END updateDeviceId;



	  PROCEDURE checkNumberOfActiveClients

    IS
      myunit CONSTANT VARCHAR2(30) := 'checkNumberOfActiveClients';

      v_enabled_functionality BOOLEAN;
      vMflexUsers NUMBER;
      vMmobileUsers NUMBER;
      cmaxSessionDuration INTEGER;

    BEGIN

      slog.debug(pkgCtxId, myunit);

	  v_enabled_functionality := sspkg.readbool('/Customization/CheckElbaClientsActivity/CheckActiveClients');
      cmaxSessionDuration := sspkg.readInt('/Core/SessMgmt/idleTime');

      IF NOT v_enabled_functionality THEN
        slog.debug(pkgCtxId, myUnit, 'Mail notification for number of active clients for web and mobile applications not enabled for this bank!');
        RETURN;
      END IF;

      --> provjera broja trenutno aktivnih klijenata na web aplikaciji
      SELECT COUNT(DISTINCT clientid) INTO vMflexUsers
      FROM mcsessmgmt.ALL_SESSION_VARIABLES
      WHERE applicationid = common_pck.cAPP_THIN
      AND username = common_pck.cMFLEX_USER
      AND LAST_READ + cmaxSessionDuration/1440 > sysdate;

      --> provjera broja trenutno aktivnih klijenata na mobilnoj aplikaciji
      SELECT COUNT(DISTINCT clientid) INTO vMmobileUsers
      FROM mcsessmgmt.ALL_SESSION_VARIABLES
      WHERE applicationid = common_pck.cAPP_MOBILE
      AND username = common_pck.cMMOBILE_USER
      AND LAST_READ +  cmaxSessionDuration/1440 > sysdate;

      slog.debug(pkgCtxId, myUnit, 'Number of active web application clients: ' || vMflexUsers);
      slog.debug(pkgCtxId, myUnit, 'Number of active mobile application clients: ' || vMmobileUsers);

      INSERT INTO ACTIVE_CLIENTS(DATETIME, ACTIVE_CLIENTS, APPLICATION) VALUES (sysdate, vMflexUsers, common_pck.cAPP_THIN);
      INSERT INTO ACTIVE_CLIENTS(DATETIME, ACTIVE_CLIENTS, APPLICATION) VALUES (sysdate, vMmobileUsers, common_pck.cAPP_MOBILE);

      COMMIT;

     EXCEPTION
        WHEN OTHERS THEN
            slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
            ROLLBACK;
    END checkNumberOfActiveClients;



    PROCEDURE  deleteActiveClientLogs
     IS
		myunit CONSTANT VARCHAR2(30) := 'deleteActiveClientLogs';
        v_enabled_functionality BOOLEAN;
        v_num_of_days INTEGER;

    BEGIN

        slog.debug(pkgCtxId, myunit);

        v_enabled_functionality := sspkg.readbool('/Customization/CheckElbaClientsActivity/CheckActiveClients');
        v_num_of_days := sspkg.readint('/Customization/CheckElbaClientsActivity/delActiveClientsLogs');

        IF NOT v_enabled_functionality THEN
          slog.debug(pkgCtxId, myUnit, 'Delete active clients logs not enabled for this bank!');
          RETURN;
        END IF;

        DELETE FROM ACTIVE_CLIENTS AC
        WHERE AC.DATETIME < trunc(sysdate) - v_num_of_days;

	COMMIT;

     EXCEPTION
        WHEN OTHERS THEN
           slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
        ROLLBACK;
    END deleteActiveClientLogs;

       PROCEDURE checkActiveClients

    IS
		myunit CONSTANT VARCHAR2(30) := 'checkActiveClients';

        v_enabled_functionality BOOLEAN;

        vMflexUsers NUMBER;
        vMmobileUsers NUMBER;
        cNumOfMinutes INTEGER;


        vEventFlex VARCHAR2(40 CHAR);
        vEventMobile VARCHAR2(40 CHAR);

        vchannelId VARCHAR2(100);
        v_fromAddr VARCHAR2(40);
        v_toAddr VARCHAR2(40);
        vLang VARCHAR2(10 CHAR):= 'bs';
        v_subject VARCHAR2(100);
        v_body VARCHAR2(4000);

    BEGIN

        slog.debug(pkgCtxId, myunit);

		v_enabled_functionality := sspkg.readbool('/Customization/CheckElbaClientsActivity/CheckActiveClients');
        cNumOfMinutes := sspkg.readInt('/Customization/CheckElbaClientsActivity/numOfMinutesForCheckingActivity');
		vchannelId := sspkg.readvchar('/Core/Main/TranPays/Notifications/MsgChannelId');
        v_fromAddr := sspkg.readvchar('/Notifications/CheckElbaClientsActivity/fromAddr');
        v_toAddr := sspkg.readvchar('/Notifications/CheckElbaClientsActivity/toAddr');

        IF NOT v_enabled_functionality THEN
          slog.debug(pkgCtxId, myUnit, 'Mail notifications for web and mobile applications clients activity not enabled for this bank!');
          RETURN;
        END IF;

        --> provjera broja aktivnih klijenata na web aplikaciji
        SELECT SUM(ACTIVE_CLIENTS) INTO vMflexUsers
        FROM ACTIVE_CLIENTS
        WHERE APPLICATION = common_pck.cAPP_THIN
        AND DATETIME  > sysdate - (cNumOfMinutes/1440);

        slog.debug(pkgCtxId, myUnit, 'Number of active web application clients: ' || vMflexUsers);

         BEGIN

             SELECT EVENT INTO vEventFlex FROM
                            (SELECT EVENT FROM ACTIVE_CLIENTS_NOTIFICATION
                             WHERE APPLICATION = common_pck.cAPP_THIN
                             ORDER BY DATETIME DESC)
                        WHERE ROWNUM = 1;

              slog.debug(pkgCtxId, myUnit, 'Last event for web application: ' || vEventFlex);

            IF vMflexUsers = 0  THEN

                      v_subject := mlang.trans(vLang,'/Notifications/CheckElbaClientsActivity/subjectFlex');
                      slog.debug(pkgCtxId, myUnit, 'Prepare subject');

                      v_body := mlang.trans(vLang,'/Notifications/CheckElbaClientsActivity/msgBodyNoActiveWebUsers', cNumOfMinutes);
                      slog.debug(pkgCtxId, myUnit, 'Prepare body');

                      BEGIN
                            mcore.send_mail(
                                fromAddr => v_fromAddr,
                                toAddr => v_toAddr,
                                subject => v_subject,
                                bodyMsg => v_body,
                                MC_ID => vchannelId,
                                sendAfter => NULL,
                                sendBefore => NULL
                                );
                                slog.debug(pkgCtxId, myUnit, 'Notification for no active logins on web app sent');

                        EXCEPTION
                            WHEN OTHERS THEN
                            slog.error(pkgCtxId, myUnit, 'Unable to send email notification for no active logins in last 15 minutes for web application!');
                            slog.error(pkgCtxId, myUnit, SQLERRM);
                        END;

                        IF vEventFlex = 'ActiveLogins' THEN

                            INSERT INTO ACTIVE_CLIENTS_NOTIFICATION(DATETIME, EVENT, APPLICATION) VALUES(SYSDATE, 'NoActiveLogins', common_pck.cAPP_THIN);
                        END IF;
            END IF;



            IF vMflexUsers > 0 and vEventFlex = 'NoActiveLogins' THEN

                      INSERT INTO ACTIVE_CLIENTS_NOTIFICATION(DATETIME, EVENT, APPLICATION) VALUES(sysdate, 'ActiveLogins', common_pck.cAPP_THIN);
            END IF;


         EXCEPTION
                  WHEN no_data_found THEN

                  IF vMflexUsers = 0 THEN

                      v_subject := mlang.trans(vLang,'/Notifications/CheckElbaClientsActivity/subjectFlex');
                       slog.debug(pkgCtxId, myUnit, 'Prepare subject');

                      v_body := mlang.trans(vLang,'/Notifications/CheckElbaClientsActivity/msgBodyNoActiveWebUsers', cNumOfMinutes);
                      slog.debug(pkgCtxId, myUnit, 'Prepare body');

                       BEGIN
                            mcore.send_mail(
                                fromAddr => v_fromAddr,
                                toAddr => v_toAddr,
                                subject => v_subject,
                                bodyMsg => v_body,
                                MC_ID => vchannelId,
                                sendAfter => NULL,
                                sendBefore => NULL
                                );
                                slog.debug(pkgCtxId, myUnit, 'Notification for no active logins on web app from exception block sent');

                        EXCEPTION
                            WHEN OTHERS THEN
                            slog.debug(pkgCtxId, myUnit, 'Unable to send email notification for no active logins in last 15 min for web app from exception block, when job is executed for the first time and when table is empty.');
                            slog.error(pkgCtxId, myUnit, SQLERRM);
                        END;

                      INSERT INTO ACTIVE_CLIENTS_NOTIFICATION(DATETIME, EVENT, APPLICATION) VALUES(SYSDATE, 'NoActiveLogins', common_pck.cAPP_THIN);

                  ELSE

                      v_subject := mlang.trans(vLang,'/Notifications/CheckElbaClientsActivity/subjectFlex');
                      slog.debug(pkgCtxId, myUnit, 'Prepare subject');

                      v_body := mlang.trans(vLang,'/Notifications/CheckElbaClientsActivity/msgBodyActiveWebUsers', cNumOfMinutes);
                      slog.debug(pkgCtxId, myUnit, 'Prepare body');

                       BEGIN
                            mcore.send_mail(
                                fromAddr => v_fromAddr,
                                toAddr => v_toAddr,
                                subject => v_subject,
                                bodyMsg => v_body,
                                MC_ID => vchannelId,
                                sendAfter => NULL,
                                sendBefore => NULL
                                );
                                slog.debug(pkgCtxId, myUnit, 'Notification for active logins on web app from exception block sent');

                        EXCEPTION
                            WHEN OTHERS THEN
                            slog.debug(pkgCtxId, myUnit, 'Unable to send email notification for active logins in last 15 min for web app from exception block, when job is executed for the first time and when table is empty.');
                            slog.error(pkgCtxId, myUnit, SQLERRM);
                        END;

                              INSERT INTO ACTIVE_CLIENTS_NOTIFICATION(DATETIME, EVENT, APPLICATION) VALUES(sysdate, 'ActiveLogins', common_pck.cAPP_THIN);

                  END IF;

          END;


          --> provjera broja aktivnih klijenata na mobilnoj aplikaciji
        SELECT SUM(ACTIVE_CLIENTS) INTO vMmobileUsers
        FROM ACTIVE_CLIENTS
        WHERE APPLICATION = common_pck.cAPP_MOBILE
        AND DATETIME  > sysdate - (cNumOfMinutes/1440);

          slog.debug(pkgCtxId, myUnit, 'Number of active mobile application clients: ' || vMmobileUsers);

            BEGIN

               SELECT EVENT INTO vEventMobile FROM
                              (SELECT EVENT FROM ACTIVE_CLIENTS_NOTIFICATION
                               WHERE APPLICATION = common_pck.cAPP_MOBILE
                               ORDER BY DATETIME DESC)
                          WHERE ROWNUM = 1;

               slog.debug(pkgCtxId, myUnit, 'Last event for mobile application: ' || vEventMobile);

              IF vMmobileUsers = 0 THEN

                        v_subject := mlang.trans(vLang,'/Notifications/CheckElbaClientsActivity/subjectMobile');
                        slog.debug(pkgCtxId, myUnit, 'Prepare subject');

                        v_body := mlang.trans(vLang,'/Notifications/CheckElbaClientsActivity/msgBodyNoActiveMobileUsers', cNumOfMinutes);
                        slog.debug(pkgCtxId, myUnit, 'Prepare body');

                         BEGIN
                            mcore.send_mail(
                                fromAddr => v_fromAddr,
                                toAddr => v_toAddr,
                                subject => v_subject, --> mail da nema mobilnih klijenata
                                bodyMsg => v_body,
                                MC_ID => vchannelId,
                                sendAfter => NULL,
                                sendBefore => NULL
                                );
                                slog.debug(pkgCtxId, myUnit, 'Notification for no active logins on mobile app sent');

                        EXCEPTION
                            WHEN OTHERS THEN
                            slog.error(pkgCtxId, myUnit, 'Unable to send email notification for no active logins in last 15 minutes for mobile application!');
                            slog.error(pkgCtxId, myUnit, SQLERRM);
                        END;

                        IF vEventMobile = 'ActiveLogins' THEN

                            INSERT INTO ACTIVE_CLIENTS_NOTIFICATION(DATETIME, EVENT, APPLICATION) VALUES(sysdate, 'NoActiveLogins', common_pck.cAPP_MOBILE);
                        END IF;
            END IF;

                    IF vMmobileUsers > 0 and vEventMobile = 'NoActiveLogins' THEN

                        INSERT INTO ACTIVE_CLIENTS_NOTIFICATION(DATETIME, EVENT, APPLICATION) VALUES(sysdate, 'ActiveLogins', common_pck.cAPP_MOBILE);

                    END IF;


           EXCEPTION
                    WHEN no_data_found THEN --> ako je tabela dogaÄ‘aja prazna za mobilnu aplikaciju --prvi put kad se izvrsava job

                    IF vMmobileUsers = 0 THEN

                          v_subject := mlang.trans(vLang,'/Notifications/CheckElbaClientsActivity/subjectMobile');
                          slog.debug(pkgCtxId, myUnit, 'Prepare subject');

                          v_body := mlang.trans(vLang,'/Notifications/CheckElbaClientsActivity/msgBodyNoActiveMobileUsers', cNumOfMinutes);
                          slog.debug(pkgCtxId, myUnit, 'Prepare body');
			BEGIN
                            mcore.send_mail(
                                fromAddr => v_fromAddr,
                                toAddr => v_toAddr,
                                subject => v_subject, --> mail da nema mobilnih klijenata
                                bodyMsg => v_body,
                                MC_ID => vchannelId,
                                sendAfter => NULL,
                                sendBefore => NULL
                                );
                                slog.debug(pkgCtxId, myUnit, 'Notification for no active logins on mobile app from exception block sent');


                        EXCEPTION
                            WHEN OTHERS THEN
                            slog.debug(pkgCtxId, myUnit, 'Unable to send email notification for no active logins in last 15 min for mobile app from exception block, when job is executed for the first time and when table is empty.');
                            slog.error(pkgCtxId, myUnit, SQLERRM);
                        END;
                          INSERT INTO ACTIVE_CLIENTS_NOTIFICATION(DATETIME, EVENT, APPLICATION) VALUES(sysdate, 'NoActiveLogins', common_pck.cAPP_MOBILE);

                    ELSE

                        v_subject := mlang.trans(vLang,'/Notifications/CheckElbaClientsActivity/subjectMobile');
                        slog.debug(pkgCtxId, myUnit, 'Prepare subject');

                        v_body := mlang.trans(vLang,'/Notifications/CheckElbaClientsActivity/msgBodyActiveMobileUsers', cNumOfMinutes);
                        slog.debug(pkgCtxId, myUnit, 'Prepare body');

                         BEGIN
                            mcore.send_mail(
                                fromAddr => v_fromAddr,
                                toAddr => v_toAddr,
                                subject => v_subject, --> mail da ima broj aktivnih klijenata mobilne aplikacije
                                bodyMsg => v_body,
                                MC_ID => vchannelId,
                                sendAfter => NULL,
                                sendBefore => NULL
                                );
                                slog.debug(pkgCtxId, myUnit, 'Notification for active logins on mobile app from exception block sent');


                        EXCEPTION
                            WHEN OTHERS THEN
                            slog.debug(pkgCtxId, myUnit, 'Unable to send email notification for active logins in last 15 min for mobile app from exception block, when job is executed for the first time and when table is empty.');
                            slog.error(pkgCtxId, myUnit, SQLERRM);
                        END;
                                INSERT INTO ACTIVE_CLIENTS_NOTIFICATION(DATETIME, EVENT, APPLICATION) VALUES(sysdate, 'ActiveLogins', common_pck.cAPP_MOBILE);

                    END IF;
            END;


     COMMIT;

     EXCEPTION
        WHEN OTHERS THEN
            slog.error(pkgCtxId, myUnit, SQLERRM);
        ROLLBACK;

 END checkActiveClients;

	PROCEDURE checkPINComplexity(pin IN varchar2) IS
	BEGIN
		mpin_plugin.checkComplexity (pin);
	END checkPINComplexity;

	PROCEDURE checkPIN(clientID IN client.id%TYPE, deviceID IN app_extauth.dev_id%TYPE, pin IN varchar2) IS
	BEGIN
		mpin_plugin.checkPIN (clientID, deviceID, pin);
	END checkPIN;

	PROCEDURE addPIN(clientID IN client.id%TYPE, deviceID IN app_extauth.dev_id%TYPE, pin IN varchar2,
		pChallenge IN varchar2 := NULL, pResponse IN varchar2 := NULL, pOtp IN varchar2 := NULL, pSourceData IN varchar2 := NULL, pSignature IN VARCHAR2 := NULL)
	IS
		vClientSignatureMethod VARCHAR2(40);
		vClientOTPType VARCHAR2(40);
		vErrorCode VARCHAR2(400);
		vCheckSignatureResult BOOLEAN;

		myunit CONSTANT VARCHAR2(30) := 'addPIN';
	BEGIN
		slog.debug(pkgCtxId, myUnit, clientID || ':' || deviceID);

		setEvent (common_pck.cAUTHEVT_LOGIN);

		checkSignature(pChallenge, pResponse, pOtp, pSourceData, pSignature, vCheckSignatureResult, vClientSignatureMethod, vClientOTPType, vErrorCode);

		IF vCheckSignatureResult THEN
			mpin_plugin.addPIN(clientID, deviceID, pin);
		ELSE
			sspkg.raiseError(vErrorCode, null, pkgCtxId, myunit);
		END IF;
	END addPIN;

	PROCEDURE changePIN(clientID IN client.id%TYPE, deviceID IN app_extauth.dev_id%TYPE, newPIN IN varchar2, oldPIN IN varchar2,
		pChallenge IN VARCHAR2 := NULL, pResponse IN varchar2 := NULL, pOtp IN varchar2 := NULL, pSourceData IN  varchar2 := NULL, pSignature IN  VARCHAR2 := NULL)
	IS
		vClientSignatureMethod VARCHAR2(40);
		vClientOTPType VARCHAR2(40);
		vErrorCode VARCHAR2(400);
		vCheckSignatureResult BOOLEAN;

		myunit CONSTANT VARCHAR2(30) := 'changePIN';
	BEGIN
		slog.debug(pkgCtxId, myUnit, clientID || ':' || deviceID);
		setEvent (common_pck.cAUTHEVT_SIGN);

		checkSignature(pChallenge, pResponse, pOtp, pSourceData, pSignature, vCheckSignatureResult, vClientSignatureMethod, vClientOTPType, vErrorCode);

		IF vCheckSignatureResult THEN
			mpin_plugin.changePIN(clientID, deviceID, newPIN, oldPIN);
		ELSE
			sspkg.raiseError(vErrorCode, null, pkgCtxId, myunit);
		END IF;

	END changePIN;

	PROCEDURE setRqrReauthorizationFlag (pClientId IN client.id%TYPE, pDeviceId IN app_extauth.dev_id%TYPE) IS
		myunit CONSTANT VARCHAR2(30) := 'setRqrReauthorizationFlag';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId || ':' || pDeviceId || ':' || getDeviceId());

		UPDATE app_extauth
		SET PH5 = cDeviceNotPasswordAuthorized
		WHERE client_id = pClientId
		AND application_id IN (common_pck.cAPP_MOBILE)--, common_pck.cAPP_mElbaWidget)
		AND dev_id LIKE NVL(pDeviceId, '%')
		AND dev_id <> NVL(getDeviceId(), '-')
		AND extauth_id in ('elbaMobileChResp_SecKey', 'elbaMobileChResp_SecKey_PwdHash');

	END setRqrReauthorizationFlag;

	PROCEDURE unsetRqrReauthorizationFlag (pClientId IN client.id%TYPE, pDeviceId IN app_extauth.dev_id%TYPE) IS
		myunit CONSTANT VARCHAR2(30) := 'unsetRqrReauthorizationFlag';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pClientId || ':' || pDeviceId);

		UPDATE app_extauth
		SET PH5 = cDevicePasswordAuthorized
		WHERE client_id = pClientId
		AND application_id IN (common_pck.cAPP_MOBILE)--, common_pck.cAPP_mElbaWidget)
		AND dev_id = pDeviceId;

	END unsetRqrReauthorizationFlag;

	PROCEDURE reauthorizeDeviceWithPassword (pClientId IN client.id%TYPE, pPassword IN VARCHAR2, pPasswordHashed IN VARCHAR2 DEFAULT NULL)
	IS
		myunit CONSTANT VARCHAR2(30) := 'reauthorizeDeviceWithPassword';
		vPasswordVerified BOOLEAN;
	BEGIN
		slog.debug(pkgCtxId, myUnit, getDeviceId() || ':' || getSApp());

		IF pPasswordHashed IS NOT NULL THEN
			slog.debug(pkgCtxId, myUnit, 'Checking for password_cs column...');
			BEGIN
				vPasswordVerified := verifyClientPassword (
					pClientId => pClientId,
					pPassword => pPasswordHashed,
					pPwdHashed => TRUE,
					pWrite2Log => TRUE, pCheckAndLock => TRUE, pCheckAndSendNotification => TRUE,
					pLogOperationType => 'CHECKPWD', pLogMessage => 'Device reauthorization',
					pHost => getHostname(), pIpAddress => getIPAddress(), pLangId => getLang(),
					pApplicationId => getSApp(),
					pDeviceId => getDeviceId(),
					pAppVersionId => getAppVersionId(),
					pOSVersionId => getOSVersionId()) ;
				slog.debug (pkgCtxId, myUnit, 'vPasswordVerified ' || mcore.util.bool2int(vPasswordVerified));
			EXCEPTION
				when sspkg.sysexception then
					slog.error(pkgCtxId, myUnit,'verifyClientPassword (password_cs) SSPKG Exception:' || NVL(sspkg.getErrorUserMessage, sspkg.getErrorMessage));
					RAISE;
				when others then
					slog.error(pkgCtxId, myUnit,'verifyClientPassword (password_cs) General Oracle error: ' || sqlerrm);
					RAISE;
			END;
		ELSE
			slog.debug(pkgCtxId, myUnit, 'Checking for password column...');
			BEGIN
				vPasswordVerified := verifyClientPassword (
					pClientId => pClientId,
					pPassword => pPassword,
					pPwdHashed => TRUE,
					pWrite2Log => TRUE, pCheckAndLock => TRUE, pCheckAndSendNotification => TRUE,
					pLogOperationType => 'CHECKPWD', pLogMessage => 'Device reauthorization',
					pHost => getHostname(), pIpAddress => getIPAddress(), pLangId => getLang(),
					pApplicationId => getSApp(),
					pDeviceId => getDeviceId(),
					pAppVersionId => getAppVersionId(),
					pOSVersionId => getOSVersionId()) ;
				slog.debug (pkgCtxId, myUnit, 'vPasswordVerified ' || mcore.util.bool2int(vPasswordVerified));
			EXCEPTION
				when sspkg.sysexception then
					slog.error(pkgCtxId, myUnit,'verifyClientPassword SSPKG Exception:' || NVL(sspkg.getErrorUserMessage, sspkg.getErrorMessage));
					RAISE;
				when others then
					slog.error(pkgCtxId, myUnit,'verifyClientPassword General Oracle error: ' || sqlerrm);
					RAISE;
			END;
		END IF;



		IF vPasswordVerified THEN
			slog.debug (pkgCtxId, myUnit, 'Password verified');

			unsetRqrReauthorizationFlag (pClientId => pClientId, pDeviceId => getDeviceId());
		ELSE
			slog.debug (pkgCtxId, myUnit, 'Password NOT verified');
			sspkg.raiseError(cERR_WrongCredentials, NULL, pkgCtxId, myunit);

		END IF;
	END reauthorizeDeviceWithPassword;

	PROCEDURE setVerificationAttempt (pExtAuthRowId ROWID, pAttempt# PLS_INTEGER)
	  IS
		myunit CONSTANT VARCHAR2(30) := 'setVerificationAttempt';

		pragma autonomous_transaction;
	  BEGIN
		slog.debug(pkgCtxId, myUnit);

		IF pAttempt# IS NULL THEN
			slog.warn(pkgCtxId, myUnit, 'Called without attempt identifier!');
		END IF;

		IF pExtAuthRowId IS NOT NULL THEN

			slog.warn(pkgCtxId, myUnit, 'Called without row identifier!');

			UPDATE app_extauth
			   SET ph4 = TO_CHAR( NVL( pAttempt#, 0 ) )
			WHERE rowid = pExtAuthRowId;

		END IF;

		COMMIT;
	  EXCEPTION
	  WHEN OTHERS THEN
		ROLLBACK;
	END setVerificationAttempt;


	PROCEDURE setRqrReauthFlagForDevice(pClientId IN client.id%TYPE, pDeviceId IN app_extauth.dev_id%TYPE) IS
		myUnit CONSTANT VARCHAR2(30) := 'setRqrReauthFlagForDevice';
	BEGIN
		slog.debug(pkgCtxId, myUnit || ':' || pClientId || ':' || pDeviceId);

		UPDATE app_extauth
		SET PH5 = cDeviceNotPasswordAuthorized
		WHERE client_id = pClientId
		AND application_id = common_pck.cAPP_MOBILE
		AND dev_id = pDeviceId;

	END setRqrReauthFlagForDevice;

	PROCEDURE generateNewReactivationKey(
				pUsername IN VARCHAR2,
				pDeviceType IN VARCHAR2 DEFAULT NULL,
				pValidUntil OUT DATE)
	IS
		myUnit CONSTANT VARCHAR2(30) := 'generateNewReactivationKey';
		vActKey VARCHAR2(30);
		vValidUntil DATE;
		vClientId client.id%TYPE;
		vElbaReactivationKeyLength PLS_INTEGER;
		vmELBAReactivationKeyDuration PLS_INTEGER;
		vSMSReactivationCodeMsg VARCHAR2(100);
		soap_req   CLOB;
		http_resp  ws_util_pck.WS_RESPONSE;
		xml_result XMLType;
		vLastName mcore.end_users.last_name%type;
		vFirstName mcore.end_users.first_name%type;
		vAccOwner mcore.end_users.ph1%type;
		vJMBG mcauth.client.ph1%TYPE;
		vEmailReactivationCodeMsgBody VARCHAR2(500);
		vEmailSubject VARCHAR2(100);
		vFromAddr VARCHAR2(50);
		vToAddr VARCHAR2(50);
		vchannelId VARCHAR2(100);
		vSendKeyPerSMS BOOLEAN;

		PROCEDURE check_ws_error(xml_result in XMLType)
		IS
			myUnit1         varchar2(14) := 'check_ws_error';

			error_response varchar2(32767);
			ErrorMessage XMLType;
		BEGIN
			slog.debug(pkgCtxId, myUnit1);

			ErrorMessage := xml_result.extract('/ServiceResult/ErrorMessage/text()','xmlns="urn:ba:ping:elba:ElbaActivationKeyDistributionService:1p0"');
			IF ErrorMessage IS NOT NULL THEN
			   error_response := SUBSTR(ErrorMessage.extract('text()').getStringVal(), 1, 32767);
			   sspkg.raiseError(pkgCtxId || '/err/serviceCalloutError', error_response);
			END IF;
		END check_ws_error;
	BEGIN
		BEGIN
			SELECT c.id, eu.last_name, eu.first_name, eu.ph1, c.ph1 INTO vClientId, vLastName, vFirstName, vAccOwner, vJMBG FROM client c
			JOIN mcore.end_users eu ON (eu.id = c.id) WHERE username = pUsername;
		EXCEPTION
			WHEN no_data_found THEN
				slog.error(pkgCtxId, myUnit, common_pck.cERR_UnknownEndUser || ':' || pUsername);
				sspkg.raiseError(common_pck.cERR_UnknownEndUser, NULL, pkgCtxId, myUnit);
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
				sspkg.raiseOraError(pkgCtxId, myUnit);
		END;
		IF isServiceRegistered(vClientId, common_pck.cAPP_MOBILE) THEN

			vElbaReactivationKeyLength := NVL(sspkg.ReadInt('/Core/Auth/mElbaReactivation/mElbaReactivationKeyLength'),8);
			vmELBAReactivationKeyDuration := NVL(sspkg.ReadInt('/Core/Auth/mElbaReactivation/mElbaReactivationKeyDuration'), 2);
		    vActKey := to_char(trunc(dbms_random.value(POWER(10, vElbaReactivationKeyLength -1) , POWER(10, vElbaReactivationKeyLength))));
				-- Specified by minutes!
			vValidUntil := SYSDATE + (vmELBAReactivationKeyDuration/1440);

			IF sspkg.readVchar('/installationId') = common_pck.cInstallationINTESA THEN

				 IF vAccOwner IS NULL THEN
					BEGIN
						select distinct ba.account_owner_id
						INTO vAccOwner
						from mcore.action_grants ag
						join mcore.bank_accounts ba on (ba.id = ag.account_id)
						where ag.user_id = vClientId
						and ag.valid = 1
						and ag.application_id = common_pck.cAPP_MOBILE
						and ag.action_id = common_pck.cACT_ViewAccount
						and ba.status IN ('A','B')
						and rownum < 2;
					EXCEPTION
						WHEN no_data_found THEN
							slog.error(pkgCtxId, myUnit, common_pck.cERR_UnknownEndUser || ':' || vClientId);
							sspkg.raiseError(common_pck.cERR_UnknownEndUser, NULL, pkgCtxId, myUnit);
						WHEN OTHERS THEN
							slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
							sspkg.raiseOraError(pkgCtxId, myUnit);
					END;
				END IF;

				slog.debug(pkgCtxId, myUnit, vActKey || ':' || TO_CHAR(vValidUntil, common_pck.cDATETIME_MASK));

				dbms_lob.createtemporary(soap_req, false, dbms_lob.call);
				dbms_lob.append(soap_req, '<?xml version="1.0" encoding="utf-8"?>'
                       || '<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">'
                       ||   '<soap:Body>'
                       ||     '<UploadActivationKey xmlns="urn:ba:ping:elba:ElbaActivationKeyDistributionService:1p0">'
                       || ws_util_pck.create_xml_element(p_name => 'p_key_val', p_value => anydata.ConvertVarchar2(vActKey))
                       || ws_util_pck.create_xml_element(p_name => 'p_start_date', p_value => anydata.ConvertDate(sysdate))
                       || ws_util_pck.create_xml_element(p_name => 'p_end_date', p_value => anydata.ConvertDate(vValidUntil))
                       || ws_util_pck.create_xml_element(p_name => 'p_name', p_value => anydata.ConvertVarchar2(vLastName || ' ' || vFirstName))
                       || ws_util_pck.create_xml_element(p_name => 'p_elba_acc', p_value => anydata.ConvertVarchar2(vJMBG))
                       || ws_util_pck.create_xml_element(p_name => 'p_core_acc', p_value => anydata.ConvertVarchar2(vAccOwner))
                       ||     '</UploadActivationKey>'
                       ||   '</soap:Body>'
                       || '</soap:Envelope>');

				http_resp := ws_util_pck.call_ws(
				p_url => sspkg.readVchar('/Customization/ActKeyWSDistribution/serviceEndpoint'),
				p_payload => soap_req,
				p_req_type => ws_util_pck.C_POST,
				p_check_http_errors => true,
				p_soap_action => 'urn:UploadActivationKey');

				xml_result := ws_util_pck.response2xml(http_resp.data);
				check_ws_error(xml_result);

			ELSE

				vSendKeyPerSMS := sspkg.readBool('/Core/Auth/mElbaReactivation/sendReactivationKeyPerSMS');

				IF vSendKeyPerSMS THEN

					vSMSReactivationCodeMsg := mlang.trans(NVL(getLang, 'bs'),'/Core/Auth/mElbaReactivation/mElbaSMSReactivationCodeMsg', vActKey, vValidUntil);

					IF pDeviceType IS NOT NULL THEN
						IF pDeviceType = common_pck.cAndroidDeviceType THEN
							vSMSReactivationCodeMsg := mlang.trans(NVL(getLang, 'bs'),'/Core/Auth/mElbaReactivation/mElbaAndroidSMSReactivationCodeMsg', vActKey);
						ELSIF pDeviceType = common_pck.cIOSDeviceType THEN
							vSMSReactivationCodeMsg := mlang.trans(NVL(getLang, 'bs'),'/Core/Auth/mElbaReactivation/mElbaIOSSMSReactivationCodeMsg', vActKey);
						END IF;
					END IF;
					BEGIN
						sendSMSMessage(
						clientID => vClientId,
						pMessage => vSMSReactivationCodeMsg,
						pSendBefore => vValidUntil);
						slog.debug(pkgCtxId, myUnit, 'Data sent successfully');
					END;
				ELSE

					vToAddr := getClientEmail(vclientId);

					vEmailSubject := mlang.trans(NVL(getLang, 'bs'),'/Core/Auth/mElbaReactivation/emailSubject');
					vEmailReactivationCodeMsgBody := mlang.trans(NVL(getLang, 'bs'),'/Core/Auth/mElbaReactivation/emailBody', vActKey, vValidUntil);
					vFromAddr:= sspkg.readvchar('/Core/Auth/mElbaReactivation/fromAddr');
					vChannelId := sspkg.readvchar('/Core/Auth/mElbaReactivation/msgChannelId');

					BEGIN
                        mcore.send_mail(
                            fromAddr => vFromAddr,
                            toAddr => vToAddr,
                            subject => vEmailSubject,
                            bodyMsg => vEmailReactivationCodeMsgBody,
                            MC_ID => vChannelId,
                            sendAfter => NULL,
                            sendBefore => NULL);
                            slog.debug(pkgCtxId, myUnit, 'mElba reactivation key sent');
                    EXCEPTION
                        WHEN OTHERS THEN
                           slog.error(pkgCtxId, myUnit, 'Unable to send reactivation key');
                           slog.error(pkgCtxId, myUnit, SQLERRM);
					END;
				END IF;

				setClientDetails(pClientId => vClientId, pAttribId => 'MELBA_ACT_KEY_SENT', pData => TO_CHAR(SYSDATE, common_pck.cDATETIME_MASK));

			END IF;
			/*autonomus transaction*/
			regNewActionKeyAT(vActKey, TO_CHAR(vValidUntil, common_pck.cDATETIME_MASK), vClientId);
			regNewActivationDataAT(pClientId => vClientId, pDeviceType => common_pck.cAPP_MOBILE, pActKey => vActKey, pUserId => NULL, pValidUntil => vValidUntil);

			pValidUntil := vValidUntil;
		ELSE
			pValidUntil := NULL;
		END IF;


	END;

	PROCEDURE checkNOnceDigest(pClientId NUMBER, pUsername VARCHAR2, pDigest VARCHAR2, pPassword VARCHAR2, pPasswordCs VARCHAR2, ipAddress VARCHAR2, host VARCHAR2, langId VARCHAR2, pApplicationId VARCHAR2, pNOnce VARCHAR2, pDeviceId VARCHAR2, pClientExtAuthId VARCHAR2, pPasswordHashed VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL, pIsFirstLogin IN BOOLEAN DEFAULT FALSE)
	IS
		myUnit CONSTANT VARCHAR2(30) := 'checkNOnceDigest';
		vNOnceDigestPwd VARCHAR2(400);	-- Hold nOnce digest based on password hash
		vNOnceDigestPwdCs VARCHAR2(400);
		vNOnceDigestSecKey VARCHAR2(400);	-- Hold nOnce digest based on secure key
		vNOnceDigestType VARCHAR2(40);
		vfrontAppSendHashOfUCasedPwd BOOLEAN;
		vExtAuthId app_extauth.extauth_id%TYPE;
		vDeviceId app_extauth.dev_id%TYPE;
	BEGIN

		slog.debug(pkgCtxId, myUnit, 'Determine nOnce digest type ...');
		vfrontAppSendHashOfUCasedPwd := sspkg.readBool(pkgCtxId || '/frontAppSendHashOfUCasedPwd');

		BEGIN
			vNOnceDigestType := getExtAuthVcharParameterValue(pUsername => pUsername, pDeviceId => pDeviceId, pApplicationId => pApplicationId, pClientExtAuthId => pClientExtAuthId, pParameter => 'nOnceDigestType');
		EXCEPTION
		WHEN sspkg.sysException THEN
			slog.debug(pkgCtxId, myUnit, 'Catched exception ' || sspkg.getErrorCode);
			IF (sspkg.getErrorCode = cERR_UnauthorizedDevice) THEN

                IF pApplicationId = common_pck.cAPP_mElbaWidget THEN
                    slog.debug(pkgCtxId, myUnit, 'ReRaise error!');
                    RAISE;
                END IF;
				slog.debug(pkgCtxId, myUnit, 'Ignore error and proceed with vNOnceDigestType PASSWORD');
				vNOnceDigestType := 'PASSWORD';
			ELSE
				slog.debug(pkgCtxId, myUnit, 'ReRaise error!');
				RAISE;
			END IF;
		END;
		slog.debug(pkgCtxId, myUnit, 'Determined nOnce digest type ... ' || vNOnceDigestType || '!');

		-- Calculate nOnce digest based on secure key
		--digest = SHA-1(username:SECKEY:cnonce)
		DECLARE
			vSecKey app_extauth.sec_key%TYPE;
		BEGIN
			slog.debug(pkgCtxId, myUnit, 'Determine SECKEY ...');
			vSecKey := getSecKey(pclientId => pClientId, papplication_id => pApplicationId, pdev_id => pDeviceId);

			slog.debug(pkgCtxId, myUnit, 'Determine SECKEY ... Got ' || vSecKey);

			IF vSecKey IS NULL THEN		-- U slučaju ThinClient aplikacije bi ovo uvijek bilo logirano jer tamo nema secure key-a
				slog.error(pkgCtxId, myUnit, cERR_MissingSecKey, vNOnceDigestType || ':' || pApplicationId || ':' || pDeviceId);
			END IF;

			vNOnceDigestSecKey := NVL(dbms_crypto.hash(UTL_I18N.STRING_TO_RAW (pUsername||hash(vSecKey)||pNOnce), DBMS_CRYPTO.HASH_SH1),'NULL');
			slog.debug(pkgCtxId, myUnit, 'Calculated nOnce digest');

		EXCEPTION
			WHEN no_data_found THEN
				vNOnceDigestSecKey := NULL;
		END;

		vDeviceId := pDeviceId;

		IF pIsFirstLogin THEN --if client has extauth_id and reinstalls app check username/password even if extauth_id is biometric
			vExtAuthId :=  common_pck.cEA_mMobile_elbaMobileChResp;
		ELSE
			BEGIN
				vExtAuthId := clientExtAuthID(pUsername => pUsername, pDeviceId => vDeviceId, pApplicationId => pApplicationId);
			EXCEPTION
				WHEN sspkg.sysexception THEN
					IF sspkg.getErrorCode = cERR_UnauthorizedDevice THEN
					  vExtAuthId :=  common_pck.cEA_mMobile_elbaMobileChResp; -- ignore error because it is registration of new client who doesn't have extuth_id (init extuth_id is username/password)
					ELSE
						RAISE;
					END IF;
			END;
		END IF;
		slog.debug(pkgCtxId, myUnit, 'vExtAuthId:' || vExtAuthId);

		-- ako za klijenta vec imamo hash od stvarne vrijednosti passworda upisanu u password_cs kolonu, radi provjeru sa stvarnom vrijednoscu passworda iz mobilne app
		IF pPasswordCs IS NOT NULL and pPasswordHashed IS NOT NULL THEN
		    -- dio dorade za caseSensitive password politiku za mobilnu app
			slog.debug(pkgCtxId, myUnit, 'Calculated nOnce digest with password_cs ...');
			vNOnceDigestPwdCs := NVL(dbms_crypto.hash(UTL_I18N.STRING_TO_RAW (pUsername||pPasswordCs||pNOnce), DBMS_CRYPTO.HASH_SH1),'NULL');
			slog.debug(pkgCtxId, myUnit, 'Calculated nOnce digest ... OK');

			-- vNOnceDigestSecKey can be null and has to be separately checked!
			slog.debug(pkgCtxId, myUnit, 'pNOnce:' || pNOnce);
			slog.debug(pkgCtxId, myUnit, 'vNOnceDigestSecKey:' || vNOnceDigestSecKey);
			slog.debug(pkgCtxId, myUnit, 'vNOnceDigestPwd:' || vNOnceDigestPwdCs);
			slog.debug(pkgCtxId, myUnit, 'pPasswordHashed:' || pPasswordHashed);

			IF ((vNOnceDigestSecKey IS NULL AND pPasswordHashed <> vNOnceDigestPwdCs) OR
				(
					vNOnceDigestSecKey IS NOT NULL AND
					CASE
						WHEN vExtAuthId = common_pck.cEA_mMobile_elbaMobileChResp THEN pPasswordHashed <> vNOnceDigestPwdCs
						WHEN vExtAuthId = common_pck.cEA_mMobile_elbaMobileChRespSK THEN pPasswordHashed <> vNOnceDigestSecKey
					END
				)
			) THEN
				slog.debug(pkgCtxId, myUnit, 'Checking vNOnceDigestPwdCs');

				loginStatusCode := cERR_WrongCredentials;
				loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_WrongCredentials),1,loginStatusMessageMaxLength);

				writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR',
					vclientId => pClientId, vhost => host, vipaddress => ipaddress,
					vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);

				RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => pClientId, pIpAddress => ipaddress, pApplicationId => pApplicationId);

				checkAndLock(getCID4User(pUsername), 'LOGIN');
				checkAndSendNotification(getCID4User(pUsername));

				slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" incorrect passwordcs (nOnce)');
				sspkg.raiseError(cERR_WrongCredentials, NULL, pkgCtxId, myunit);
			END IF;

		ELSE
			-- Calculate nOnce digest based on password hash
			--digest = SHA-1(username:MD5(password):cnonce)
			slog.debug(pkgCtxId, myUnit, 'Calculated nOnce digest ...');

			IF vfrontAppSendHashOfUCasedPwd THEN
				vNOnceDigestPwd := NVL(dbms_crypto.hash(UTL_I18N.STRING_TO_RAW(pUsername||NVL(pPassword, 'NULL')||pNOnce), DBMS_CRYPTO.HASH_SH1),'NULL');
			ELSE
				vNOnceDigestPwd := NVL(dbms_crypto.hash(UTL_I18N.STRING_TO_RAW(pUsername||NVL(pPasswordCs, 'NULL')||pNOnce), DBMS_CRYPTO.HASH_SH1),'NULL');
			END IF;

			slog.debug(pkgCtxId, myUnit, 'Calculated nOnce digest ... OK');
			-- Password is mandatory input
			-- vNOnceDigestSecKey can be null and has to be separately checked!
			slog.debug(pkgCtxId, myUnit, 'pNOnce:' || pNOnce);
			slog.debug(pkgCtxId, myUnit, 'vNOnceDigestSecKey:' || vNOnceDigestSecKey);
			slog.debug(pkgCtxId, myUnit, 'vNOnceDigestPwd:' || vNOnceDigestPwd);
			slog.debug(pkgCtxId, myUnit, 'UPPER(pDigest):' || UPPER(pDigest));

			IF ((vNOnceDigestSecKey IS NULL AND UPPER(pDigest) <> vNOnceDigestPwd) OR
				(
					vNOnceDigestSecKey IS NOT NULL AND
					CASE
						WHEN vExtAuthId = common_pck.cEA_mMobile_elbaMobileChResp THEN UPPER(pDigest) <> vNOnceDigestPwd
						WHEN vExtAuthId = common_pck.cEA_mMobile_elbaMobileChRespSK THEN UPPER(pDigest) <> vNOnceDigestSecKey
					END
				)
			) THEN
				slog.debug(pkgCtxId, myUnit, 'Checking vNOnceDigestPwd');

				loginStatusCode := cERR_WrongCredentials;
				loginStatusMessage := SUBSTR(mlang.trans(langId, cERR_WrongCredentials),1,loginStatusMessageMaxLength);

				writeClientLog(logOpeType => 'LOGIN', message => 'BASIC_LOGIN', vlogstatus=>'ERROR',
					vclientId => pClientId, vhost => host, vipaddress => ipaddress,
					vstatus_code => loginStatusCode, vstatus_message => loginStatusMessage, vApplicationId => pApplicationId, vDeviceId => pDeviceId, vAppVersionId => pAppVersionId, vOSVersionId => pOSVersionId);
					RegisterSplunkNotification(pActionId => common_pck.cFailedLogin, pClientId => pClientId, pIpAddress => ipaddress, pApplicationId => pApplicationId);

				checkAndLock(getCID4User(pUsername), 'LOGIN');
				checkAndSendNotification(getCID4User(pUsername));

				slog.warn(pkgCtxId, myUnit, 'User "'||pUsername||'" incorrect password (nOnce)');
				sspkg.raiseError(cERR_WrongCredentials, NULL, pkgCtxId, myunit);
			END IF;
		END IF;
	END;

	PROCEDURE passwordReset(
        pUsername mcauth.client.username%TYPE,
        pEmail mcauth.client.email%TYPE,
        pMobilePhone mcauth.client.gsm%TYPE,
        pLangId VARCHAR2 DEFAULT 'bs',
        pUserId OUT NUMBER)
    IS
        myunit CONSTANT VARCHAR2(30) := 'passwordReset';
        vClientId client.id%TYPE;
        vClientRec client%ROWTYPE;
        vVerificationCode VARCHAR2(6);
        vValidUntil DATE;
        vStoredCode VARCHAR2(200);
        vStoredValidUntil VARCHAR2(40);
        vFromAddr VARCHAR2(200);
        vEmailSubject VARCHAR2(500);
        vEmailBody VARCHAR2(4000);
        vChannelId VARCHAR2(40);

        cVERIF_CODE_ATTRIB CONSTANT VARCHAR2(40) := 'PWD_RESET_VERIF_CODE';
        cVERIF_CODE_VALID_UNTIL CONSTANT VARCHAR2(40) := 'PWD_RESET_VERIF_VALID_UNTIL';

        cERR_InvalidUserData CONSTANT VARCHAR2(50) := pkgCtxId || '/err/InvalidUserData';
        cERR_UserNotFound CONSTANT VARCHAR2(50) := pkgCtxId || '/err/UserNotFound';
        cERR_UserDisabled CONSTANT VARCHAR2(50) := pkgCtxId || '/err/UserDisabled';
        cERR_PasswordResetFailed CONSTANT VARCHAR2(50) := pkgCtxId || '/err/PasswordResetFailed';

    BEGIN
        slog.debug(pkgCtxId, myUnit, 'Starting password reset for username: ' || pUsername);

        -- Validacija ulaznih parametara
        IF pUsername IS NULL OR TRIM(pUsername) = '' THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidUserData, 'Username is required');
            sspkg.raiseError(cERR_InvalidUserData, mlang.trans(pLangId, cERR_InvalidUserData || '/Username'), pkgCtxId, myUnit);
            RETURN;
        END IF;

        IF pEmail IS NULL OR TRIM(pEmail) = '' THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidUserData, 'Email is required');
            sspkg.raiseError(cERR_InvalidUserData, mlang.trans(pLangId, cERR_InvalidUserData || '/Email'), pkgCtxId, myUnit);
            RETURN;
        END IF;

        IF pMobilePhone IS NULL OR TRIM(pMobilePhone) = '' THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidUserData, 'Mobile phone is required');
            sspkg.raiseError(cERR_InvalidUserData, mlang.trans(pLangId, cERR_InvalidUserData || '/MobilePhone'), pkgCtxId, myUnit);
            RETURN;
        END IF;

        BEGIN
            SELECT *
            INTO vClientRec
            FROM mcauth.client
            WHERE UPPER(TRIM(username)) = UPPER(TRIM(pUsername))
              AND UPPER(TRIM(email)) = UPPER(TRIM(pEmail))
              AND TRIM(gsm) = TRIM(pMobilePhone)
              AND enabled = 1;

            vClientId := vClientRec.id;
            slog.debug(pkgCtxId, myUnit, 'User found with ID: ' || vClientId);

        EXCEPTION
            WHEN NO_DATA_FOUND THEN
                slog.error(pkgCtxId, myUnit, cERR_UserNotFound,
                          'User not found: ' || pUsername || ':' || pEmail || ':' || pMobilePhone);
                sspkg.raiseError(cERR_UserNotFound,
                               mlang.trans(pLangId, cERR_UserNotFound), pkgCtxId, myUnit);
                RETURN;
            WHEN TOO_MANY_ROWS THEN
                slog.error(pkgCtxId, myUnit, cERR_InvalidUserData,
                          'Multiple users found with same data: ' || pUsername);
                sspkg.raiseError(cERR_InvalidUserData,
                               mlang.trans(pLangId, cERR_InvalidUserData || '/MultipleUsers'), pkgCtxId, myUnit);
                RETURN;
        END;

        IF vClientRec.enabled = 0 THEN
            slog.error(pkgCtxId, myUnit, cERR_UserDisabled, 'User is disabled: ' || vClientId);
            sspkg.raiseError(cERR_UserDisabled, mlang.trans(pLangId, cERR_UserDisabled), pkgCtxId, myUnit);
            RETURN;
        END IF;



        -- Provjeri da li već postoji važeći verifikacijski kod
        BEGIN
            vStoredCode := getClientDetail(vClientId, cVERIF_CODE_ATTRIB);
            vStoredValidUntil := getClientDetail(vClientId, cVERIF_CODE_VALID_UNTIL);

            IF vStoredCode IS NOT NULL AND vStoredValidUntil IS NOT NULL THEN
                IF TO_DATE(vStoredValidUntil, common_pck.cDATETIME_MASK) > SYSDATE THEN
                    slog.error(pkgCtxId, myUnit, cERR_InvalidUserData, 'Verification code already sent and still valid');
                    sspkg.raiseError(cERR_InvalidUserData,
                                   mlang.trans(pLangId, cERR_InvalidUserData || '/VerificationCodeAlreadySent'),
                                   pkgCtxId, myUnit);
                    RETURN;
                END IF;
            END IF;
        EXCEPTION
            WHEN OTHERS THEN
                -- Ako nema postojećeg koda, nastavi
                NULL;
        END;

        -- Generiraj verifikacijski kod
        vVerificationCode := LPAD(TRUNC(DBMS_RANDOM.VALUE(100000, 999999)), 6, '0');
        vValidUntil := SYSDATE + INTERVAL '10' MINUTE; -- 10 minuta
        pUserId := vClientId;

        -- Sačuvaj verifikacijski kod
        setClientDetails(vClientId, cVERIF_CODE_ATTRIB, common_pck.hash(vVerificationCode));
        setClientDetails(vClientId, cVERIF_CODE_VALID_UNTIL, TO_CHAR(vValidUntil, common_pck.cDATETIME_MASK));

        -- Pošalji verifikacijski kod na email
        BEGIN
            vChannelId := sspkg.ReadVChar('/Core/Main/TranPays/Attachments/MsgChannelId');
            vFromAddr := sspkg.ReadVChar('/Core/Admin/Forms/SendPasswordResetVerificationAddress');
            vEmailSubject := mlang.trans(pLangId, pkgCtxId || '/email/PasswordResetVerificationSubject');
            vEmailBody := mlang.trans(pLangId, pkgCtxId || '/email/PasswordResetVerificationBody', vVerificationCode, '10');

            mcore.send_mail(
                fromAddr => vFromAddr,
                toAddr => pEmail,
                subject => vEmailSubject,
                bodyMsg => vEmailBody,
                MC_ID => vChannelId,
                msgCategory => common_pck.cConfidentialMsgCategory
            );

            slog.debug(pkgCtxId, myUnit, 'Verification code sent to email: ' || pEmail);

        EXCEPTION
            WHEN OTHERS THEN
                slog.error(pkgCtxId, myUnit, cERR_PasswordResetFailed,
                          'Failed to send verification email: ' || pEmail || ':' || SQLERRM);
                sspkg.raiseError(cERR_PasswordResetFailed,
                               mlang.trans(pLangId, cERR_PasswordResetFailed || '/Email'), pkgCtxId, myUnit);
                RETURN;
        END;

        slog.info(pkgCtxId, myUnit, 'Verification code generated and sent for user: ' || vClientId);

    EXCEPTION
        WHEN sspkg.sysException THEN
            RAISE;
        WHEN OTHERS THEN
            slog.error(pkgCtxId, myUnit, cERR_PasswordResetFailed,
                      'Unexpected error: ' || SQLERRM);
            sspkg.raiseError(cERR_PasswordResetFailed,
                           mlang.trans(pLangId, cERR_PasswordResetFailed), pkgCtxId, myUnit);
    END passwordReset;

    PROCEDURE confirmPasswordReset(
        pVerificationCode VARCHAR2,
        pLangId VARCHAR2 DEFAULT 'bs',
        pUserId NUMBER DEFAULT NULL)
    IS
        myunit CONSTANT VARCHAR2(30) := 'confirmPasswordReset';
        vClientId client.id%TYPE;
        vPasswordLength NUMBER;
        vPasswordType VARCHAR2(1);
        vNewPassword VARCHAR2(200);
        vSmsBody VARCHAR2(4000);
        vStoredCode VARCHAR2(200);
        vStoredValidUntil VARCHAR2(40);

        cVERIF_CODE_ATTRIB CONSTANT VARCHAR2(40) := 'PWD_RESET_VERIF_CODE';
        cVERIF_CODE_VALID_UNTIL CONSTANT VARCHAR2(40) := 'PWD_RESET_VERIF_VALID_UNTIL';

        cERR_InvalidUserData CONSTANT VARCHAR2(50) := pkgCtxId || '/err/InvalidUserData';
        cERR_PasswordResetFailed CONSTANT VARCHAR2(50) := pkgCtxId || '/err/PasswordResetFailed';
        cERR_InvalidVerificationCode CONSTANT VARCHAR2(50) := pkgCtxId || '/err/InvalidVerificationCode';

    BEGIN
        slog.debug(pkgCtxId, myUnit, 'Starting password reset confirmation with verification code');

        -- Validacija ulaznih parametara
        IF pVerificationCode IS NULL OR TRIM(pVerificationCode) = '' THEN
            slog.error(pkgCtxId, myUnit, cERR_InvalidUserData, 'Verification code is required');
            sspkg.raiseError(cERR_InvalidUserData, mlang.trans(pLangId, cERR_InvalidUserData || '/VerificationCode'), pkgCtxId, myUnit);
            RETURN;
        END IF;

        -- Pronađi korisnika na osnovu verifikacijskog koda
        IF pUserId IS NOT NULL THEN
            -- Ako je proslijeđen userId, koristi ga za brže pronalaženje
            vClientId := pUserId;
            slog.debug(pkgCtxId, myUnit, 'Using provided user ID: ' || vClientId);
        ELSE
            -- Pronađi korisnika na osnovu verifikacijskog koda
            BEGIN
                SELECT cd.client_id
                INTO vClientId
                FROM mcauth.client_details cd
                WHERE cd.attrib_id = cVERIF_CODE_ATTRIB
                  AND cd.data_vchar = common_pck.hash(pVerificationCode)
                  AND EXISTS (
                      SELECT 1 FROM mcauth.client_details cd2
                      WHERE cd2.client_id = cd.client_id
                        AND cd2.attrib_id = cVERIF_CODE_VALID_UNTIL
                        AND TO_DATE(cd2.data_vchar, common_pck.cDATETIME_MASK) > SYSDATE
                  );

                slog.debug(pkgCtxId, myUnit, 'User found by verification code: ' || vClientId);

            EXCEPTION
                WHEN NO_DATA_FOUND THEN
                    slog.error(pkgCtxId, myUnit, cERR_InvalidVerificationCode, 'Invalid or expired verification code');
                    sspkg.raiseError(cERR_InvalidVerificationCode,
                                   mlang.trans(pLangId, cERR_InvalidVerificationCode), pkgCtxId, myUnit);
                    RETURN;
            END;
        END IF;

        -- Provjeri verifikacijski kod
        BEGIN
            vStoredCode := getClientDetail(vClientId, cVERIF_CODE_ATTRIB);
            vStoredValidUntil := getClientDetail(vClientId, cVERIF_CODE_VALID_UNTIL);

            IF vStoredCode IS NULL OR vStoredValidUntil IS NULL THEN
                slog.error(pkgCtxId, myUnit, cERR_InvalidVerificationCode, 'No verification code found');
                sspkg.raiseError(cERR_InvalidVerificationCode,
                               mlang.trans(pLangId, cERR_InvalidVerificationCode || '/NotFound'), pkgCtxId, myUnit);
                RETURN;
            END IF;

            -- Provjeri da li je kod istekao
            IF TO_DATE(vStoredValidUntil, common_pck.cDATETIME_MASK) <= SYSDATE THEN
                slog.error(pkgCtxId, myUnit, cERR_InvalidVerificationCode, 'Verification code expired');
                sspkg.raiseError(cERR_InvalidVerificationCode,
                               mlang.trans(pLangId, cERR_InvalidVerificationCode || '/Expired'), pkgCtxId, myUnit);
                RETURN;
            END IF;

            -- Provjeri da li je kod tačan
            IF vStoredCode != common_pck.hash(pVerificationCode) THEN
                slog.error(pkgCtxId, myUnit, cERR_InvalidVerificationCode, 'Invalid verification code');
                sspkg.raiseError(cERR_InvalidVerificationCode,
                               mlang.trans(pLangId, cERR_InvalidVerificationCode), pkgCtxId, myUnit);
                RETURN;
            END IF;

        EXCEPTION
            WHEN sspkg.sysException THEN
                RAISE;
            WHEN OTHERS THEN
                slog.error(pkgCtxId, myUnit, cERR_InvalidVerificationCode, 'Error checking verification code: ' || SQLERRM);
                sspkg.raiseError(cERR_InvalidVerificationCode,
                               mlang.trans(pLangId, cERR_InvalidVerificationCode), pkgCtxId, myUnit);
                RETURN;
        END;

        -- Obriši verifikacijski kod nakon uspješne provjere
        DELETE FROM mcauth.client_details WHERE client_id = vClientId AND attrib_id IN (cVERIF_CODE_ATTRIB, cVERIF_CODE_VALID_UNTIL);

        slog.debug(pkgCtxId, myUnit, 'Verification code confirmed, proceeding with password reset');

        -- Otkljucaj korisnički račun
        slog.debug(pkgCtxId, myUnit, 'Unlocking user account: ' || vClientId);
        UPDATE mcauth.client
        SET usernamelocked_since = NULL,
            usernamelocked_until = SYSDATE
        WHERE id = vClientId;

        -- Generiraj novu lozinku
        vPasswordLength := NVL(sspkg.ReadInt('/Core/Admin/Forms/ActionGrantsVisualElements/flexPasswordLength'), 8);
        vPasswordType := NVL(sspkg.ReadVChar('/Core/Admin/Forms/ActionGrantsVisualElements/flexPasswordType'), 'X');
        vSmsBody := NVL(sspkg.ReadVChar('/Core/Admin/Forms/SendPasswordBody'),
                        mlang.trans(pLangId, pkgCtxId || '/msg/DefaultPasswordSMS', '<s0>'));

        vNewPassword := DBMS_RANDOM.STRING(vPasswordType, vPasswordLength);
        slog.debug(pkgCtxId, myUnit, 'New password generated for user: ' || vClientId);

        -- Ažuriraj lozinku
        UPDATE mcauth.client
        SET password = common_pck.hash(UPPER(vNewPassword)),
            password_cs = common_pck.hash(vNewPassword),
            password_enabled = 1,
            password_change_required = 1
        WHERE id = vClientId;

        slog.debug(pkgCtxId, myUnit, 'Password updated in database for user: ' || vClientId);

        -- Audit log za password reset
        writeClientLog(
            logOpeType => 'PASSWORD_RESET',
            message => 'Password reset with verification code',
            vlogstatus => 'OK',
            vclientId => vClientId,
            vstatus_code => 'PASSWORD_RESET_SUCCESS',
            vstatus_message => 'Password reset completed successfully with verification code'
        );

        -- Pošalji novu lozinku na SMS
        BEGIN
            sendSMSMessage(
                clientID => vClientId,
                pMessage => REPLACE(vSmsBody, '<s0>', vNewPassword)
            );
            slog.debug(pkgCtxId, myUnit, 'SMS sent successfully to user: ' || vClientId);
        EXCEPTION
            WHEN OTHERS THEN
                slog.error(pkgCtxId, myUnit, cERR_PasswordResetFailed,
                          'Failed to send SMS: ' || vClientId || ':' || SQLERRM);
                sspkg.raiseError(cERR_PasswordResetFailed,
                               mlang.trans(pLangId, cERR_PasswordResetFailed || '/SMS'), pkgCtxId, myUnit);
                RETURN;
        END;

        slog.info(pkgCtxId, myUnit, 'Password reset completed successfully for user: ' || vClientId);

    EXCEPTION
        WHEN sspkg.sysException THEN
            RAISE;
        WHEN OTHERS THEN
            slog.error(pkgCtxId, myUnit, cERR_PasswordResetFailed,
                      'Unexpected error: ' || SQLERRM);
            sspkg.raiseError(cERR_PasswordResetFailed,
                           mlang.trans(pLangId, cERR_PasswordResetFailed), pkgCtxId, myUnit);
    END confirmPasswordReset;

END AUTH;
/

show errors
				