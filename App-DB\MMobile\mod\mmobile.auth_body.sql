PROMPT Creating package body script for package AUTH
CREATE OR REPLACE
PACKAGE BODY MMOBILE.AUTH AS

	/*
	/App-DB/MMobile/Auth/err/noIPAddress
	/App-DB/MMobile/Auth/err/IPChanged
	*/
	cERR_noIPAddress CONSTANT VARCHAR2(36) := pkgCtxId || '/err/noIPAddress';
	cERR_IPChanged   CONSTANT VARCHAR2(34) := pkgCtxId || '/err/IPChanged';
	cERR_MultipleAccOwnerTypes   CONSTANT VARCHAR2(46) := pkgCtxId || '/err/MultipleAccOwnerTypes';

	/* get Session var 4 user */
	FUNCTION getSApp RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getSApp;
	end;

	/* get Session var 4 user */
	FUNCTION getSUser RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getSUser;
	END;

/*	 True IF login PROCEDURE IS fully and successfully completed (basic login and extended login IF needed) */

	FUNCTION isLogged RETURN BOOLEAN AS
	BEGIN
		RETURN getSCID IS NOT NULL;
	END isLogged;

	/* Return true IF username IS locked, and dates when lock IS issued and when will expire */
	FUNCTION isUsernameLocked(vusername VARCHAR2, sinceDate out date, untilDate out date) RETURN BOOLEAN AS
	BEGIN
	RETURN mcauth.auth.isUsernameLocked(vusername, sinceDate, untilDate);
	END;

	/* Return true IF username IS locked */
	FUNCTION isUsernameLocked(vusername VARCHAR2) RETURN BOOLEAN AS
	BEGIN
	RETURN mcauth.auth.isUsernameLocked(vusername);
	END;

	/* Return true IF gsm IS locked, and dates when lock IS issued and when will expire */
	FUNCTION isGsmLocked(vgsm VARCHAR2, sinceDate out date, untilDate out date) RETURN BOOLEAN AS
	BEGIN
	RETURN mcauth.auth.isGsmLocked(vgsm, sinceDate, untilDate);
	END;

	/* Return true IF gsm IS locked, and dates when lock IS issued and when will expire */
	FUNCTION isGsmLocked(vgsm VARCHAR2) RETURN BOOLEAN AS
	BEGIN
	RETURN mcauth.auth.isGsmLocked(vgsm);
	END;


	/* get Session var 4 client ID */
	FUNCTION getSCID RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getSCID;
	END;

/*	 get IP address used during logong */
	FUNCTION getIPAddress RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getIPAddress;
	END;

	/*  */
	FUNCTION getDeviceAuthorizationStatus RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getDeviceAuthorizationStatus;
	END;

	/* Return last login status code. NULL IS fine, otherwise there IS error code */
	FUNCTION getLoginStatusCode RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getLoginStatusCode;
	END;

	/* Return last status message */
	FUNCTION getLoginStatusDetails RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getLoginStatusDetails;
	END;

	PROCEDURE write(varName VARCHAR2, varValue VARCHAR2) AS
	BEGIN
		mcauth.auth.write(varName, varValue);
	END;

	FUNCTION read(varName VARCHAR2) RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.read(varName);
	END;

	FUNCTION readMDate(varName VARCHAR2) RETURN DATE AS
	BEGIN
		RETURN mcauth.auth.readMDate(varName);
	END;

	FUNCTION hash(var VARCHAR2) RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.hash(var);
	END;

	FUNCTION clientOTPType RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.clientOTPType;
	END;

	FUNCTION clientRequirePKILogin(vusername VARCHAR2, pDeviceId VARCHAR2, pApplicationId VARCHAR2) RETURN BOOLEAN AS
	BEGIN
	RETURN mcauth.auth.clientRequirePKILogin(vusername=>vusername, pDeviceId=>pDeviceId, pApplicationId=>pApplicationId);
	END;

	FUNCTION clientRequirePKILogin RETURN BOOLEAN AS
	BEGIN
	RETURN mcauth.auth.clientRequirePKILogin();
	END;

	FUNCTION clientSignatureMethod RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.clientSignatureMethod;
	END clientSignatureMethod;

	FUNCTION clientSignatureMethod(pUsername VARCHAR2, pDeviceId VARCHAR2 := NULL) RETURN VARCHAR2 AS
		myunit CONSTANT VARCHAR2(30) := 'clientSignatureMethod2';

		vDeviceId VARCHAR2(1000 CHAR) := pDeviceId;
		vClientExtAuthId VARCHAR2(40 CHAR);
	BEGIN
		slog.debug(pkgCtxId, myUnit, vDeviceId);

		vClientExtAuthId := mcauth.auth.clientExtAuthID(pUsername, vDeviceId, mcore.common_pck.cAPP_MOBILE);

		slog.debug(pkgCtxId, myUnit, 'vClientExtAuthId:' || vClientExtAuthId);

		RETURN mcauth.auth.clientSignatureMethod(pClientExtAuthId=> vClientExtAuthId);
	END clientSignatureMethod;

	FUNCTION clientSignatureOtpType RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.clientSignatureOtpType;
	END clientSignatureOtpType;

	FUNCTION clientSignatureOtpType(pUsername VARCHAR2, pDeviceId VARCHAR2 := NULL)
	RETURN VARCHAR2 AS
		myunit CONSTANT VARCHAR2(30) := 'clientSignatureOtpType2';
		vClientExtAuthId VARCHAR2(40 CHAR);
		vDeviceId VARCHAR2(1000 CHAR) := pDeviceId;
	BEGIN
		slog.debug(pkgCtxId, myUnit, vDeviceId);

		vClientExtAuthId := mcauth.auth.clientExtAuthID(pUsername, vDeviceId, mcore.common_pck.cAPP_MOBILE);
		slog.debug(pkgCtxId, myUnit, 'vClientExtAuthId:' || vClientExtAuthId);

		RETURN mcauth.auth.clientSignatureOtpType(
				pUsername => pUsername,
				pDeviceId => vDeviceId,
				pApplicationId => mcore.common_pck.cAPP_MOBILE,
				pClientExtAuthId => vClientExtAuthId);

	END clientSignatureOtpType;

	FUNCTION hasMultipleAccOwnerTypesAssign(pUsername IN mcauth.client.username%TYPE)
	RETURN BOOLEAN IS
		vPom PLS_INTEGER;
	BEGIN
		SELECT COUNT(DISTINCT NVL(ao.ph0, 'F'))
		INTO vPom
		FROM mcauth.client c
		JOIN mcore.action_grants ag ON (ag.user_id = c.id)
		JOIN mcore.bank_accounts ba ON (ba.id = ag.account_id)
		JOIN mcore.account_owners ao ON (ao.id = ba.account_owner_id)
		WHERE   lower(c.username) = lower(pUsername) AND
				application_id = mcore.common_pck.cAPP_MOBILE AND
				valid = 1;

		IF vPom <= 1 THEN
			RETURN FALSE;
		END IF;

		RETURN TRUE;
	END hasMultipleAccOwnerTypesAssign;

	FUNCTION basicLogin(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
						certificate VARCHAR2, application_id VARCHAR2, otpType out VARCHAR2,
						sessionId out VARCHAR2, sessLogLevel integer := NULL, langId VARCHAR2 := NULL,
						pInstallationId VARCHAR2 := NULL, pDeviceId VARCHAR2 := NULL, pPasswordExpireInDays OUT PLS_INTEGER, pNOnce IN VARCHAR2 DEFAULT NULL, pUserId OUT PLS_INTEGER, pPasswordHashed IN VARCHAR2 DEFAULT NULL,
						pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL, pIsFirstLogin IN BOOLEAN DEFAULT FALSE)
						RETURN BOOLEAN IS
		myunit CONSTANT VARCHAR2(30) := 'basicLogin';
		funcResult BOOLEAN:= FALSE;
		vPasswordExpireInDays PLS_INTEGER;
	BEGIN

		slog.debug(pkgCtxId, myUnit,  vusername || ':' || ipAddress || ':' || host || ':' || application_id || ':' || sessLogLevel || ':' || langId
			|| ':' || pInstallationId || ':' || pDeviceId);

		funcResult := mcauth.auth.basicLogin(substr(vusername,1,40), substr(vpassword,1,40), ipAddress, host, certificate, otpType, sessionId, sessLogLevel, langId,
			application_id, pInstallationId, pDeviceId, vPasswordExpireInDays, pNOnce, pUserId, pPasswordHashed, pAppVersionId, pOSVersionId, pIsFirstLogin);
		slog.debug(pkgCtxId, myUnit, 'Core Basic login routine returns ' || mcore.util.bool2char(funcResult));

		pPasswordExpireInDays := vPasswordExpireInDays;

		IF funcResult then
			slog.debug(pkgCtxId, myUnit, 'Restore session ' || substr(sessionId,1,10));
			opens(sessionId, ipAddress);

			--dbms_output.put_line(myUnit || ' - InstallationId:' || UPPER(sspkg.getInstallationId));

			IF NOT sspkg.readBool('/Core/Auth/setDefAccountOwnerDuringLogon') THEN
				-- Ako ima, onemogu�iti login s odgovaraju�om gre�kom
				IF hasMultipleAccOwnerTypesAssign(vusername) THEN
					logout;
					slog.error(pkgCtxId, myUnit, cERR_MultipleAccOwnerTypes, 'Multiple account owners types for ' || vusername);
					sspkg.raiseError(cERR_MultipleAccOwnerTypes, NULL, pkgCtxId, myunit);
				END IF;
			END IF;

			slog.debug(pkgCtxId, myUnit, 'Session ' || substr(sessionId,1,10) || ' restored!');
		END IF;
		RETURN funcResult;
	END basicLogin;

	FUNCTION extendedLogin(sessionId VARCHAR2, otp VARCHAR2, challenge VARCHAR2 := NULL) RETURN BOOLEAN AS
		myunit CONSTANT VARCHAR2(30) := 'extendedLogin';
		vLoginResult BOOLEAN;
	BEGIN
	slog.debug(pkgCtxId, myUnit,  substr(sessionId,1,10) || ':' || otp || ':' || challenge);
	vLoginResult := mcauth.auth.extendedLogin(sessionId, otp, challenge);

	RETURN vLoginResult;
	END extendedLogin;

	FUNCTION extendedLogin(sessionId VARCHAR2, otp VARCHAR2, challenge VARCHAR2 := NULL,
					pLicenses out sys_refcursor, pAccountOwners out sys_refcursor) RETURN BOOLEAN AS
		myunit CONSTANT VARCHAR2(30) := 'extendedLogin2';
		vLoginResult BOOLEAN;
	BEGIN
	slog.debug(pkgCtxId, myUnit, substr(sessionId,1,10) || ':' || otp || ':' || challenge);
	vLoginResult := mcauth.auth.extendedLogin(sessionId, otp, challenge);

	IF vLoginResult then
		slog.debug(pkgCtxId, myUnit, 'Login success');
		mcauth.auth.opens(sessionId);

		pLicenses := accounts_pck.getAssignedLicences;
		pAccountOwners := accounts_pck.getAccOwnersList;
	else
		slog.debug(pkgCtxId, myUnit, 'Login failure');
	    	OPEN pLicenses FOR SELECT NULL FROM dual WHERE 1 = 2;
	   	OPEN pAccountOwners FOR SELECT NULL FROM dual WHERE 1 = 2;
	END IF;

	RETURN vLoginResult;
	END extendedLogin;

	PROCEDURE opens(sessionId VARCHAR2) AS
	BEGIN
		mcauth.auth.opens(sessionId);
	END opens;

	PROCEDURE opens(sessionId VARCHAR2, ipaddress VARCHAR2 default NULL) AS
		myunit CONSTANT VARCHAR2(30) := 'opens2';
		vIpChangeAllowed BOOLEAN := FALSE;
	BEGIN
		slog.debug(pkgCtxId, myUnit,  substr(sessionId,1,10) || ':' || ipaddress);

		vIpChangeAllowed := sspkg.readBool(pkgCtxId || '/IPChangeAllowed');

		IF vIpChangeAllowed THEN
		slog.debug(pkgCtxId, myUnit, 'Allow IP change!');
		END IF;

		IF (NOT vIpChangeAllowed) AND (ipaddress IS NULL) THEN
			sspkg.raiseError(cERR_noIPAddress, NULL, pkgCtxId, myunit);
		END IF;


		mcauth.auth.opens(sessionId);
		slog.debug(pkgCtxId, myUnit, 'Session opened');

		IF (NOT vIpChangeAllowed) AND (getIPAddress <> ipaddress) THEN
			slog.debug(pkgCtxId, myUnit, 'IP change disallowed and saved address ' || getIPAddress || ' differes from actual one : ' || ipaddress || '. Logout!');
			logout;
			sspkg.raiseError(cERR_IPChanged, substr(sessionId,1,10) || ':' || ipaddress, pkgCtxId, myunit);
		END IF;

		-- accounts_pck.InitializeBankAccounts;
	END opens;

	PROCEDURE closes AS
	BEGIN
		mcauth.auth.closes;
	END closes;

	PROCEDURE logout AS
	BEGIN
		mcauth.auth.logout;
	END logout;

	FUNCTION resetPasswordRQ(contactClient VARCHAR2, secQuestionID VARCHAR2, secQuestionAnswer VARCHAR2) RETURN BOOLEAN AS
	BEGIN
		RETURN mcauth.auth.resetPasswordRQ(contactClient, secQuestionID, secQuestionAnswer);
	END resetPasswordRQ;

	FUNCTION changePassword(oldPassword VARCHAR2, newPassword VARCHAR2, otp VARCHAR2 := NULL, challenge VARCHAR2 := NULL, pNOnce IN VARCHAR2 := NULL) RETURN BOOLEAN AS
	BEGIN
		RETURN mcauth.auth.changePassword(oldPassword, newPassword, otp, challenge, pNOnce);
	END changePassword;

	FUNCTION checkOTP(otp VARCHAR2) RETURN BOOLEAN AS
	BEGIN
		RETURN mcauth.auth.checkOTP(otp);
	END checkOTP;

	PROCEDURE genSendOTP  AS
	BEGIN
		mcauth.auth.genSendOTP;
	END genSendOTP;

	FUNCTION genChallenge(sourceData VARCHAR2 := dbms_random.string('A', 24)) RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.genChallenge(sourceData);
	END;

	FUNCTION checkSignature(sourceData VARCHAR2, signature VARCHAR2) RETURN BOOLEAN AS
	BEGIN
		RETURN mcauth.auth.checkSignature(sourceData, signature);
	END checkSignature;

	FUNCTION checkCHRESP(challenge VARCHAR2, response VARCHAR2) RETURN BOOLEAN AS
	BEGIN
		RETURN mcauth.auth.checkCHRESP(challenge, response);
	END checkCHRESP;

	FUNCTION getGsmSCID RETURN NUMBER AS
	BEGIN
	RETURN mcauth.auth.getGsmSCID;
	END;

	FUNCTION getGsmSPhone RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getGsmSPhone;
	END;

	FUNCTION getGsmForCID RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getGsmForCID(getSCID);
	END;

	PROCEDURE gsmLogout AS
	BEGIN
		mcauth.auth.gsmLogout;
	END;

	FUNCTION gsmLogin(vgsm VARCHAR2, vpassword VARCHAR2, vapplication VARCHAR2) RETURN BOOLEAN AS
	BEGIN
		RETURN mcauth.auth.gsmLogin(vgsm, vpassword, vapplication);
	END;

	-- This FUNCTION RETURN extauth id. If NULL IS returned then user does not use additional OTP.
	-- Extauth_id are configured in branch /Core/Auth/ExtAuth/<ExtAuthID>

	FUNCTION getExtAuthId RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getExtAuthId;
	END;

	-- Set language
	PROCEDURE setLang(langId VARCHAR2) AS
	BEGIN
		mcauth.auth.setLang(langId);
	END;

	-- Get language
	FUNCTION getLang RETURN VARCHAR2 AS
	BEGIN
		RETURN mcauth.auth.getLang;
	END;

	-- Set account owner
	PROCEDURE setAccountOwner(pAccOwnerId IN VARCHAR2)
	IS
	BEGIN
		mcauth.auth.setAccountOwner(pAccOwnerId => pAccOwnerId);
	END setAccountOwner;

	FUNCTION getAccountOwner
	RETURN VARCHAR2 IS
	BEGIN
		RETURN mcauth.auth.getAccountOwner;
	END getAccountOwner;

	FUNCTION getPrimaryAccountOwner
	RETURN VARCHAR2 IS
	BEGIN
		RETURN mcauth.auth.getPrimaryAccountOwner;
	END getPrimaryAccountOwner;

	/* Return previous login */
	FUNCTION getPreviousLoginInfo RETURN SYS_REFCURSOR IS
	BEGIN
		RETURN mcauth.auth.getPreviousLoginInfo;
	END;

	/* Return previous logins */
	FUNCTION getPreviousLogins
	RETURN SYS_REFCURSOR
	IS
	BEGIN
		RETURN mcauth.auth.getPreviousLogins(getSCID);
	END getPreviousLogins;

	FUNCTION isSessionExpired
	RETURN BOOLEAN
	IS
	BEGIN
		RETURN mcauth.auth.isSessionExpired;
	END isSessionExpired;

	FUNCTION p_isSessionExpired
	RETURN PLS_INTEGER
	IS
	BEGIN
		IF isSessionExpired THEN
			RETURN 1;
		END IF;
		RETURN 0;
	END p_isSessionExpired;

	FUNCTION getDayOfWeeks
	RETURN sys_refcursor IS
		rez sys_refcursor;
	BEGIN
		OPEN rez FOR
		SELECT dow, dow_name FROM mcore.vw$day_of_weeks a
		ORDER BY dow ASC;
		RETURN rez;
	END getDayOfWeeks;

	FUNCTION generateTanList(
		pThrId OUT PLS_INTEGER,
		pTanDim OUT PLS_INTEGER, pOtpLength OUT PLS_INTEGER,
		pIndexD1Count OUT PLS_INTEGER, pIndexD1Type OUT VARCHAR2,
		pIndexD2Count OUT PLS_INTEGER, pIndexD2Type OUT VARCHAR2
		)
	RETURN sys_refcursor
	IS
	BEGIN
		RETURN mcauth.tanlist_plugin.generateTanList(getSCID(), pThrId, pTanDim, pOtpLength, pIndexD1Count, pIndexD1Type, pIndexD2Count, pIndexD2Type);
	END generateTanList;

	PROCEDURE activateNewTanList IS
	BEGIN
		mcauth.tanlist_plugin.activateNewTanList(getSCID());
	END activateNewTanList;

	FUNCTION isAlmostDepleted RETURN VARCHAR2 IS
	BEGIN
		RETURN mcauth.auth.isAlmostDepleted(getSCID());
	END isAlmostDepleted;

	FUNCTION registerDevice(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2,
		host VARCHAR2, certificate VARCHAR2, pApplicationId VARCHAR2 := NULL,
		sessLogLevel integer := NULL, langId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL,
		pDeviceId VARCHAR2, pDeviceDescription VARCHAR2, pActivationKey VARCHAR2, pExtAuthId VARCHAR2, pStatusMessage OUT VARCHAR2,
		pOperatingSystem IN VARCHAR2 := NULL, pUserIdentifier IN VARCHAR2 := NULL, pDeviceType IN VARCHAR2 := NULL, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId VARCHAR2 DEFAULT NULL)
	RETURN VARCHAR2 IS
	BEGIN
		RETURN mcauth.auth.registerDevice(vusername => vusername, vpassword => vpassword, ipAddress => ipAddress,
					host => host, certificate => certificate,
					sessLogLevel => sessLogLevel, langId => langId, pApplicationId => pApplicationId, pInstallationId => pInstallationId,
					pDeviceId => pDeviceId, pDeviceDescription => pDeviceDescription, pActivationKey => pActivationKey, pExtAuthId => pExtAuthId,
					pStatusMessage => pStatusMessage, pOperatingSystem => pOperatingSystem, pUserIdentifier => pUserIdentifier, pDeviceType => pDeviceType, pNOnce => pNOnce, pPasswordHashed => pPasswordHashed, pAppVersionId => pAppVersionId, pOSVersionId => pOSVersionId);
	END registerDevice;

	FUNCTION getListOfAuthorizedDevices RETURN sys_refcursor
	IS
	BEGIN
		RETURN mcauth.auth.getListOfAuthorizedDevices();
	END getListOfAuthorizedDevices;

	FUNCTION getListOfAuthorizedDevices(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
		sessLogLevel INTEGER := NULL, langId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pDeviceId VARCHAR2 DEFAULT NULL, pAppVersionId VARCHAR2 DEFAULT NULL, pOSVersionId VARCHAR2 DEFAULT NULL)
	RETURN sys_refcursor
	IS
	BEGIN
		RETURN mcauth.auth.getListOfAuthorizedDevices(vusername => vusername, vpassword => vpassword, ipAddress => ipAddress, host => host,
			sessLogLevel => sessLogLevel, langId => langId, pInstallationId => pInstallationId, pNOnce => pNOnce, pPasswordHashed => pPasswordHashed, pDeviceId => pDeviceId, pAppVersionId => pAppVersionId, pOSVersionId => pOSVersionId);
	END getListOfAuthorizedDevices;

	PROCEDURE unregisterDevice(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
		sessLogLevel INTEGER := NULL, langId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL, pAppExtAuthId NUMBER, pComment VARCHAR2 := NULL, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pDeviceId VARCHAR2 DEFAULT NULL, pAppVersionId VARCHAR2 DEFAULT NULL, pOSVersionId VARCHAR2 DEFAULT NULL)
	IS
	BEGIN
		mcauth.auth.unregisterDevice(vusername => vusername, vpassword => vpassword, ipAddress => ipAddress, host => host,
			sessLogLevel => sessLogLevel, langId => langId, pInstallationId => pInstallationId, pAppExtAuthId => pAppExtAuthId, pNOnce => pNOnce, pPasswordHashed => pPasswordHashed, pDeviceId => pDeviceId, pAppVersionId => pAppVersionId, pOSVersionId => pOSVersionId);
	END unregisterDevice;

	FUNCTION getPassword(pUsername mcauth.client.username%TYPE)
	RETURN mcauth.client.password%TYPE IS
	BEGIN
		RETURN mcauth.auth.getPassword(pUsername);
	END getPassword;

	PROCEDURE sendSMSMessage (pMessage VARCHAR2)
	IS
	BEGIN
		mcauth.auth.sendSMSMessage(clientID => getSCID(), pMessage => pMessage);
	END sendSMSMessage;

	FUNCTION setExtAuthId(pExtAuthId IN VARCHAR2)
	RETURN NUMBER IS
	BEGIN
		common_pck.CommonSecurityChecks;

		RETURN mcauth.auth.setExtAuthMethod(pClientID => getSCID(), pApplicationId => mcore.common_pck.cAPP_MOBILE, pDeviceId => mcauth.auth.getDeviceId(), pExtAuthId => pExtAuthId);
	END setExtAuthId;

	FUNCTION getEncryptionKey(pId NUMBER)
	RETURN VARCHAR2	IS
	BEGIN
		RETURN mcauth.auth.getEncryptionKey(pId);
	END getEncryptionKey;

	FUNCTION isPasswordForSignatureRequired
	RETURN BOOLEAN IS
	BEGIN
		common_pck.CommonSecurityChecks;

		RETURN mcauth.auth.isPasswordForSignatureRequired();
	END isPasswordForSignatureRequired;

	PROCEDURE updateDeviceId(pUsername VARCHAR2, pPassword VARCHAR2, pDeviceId VARCHAR2, pSecKey VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
      		sessLogLevel INTEGER := NULL, pApplicationId VARCHAR2, langId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pAppVersionId VARCHAR2 DEFAULT NULL, pOSVersionId VARCHAR2 DEFAULT NULL)

  	  IS
	BEGIN
         	mcauth.auth.updateDeviceId(pUsername => pUsername, pPassword => pPassword, pDeviceId => pDeviceId, pSecKey => pSecKey, ipAddress => ipAddress,
         	host => host, sessLogLevel => sessLogLevel, pApplicationId => pApplicationId, langId => langId, pInstallationId => pInstallationId, pNOnce => pNOnce, pPasswordHashed => pPasswordHashed, pAppVersionId => pAppVersionId, pOSVersionId => pOSVersionId);
	END updateDeviceId;

	PROCEDURE checkMobilePINComplexity(pin IN VARCHAR2) IS
	BEGIN
		mcauth.auth.checkPINComplexity (pin);
	END checkMobilePINComplexity;

	PROCEDURE checkMobilePin(pin IN varchar2) IS
	BEGIN
		common_pck.CommonSecurityChecks;
		mcauth.auth.checkPIN (getSCID(), mcauth.auth.getDeviceId(), pin);
	END checkMobilePin;

	PROCEDURE addMobilePin(pin IN varchar2,
		pChallenge varchar2 := NULL, pResponse varchar2 := NULL, pOtp varchar2 := NULL, pSourceData varchar2 := NULL,pSignature VARCHAR2 := NULL)
	IS
	BEGIN
		mcauth.auth.addPIN(mcauth.auth.getCID4User(mcauth.auth.getSUser), mcauth.auth.getDeviceId(), pin, pChallenge, pResponse, pOtp, pSourceData, pSignature);
	END addMobilePin;

	PROCEDURE changeMobilePin(newPIN IN varchar2, oldPIN IN varchar2,
		pChallenge varchar2 := NULL, pResponse varchar2 := NULL, pOtp varchar2 := NULL, pSourceData varchar2 := NULL,pSignature VARCHAR2 := NULL)
	IS
	BEGIN
		common_pck.CommonSecurityChecks;
		mcauth.auth.changePIN(getSCID(), mcauth.auth.getDeviceId(), newPIN, oldPIN, pChallenge, pResponse, pOtp, pSourceData, pSignature);
	END changeMobilePin;

	PROCEDURE reauthorizeDeviceWithPassword (pPassword IN VARCHAR2, pPasswordHashed IN VARCHAR2 DEFAULT NULL)
	IS
		myunit CONSTANT VARCHAR2(30) := 'reauthorizeDeviceWithPassword';
	BEGIN
		slog.debug(pkgCtxId, myUnit, pPassword);

		common_pck.CommonSecurityChecks;

		slog.debug(pkgCtxId, myUnit, 'Passed security check!');

		mcauth.auth.reauthorizeDeviceWithPassword (pClientId => getSCID(), pPassword => pPassword, pPasswordHashed => pPasswordHashed);
	END reauthorizeDeviceWithPassword;

	FUNCTION verifyClientPassword (pPassword IN VARCHAR2)
	RETURN BOOLEAN IS
	BEGIN
		common_pck.CommonSecurityChecks;

		RETURN mcauth.auth.verifyClientPassword (
			pClientId => getSCID(), pPassword => pPassword, pPwdHashed => TRUE,
			pWrite2Log => TRUE, pCheckAndLock => TRUE, pCheckAndSendNotification => TRUE,
			pLogOperationType => 'CHECK_PASSWORD', pLogMessage => 'Check password', pHost => mcauth.auth.getHostname(),
			pIpAddress => getIPAddress(), pLangId => getLang(), pApplicationId => mcauth.auth.getSApp());
	END verifyClientPassword;

	PROCEDURE setMOTP_USE_PIN(pMOtpUsePIN VARCHAR2)
	IS
	BEGIN
		mcsm.setMOTP_USE_PIN(pMOtpUsePIN => pMOtpUsePIN);
	END;


	FUNCTION getMOTP_USE_PIN
	RETURN VARCHAR2
	IS
	BEGIN
		RETURN mcsm.getMOTP_USE_PIN;
	END;

	PROCEDURE setRqrReauthFlagForDevice IS
	BEGIN
		mcauth.auth.setRqrReauthFlagForDevice(pClientId => getSCID, pDeviceId => mcauth.auth.getDeviceId);
	END;

	PROCEDURE checkNOnceDigest(pUsername IN VARCHAR2,
							  pDigest IN VARCHAR2,
							  ipAddress IN VARCHAR2,
							  host IN VARCHAR2,
							  langId IN VARCHAR2,
							  pApplicationId IN VARCHAR2,
							  pNOnce IN VARCHAR2,
							  pDeviceId IN VARCHAR2,
							  pPasswordHashed IN VARCHAR2 DEFAULT NULL,
							  pAppVersionId VARCHAR2 DEFAULT NULL,
							  pOSVersionId VARCHAR2 DEFAULT NULL,
							  pIsFirstLogin BOOLEAN DEFAULT FALSE)
	IS
		myUnit CONSTANT VARCHAR2(30) := 'checkNOnceDigest';
		vClientId NUMBER;
		vPassword VARCHAR2(40);
		vPasswordCs VARCHAR2(40);
	BEGIN
		BEGIN
			SELECT id, password, password_cs
			INTO vClientId, vPassword, vPasswordCs
			FROM mcauth.client WHERE lower(username) = lower(pUsername)
			AND pUsername is NOT NULL;
		EXCEPTION
			WHEN no_data_found THEN
				 slog.error(pkgCtxId, myUnit, mcore.common_pck.cERR_UnknownEndUser || ':' || pUsername);
				 sspkg.raiseError(mcore.common_pck.cERR_UnknownEndUser, NULL, pkgCtxId, myUnit);
			WHEN OTHERS THEN
				slog.error(pkgCtxId, myUnit, sqlcode||':'||sqlerrm);
				sspkg.raiseOraError(pkgCtxId, myUnit);
		END;

		mcauth.auth.checkNOnceDigest(pClientId => vClientId, pUsername => pUsername, pDigest => pDigest, pPassword => vPassword,pPasswordCs => vPasswordCs, ipAddress => ipAddress, host => host, langId => langId, pApplicationId => pApplicationId, pNOnce => pNOnce, pDeviceId => pDeviceId, pClientExtAuthId => NULL, pPasswordHashed => pPasswordHashed, pAppVersionId => pAppVersionId, pOSVersionId => pOSVersionId, pIsFirstLogin => pIsFirstLogin);
	END;

	PROCEDURE generateNewReactivationKey(
                pUsername IN VARCHAR2,
				pDigest IN VARCHAR2,
				ipAddress IN VARCHAR2,
				host IN VARCHAR2,
				langId IN VARCHAR2,
				pApplicationId IN VARCHAR2,
				pNOnce IN VARCHAR2,
				pDeviceId IN VARCHAR2,
				pPasswordHashed IN VARCHAR2 DEFAULT NULL,
				pDeviceType IN VARCHAR2 DEFAULT NULL,
                pValidUntil OUT DATE,
				pAppVersionId VARCHAR2 DEFAULT NULL,
				pOSVersionId VARCHAR2 DEFAULT NULL)
	IS
		myunit CONSTANT VARCHAR2(30) := 'generateNewReactivationKey';
	BEGIN

		checkNOnceDigest(pUsername => pUsername, pDigest => pDigest, ipAddress => ipAddress, host => host, langId => langId, pApplicationId => pApplicationId, pNOnce => pNOnce, pDeviceId => pDeviceId, pPasswordHashed => pPasswordHashed, pAppVersionId => pAppVersionId, pOSVersionId => pOSVersionId, pIsFirstLogin => TRUE);

		mcauth.auth.generateNewReactivationKey(pUsername => pUsername,
											   pDeviceType => pDeviceType,
											   pValidUntil => pValidUntil);
	END;

	PROCEDURE ResetPasswordWithoutSession(
        pUsername mcauth.client.username%TYPE,
        pEmail mcauth.client.email%TYPE,
        pMobilePhone mcauth.client.gsm%TYPE,
        pLangId VARCHAR2 DEFAULT 'bs')
    IS
        myunit CONSTANT VARCHAR2(30) := 'ResetPasswordWithoutSession';
    BEGIN
        slog.debug(pkgCtxId, myUnit, 'Mobile wrapper for password reset: ' || pUsername);
        mcauth.auth.ResetPasswordWithoutSession(pUsername, pEmail, pMobilePhone, pLangId);
    END ResetPasswordWithoutSession;

    PROCEDURE ConfirmPasswordResetWithVerificationCode(
        pUsername mcauth.client.username%TYPE,
        pEmail mcauth.client.email%TYPE,
        pMobilePhone mcauth.client.gsm%TYPE,
        pVerificationCode VARCHAR2,
        pLangId VARCHAR2 DEFAULT 'bs')
    IS
        myunit CONSTANT VARCHAR2(40) := 'ConfirmPasswordResetWithVerificationCode';
    BEGIN
        slog.debug(pkgCtxId, myUnit, 'Mobile wrapper for password reset confirmation: ' || pUsername);
        mcauth.auth.ConfirmPasswordResetWithVerificationCode(pUsername, pEmail, pMobilePhone, pVerificationCode, pLangId);
    END ConfirmPasswordResetWithVerificationCode;

END AUTH;
/

show errors
				