SET SERVEROUTPUT ON SIZE 1000000;

DECLARE
    vFromAddr VARCHAR2(200) := sspkg.ReadVChar('/Core/Admin/Forms/SendUsernameAddress');
    vChannelId VARCHAR2(40);
    vSubject VARCHAR2(500) := 'Novi pristupni podaci za administratorski panel';
    
    vProcessedCount NUMBER := 0;
    vSuccessCount NUMBER := 0;
    vErrorCount NUMBER := 0;

    FUNCTION generate_password(pFirstName VARCHAR2, pLastName VARCHAR2) RETURN VARCHAR2 IS
        vPassword VARCHAR2(50);
        vCleanFirstName VARCHAR2(50);
        vCleanLastName VARCHAR2(50);
    BEGIN
        vCleanFirstName := LOWER(TRANSLATE(pFirstName, 
            'ĆČĐŠŽćčđšžÁÉÍÓÚáéíóúÀÈÌÒÙàèìòùÂÊÎÔÛâêîôûÄËÏÖÜäëïöü', 
            'CCDSZccdsz'));
        vCleanLastName := LOWER(TRANSLATE(pLastName, 
            'ĆČĐŠŽćčđšžÁÉÍÓÚáéíóúÀÈÌÒÙàèìòùÂÊÎÔÛâêîôûÄËÏÖÜäëïöü', 
            'CCDSZccdsz'));
            
        vPassword := vCleanFirstName || '.' || vCleanLastName || '.bbi2025';
        
        RETURN vPassword;
    END generate_password;
    
    FUNCTION create_email_body(pUsername VARCHAR2, pPassword VARCHAR2, pFirstName VARCHAR2, pLastName VARCHAR2) RETURN VARCHAR2 IS
        vBody VARCHAR2(4000);
    BEGIN
        vBody := 'Poštovani ' || pFirstName || ' ' || pLastName || ',' || CHR(10) || CHR(10) ||
                'Vaši novi pristupni podaci za administratorski panel su:' || CHR(10) || CHR(10) ||
                'Username: ' || pUsername || CHR(10) ||
                'Password: ' || pPassword || CHR(10) || CHR(10) ||
                'Molimo vas da se prijavite na sistem i promijenite lozinku pri prvoj prilici.' || CHR(10) || CHR(10) ||
                'NAPOMENA: Ova lozinka je privremena i trebate je promijeniti nakon prve prijave.' || CHR(10) || CHR(10) ||
                'Srdačan pozdrav,' || CHR(10) ||
                'IT Tim';
        
        RETURN vBody;
    END create_email_body;

BEGIN
    DBMS_OUTPUT.PUT_LINE('=== ADMINISTRATOR PASSWORD RESET AND EMAIL NOTIFICATION ===');
    DBMS_OUTPUT.PUT_LINE('Start time: ' || TO_CHAR(SYSDATE, 'DD.MM.YYYY HH24:MI:SS'));
    DBMS_OUTPUT.PUT_LINE('');
    
    BEGIN
        vChannelId := sspkg.ReadVChar('/Core/Main/TranPays/Attachments/MsgChannelId');
        IF vChannelId IS NULL THEN
            vChannelId := 'DEFAULT';
        END IF;
    EXCEPTION
        WHEN OTHERS THEN
            vChannelId := 'DEFAULT';
    END;
    
    DBMS_OUTPUT.PUT_LINE('Using email channel: ' || vChannelId);
    DBMS_OUTPUT.PUT_LINE('From address: ' || vFromAddr);
    DBMS_OUTPUT.PUT_LINE('');
    
    FOR admin_rec IN (
        SELECT 
            id,
            username,
            first_name,
            last_name,
            email,
            enabled
        FROM mcadmin.administrator 
        WHERE enabled = 1 
        AND email IS NOT NULL 
        AND TRIM(email) != ''
        ORDER BY last_name, first_name
    ) LOOP
        
        vProcessedCount := vProcessedCount + 1;
        
        DECLARE
            vNewPassword VARCHAR2(100);
            vPasswordHash VARCHAR2(200);
            vEmailBody VARCHAR2(4000);
            vCurrentAdmin VARCHAR2(200);
        BEGIN
            vCurrentAdmin := admin_rec.first_name || ' ' || admin_rec.last_name || ' (' || admin_rec.username || ')';
            
            DBMS_OUTPUT.PUT_LINE('Processing: ' || vCurrentAdmin);
            DBMS_OUTPUT.PUT_LINE('  Email: ' || admin_rec.email);
            
            vNewPassword := generate_password(admin_rec.first_name, admin_rec.last_name);
            DBMS_OUTPUT.PUT_LINE('  Generated password: ' || vNewPassword);
            
            SELECT mcadmin.auth.hash(vNewPassword) INTO vPasswordHash FROM dual;
            DBMS_OUTPUT.PUT_LINE('  Password hash generated');
            
            UPDATE mcadmin.administrator 
            SET password_hash = vPasswordHash
            WHERE id = admin_rec.id;
            
            DBMS_OUTPUT.PUT_LINE('  Database updated');
            
            vEmailBody := create_email_body(admin_rec.username, vNewPassword, admin_rec.first_name, admin_rec.last_name);
            
            BEGIN
                mcore.send_mail(
                    fromAddr => vFromAddr,
                    toAddr => admin_rec.email,
                    subject => vSubject,
                    bodyMsg => vEmailBody,
                    MC_ID => vChannelId,
                    msgCategory => 'CONFIDENTIAL'
                );
                
                DBMS_OUTPUT.PUT_LINE('  Email sent successfully');
                vSuccessCount := vSuccessCount + 1;
                
            EXCEPTION
                WHEN OTHERS THEN
                    DBMS_OUTPUT.PUT_LINE('  ERROR sending email: ' || SQLERRM);
                    vErrorCount := vErrorCount + 1;
            END;
            
            DBMS_OUTPUT.PUT_LINE('  Status: COMPLETED');
            DBMS_OUTPUT.PUT_LINE('');
            
            COMMIT;
            
        EXCEPTION
            WHEN OTHERS THEN
                DBMS_OUTPUT.PUT_LINE('  ERROR processing administrator: ' || SQLERRM);
                vErrorCount := vErrorCount + 1;
                ROLLBACK;
        END;
        
    END LOOP;
    
    -- Final commit
    COMMIT;
    
    DBMS_OUTPUT.PUT_LINE('=== SUMMARY ===');
    DBMS_OUTPUT.PUT_LINE('Total administrators processed: ' || vProcessedCount);
    DBMS_OUTPUT.PUT_LINE('Successful operations: ' || vSuccessCount);
    DBMS_OUTPUT.PUT_LINE('Failed operations: ' || vErrorCount);
    DBMS_OUTPUT.PUT_LINE('End time: ' || TO_CHAR(SYSDATE, 'DD.MM.YYYY HH24:MI:SS'));
    
    IF vErrorCount > 0 THEN
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('WARNING: Some operations failed. Please check the log above.');
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        DBMS_OUTPUT.PUT_LINE('FATAL ERROR: ' || SQLERRM);
        RAISE;
END;
/

-- Provjeri rezultate
PROMPT 
PROMPT === VERIFICATION QUERIES ===
PROMPT 

PROMPT Recently updated administrators:
SELECT 
    username,
    first_name,
    last_name,
    email,
    TO_CHAR(date_modified, 'DD.MM.YYYY HH24:MI:SS') as last_modified,
    CASE WHEN password_hash IS NOT NULL THEN 'YES' ELSE 'NO' END as has_password
FROM mcadmin.administrator 
WHERE enabled = 1 
AND date_modified > SYSDATE - 1
ORDER BY date_modified DESC;

PROMPT 
PROMPT Administrators without email addresses:
SELECT 
    username,
    first_name,
    last_name,
    email,
    enabled
FROM mcadmin.administrator 
WHERE enabled = 1 
AND (email IS NULL OR TRIM(email) = '')
ORDER BY last_name, first_name;

PROMPT 
PROMPT === NOTES ===
PROMPT 1. All passwords follow the pattern: firstname.lastname.bbi2025
PROMPT 2. Diacritics are removed and converted to lowercase
PROMPT 3. Only enabled administrators with valid email addresses were processed
PROMPT 4. Each administrator should change their password after first login
PROMPT 5. Check email delivery status in your email system
PROMPT
