PROMPT Creating package specification script for package AUTH
CREATE OR REPLACE
PACKAGE MMOBILE.AUTH AS

	/* Core authentication package */
	pkgCtxId CONSTANT VARCHAR2(20) := '/App-DB/MMobile/Auth';

	mcsmDEVID_Var CONSTANT VARCHAR2(9) := 'DEVICE_ID';

	/********************/
	/* LOGIN AND LOGOUT */
	/********************/

	/* This method login user to database and create session in session mgmt subsystem.
	Method return true and sessionId if authentication succeeded. If there is need to additionally
	authenticate with ex. otp then otpType will be not null.
	Additional information about login procedure success can be get with
	getLoginStatusCode and getLoginStatusDetails.
	*/

	function basicLogin(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
						certificate VARCHAR2, application_id VARCHAR2, otpType out VARCHAR2,
						sessionId OUT VARCHAR2, sessLogLevel integer := NULL, langId VARCHAR2 := NULL,
						pInstallationId VARCHAR2 := NULL, pDeviceId VARCHAR2 := NULL, pPasswordExpireInDays OUT PLS_INTEGER,
						pNOnce IN VARCHAR2 DEFAULT NULL, pUserId OUT PLS_INTEGER, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL, pIsFirstLogin IN BOOLEAN DEFAULT FALSE) RETURN BOOLEAN;

	/* This method provides additional credentional for authentication purpose.
	otp is otp code sent by user.
	sessionId is id of session returned by basicLogin
	If login is successfull result is true, otherwise is false.
	Additional information about login procedure success can be get with
	getLoginStatusCode and getLoginStatusDetails
	*/
	function extendedLogin(sessionId VARCHAR2, otp VARCHAR2, challenge VARCHAR2 := NULL) RETURN BOOLEAN;

	function extendedLogin(sessionId VARCHAR2, otp VARCHAR2, challenge VARCHAR2 := NULL,
					pLicenses OUT sys_refcursor, pAccountOwners OUT sys_refcursor) RETURN BOOLEAN;

	/* This method hook on session by referencing session id and client ip address. */
	procedure opens(sessionId VARCHAR2, ipaddress VARCHAR2 DEFAULT NULL);

	/* */
	FUNCTION getDeviceAuthorizationStatus RETURN VARCHAR2;

	/* Return last login status. NULL means that there were no issues, otherwise this function returns error code
	The error codes are described and listed in system setup branch: /Core/Auth/err
	*/
	function getLoginStatusCode RETURN VARCHAR2;

	/* Return last status message */
	function getLoginStatusDetails RETURN VARCHAR2;

	/* Return true if client - specified by username needs PKI login */
	function clientRequirePKILogin(vusername VARCHAR2, pDeviceId VARCHAR2, pApplicationId VARCHAR2) RETURN BOOLEAN;

	/* Return true if session client - needs PKI login */
	function clientRequirePKILogin RETURN BOOLEAN;

	/* GSM login, this is a login that does not use session manager. Current client id is stored as package session parameter.
	This login method is used to authenticate inbound sms messages (ex. sms banking) */
	FUNCTION GsmLogin(vgsm VARCHAR2, vpassword VARCHAR2, vapplication VARCHAR2) RETURN BOOLEAN;

	/* Gsm logout - reset package session variable */
	PROCEDURE GsmLogout;

	/* This method close hook on previously hooked session. */
	procedure closes;

	/*This method provides logout user session */
	procedure logout;

	/********************/
	/* PASSWORD MGMT    */
	/********************/
	/* This method provides reset static password feature. Function will send password thru email or sms to client. */
	function resetPasswordRQ(contactClient VARCHAR2, secQuestionID VARCHAR2, secQuestionAnswer VARCHAR2) RETURN BOOLEAN;

	/* This method provides change static password feature. Such as motp or end-user 'device' generated otp
	oldPassword is old password
	newPassword is new password
	otp is optional and should be NULL only if user does not use OTP features. Programmer should check by calling function clientOTPType.*/
	function changePassword(oldPassword VARCHAR2, newPassword VARCHAR2, otp VARCHAR2 := NULL, challenge VARCHAR2 := NULL, pNOnce IN VARCHAR2 := NULL) RETURN BOOLEAN;

	/********************/
	/* OTP RELATED      */
	/********************/

	/* This method check if OTP that client sent is ok. This method is used internally by extendedLogin and is exposed for checking otp.*/
	function checkOTP(otp VARCHAR2) RETURN BOOLEAN;

	/* This method generate sms or email OTP (depend on client configuration) and send them to client. This method is used internally by extendedLogin and is exposed for generating sms or email otp.*/
	procedure genSendOTP;

	/* This method RETURN client OTP type if client use such feature. */
	function clientOTPType RETURN VARCHAR2;

	/* Generate challenge for challenge response */
	function genChallenge(sourceData VARCHAR2 := dbms_random.string('A', 24)) RETURN VARCHAR2;

	/* Check response in challenge response scenario */
	function checkCHRESP(challenge VARCHAR2, response VARCHAR2) RETURN BOOLEAN;

	/********************/
	/* DATA SIGNING     */
	/********************/

	/* This method RETURN details regarding signature functionalities that are assigned to Meerkat user (bank client). This info will be mainly used to program if some additional signing 'form' will be opened to end-client to enter signature or to calculate one.*/
	function clientSignatureMethod RETURN VARCHAR2;
	FUNCTION clientSignatureMethod(pUsername VARCHAR2, pDeviceId VARCHAR2 := NULL) RETURN VARCHAR2;

	FUNCTION clientSignatureOtpType RETURN VARCHAR2;
	FUNCTION clientSignatureOtpType(pUsername VARCHAR2, pDeviceId VARCHAR2 := NULL) RETURN VARCHAR2;


	/* This method check if data was signed properly (ex. challenge/request, digital signature...).*/
	function checkSignature(sourceData VARCHAR2, signature VARCHAR2) RETURN BOOLEAN;

	/********************/
	/* READ AND WRITE     */
	/********************/

	-- Write varName. Session should be hooked before with opens
	PROCEDURE write(varName VARCHAR2, varValue VARCHAR2);

	-- Read varName. Session should be hooked before with opens
	FUNCTION read(varName VARCHAR2) RETURN VARCHAR2;

	-- Read modification time of varName. Session should be hooked before with opens
	FUNCTION readMDate(varName VARCHAR2) RETURN DATE;


	/********************/
	/* AUXILARY METHODS */
	/********************/

	/* Return hash value */
	FUNCTION hash(var VARCHAR2) RETURN VARCHAR2;

	-- Set language
	PROCEDURE setLang(langId VARCHAR2);

	-- Get language
	FUNCTION getLang RETURN VARCHAR2;

	/* get Current Session USER ID (username) */
	function getSUser RETURN VARCHAR2;

	/* get Current Session Client ID. Return NULL if client is not logged. */
	function getSCID RETURN VARCHAR2;

	/* get gsm Session Client ID. Return NULL if client is not logged. */
	FUNCTION getGsmSCID RETURN NUMBER;

	/* get gsm session phone number */
	FUNCTION getGsmSPhone RETURN VARCHAR2;

	FUNCTION getGsmForCID RETURN VARCHAR2;

	/* Return true if username is locked and RETURN dates when lock is issued and when will expire */
	function isUsernameLocked(vusername VARCHAR2, sinceDate OUT DATE, untilDate OUT DATE) RETURN BOOLEAN;

	/* Return true if username is locked */
	function isUsernameLocked(vusername VARCHAR2) RETURN BOOLEAN;

	/* Return true if gsm is locked and RETURN dates when lock is issued and when will expire */
	function isGsmLocked(vgsm VARCHAR2, sinceDate OUT DATE, untilDate OUT DATE) RETURN BOOLEAN;

	/* Return true if gsm is locked */
	function isGsmLocked(vgsm VARCHAR2) RETURN BOOLEAN;

	-- This function RETURN extauth id. If NULL is returned then user does not use additional OTP.
	-- Extauth_id are configured in branch /Core/Auth/ExtAuth/<ExtAuthID>

	FUNCTION getExtAuthId RETURN VARCHAR2;

	/* get Current Session _APPLICATIONID (application id) */
	function getSApp RETURN VARCHAR2;

	/* Return previous login */
	FUNCTION getPreviousLoginInfo RETURN SYS_REFCURSOR;
	FUNCTION getPreviousLogins RETURN SYS_REFCURSOR;

	FUNCTION isSessionExpired RETURN BOOLEAN;
	FUNCTION p_isSessionExpired RETURN PLS_INTEGER;

	FUNCTION getDayOfWeeks RETURN sys_refcursor;

	FUNCTION getAccountOwner
	RETURN VARCHAR2;

	FUNCTION getPrimaryAccountOwner
	RETURN VARCHAR2;

	PROCEDURE setAccountOwner(pAccOwnerId IN VARCHAR2);

	FUNCTION generateTanList(pThrId OUT PLS_INTEGER,
		pTanDim OUT PLS_INTEGER, pOtpLength OUT PLS_INTEGER,
		pIndexD1Count OUT PLS_INTEGER, pIndexD1Type OUT VARCHAR2,
		pIndexD2Count OUT PLS_INTEGER, pIndexD2Type OUT VARCHAR2
		)
	RETURN sys_refcursor;

	PROCEDURE activateNewTanList;

	function isAlmostDepleted RETURN VARCHAR2;

	function registerDevice(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2,
		host VARCHAR2, certificate VARCHAR2, pApplicationId VARCHAR2 := NULL,
		sessLogLevel integer := NULL, langId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL,
		pDeviceId VARCHAR2, pDeviceDescription VARCHAR2, pActivationKey VARCHAR2, pExtAuthId VARCHAR2, pStatusMessage OUT VARCHAR2,
		pOperatingSystem IN VARCHAR2 := NULL, pUserIdentifier IN VARCHAR2 := NULL, pDeviceType IN VARCHAR2 := NULL, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId VARCHAR2 DEFAULT NULL)
	RETURN VARCHAR2;

	FUNCTION getListOfAuthorizedDevices RETURN sys_refcursor;

	FUNCTION getListOfAuthorizedDevices(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
		sessLogLevel INTEGER := NULL, langId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pDeviceId VARCHAR2 DEFAULT NULL, pAppVersionId VARCHAR2 DEFAULT NULL, pOSVersionId VARCHAR2 DEFAULT NULL)
	RETURN SYS_REFCURSOR;

	PROCEDURE unregisterDevice(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
		sessLogLevel INTEGER := NULL, langId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL, pAppExtAuthId NUMBER, pComment VARCHAR2 := NULL, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pDeviceId VARCHAR2 DEFAULT NULL, pAppVersionId VARCHAR2 DEFAULT NULL, pOSVersionId VARCHAR2 DEFAULT NULL);

	/* Required for mOnce functionality */
	FUNCTION getPassword(pUsername mcauth.client.username%TYPE)
	RETURN mcauth.client.password%TYPE;

	PROCEDURE sendSMSMessage (pMessage VARCHAR2);

	--PROCEDURE setNOnceDigestType(pDigestType IN VARCHAR2 DEFAULT 'PASSWORD');
	FUNCTION setExtAuthId(pExtAuthId IN VARCHAR2)
	RETURN NUMBER;

	FUNCTION getEncryptionKey(pId NUMBER)
	RETURN VARCHAR2;

	FUNCTION isPasswordForSignatureRequired
	RETURN BOOLEAN;

	PROCEDURE updateDeviceId(pUsername VARCHAR2, pPassword VARCHAR2, pDeviceId VARCHAR2, pSecKey VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
      		sessLogLevel INTEGER := NULL, pApplicationId VARCHAR2, langId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pAppVersionId VARCHAR2 DEFAULT NULL, pOSVersionId VARCHAR2 DEFAULT NULL);

	/* Valide PIN complexity */
	PROCEDURE checkMobilePINComplexity(pin IN VARCHAR2);

	/* Check PIN */
	PROCEDURE checkMobilePin(pin IN varchar2);

	/* Set PIN */
	PROCEDURE addMobilePin(pin IN varchar2,
		pChallenge varchar2 := NULL, pResponse varchar2 := NULL, pOtp varchar2 := NULL, pSourceData varchar2 := NULL,pSignature VARCHAR2 := NULL);

	/* Change existing PIN */
	PROCEDURE changeMobilePin(newPIN IN varchar2, oldPIN IN varchar2,
		pChallenge varchar2 := NULL, pResponse varchar2 := NULL, pOtp varchar2 := NULL, pSourceData varchar2 := NULL,pSignature VARCHAR2 := NULL);

	PROCEDURE reauthorizeDeviceWithPassword (pPassword IN VARCHAR2, pPasswordHashed IN VARCHAR2 DEFAULT NULL);
	FUNCTION verifyClientPassword (pPassword IN VARCHAR2) RETURN BOOLEAN;

	PROCEDURE setMOTP_USE_PIN(pMOtpUsePIN VARCHAR2);

	FUNCTION getMOTP_USE_PIN
	RETURN VARCHAR2;

	PROCEDURE setRqrReauthFlagForDevice;

	PROCEDURE generateNewReactivationKey(
                pUsername IN VARCHAR2,
				pDigest IN VARCHAR2,
				ipAddress IN VARCHAR2,
				host IN VARCHAR2,
				langId IN VARCHAR2,
				pApplicationId IN VARCHAR2,
				pNOnce IN VARCHAR2,
				pDeviceId IN VARCHAR2,
				pPasswordHashed IN VARCHAR2 DEFAULT NULL,
				pDeviceType IN VARCHAR2 DEFAULT NULL,
                pValidUntil OUT DATE,
				pAppVersionId VARCHAR2 DEFAULT NULL,
				pOSVersionId VARCHAR2 DEFAULT NULL);

	PROCEDURE passwordReset(
        pUsername mcauth.client.username%TYPE,
        pEmail mcauth.client.email%TYPE,
        pMobilePhone mcauth.client.gsm%TYPE,
        pLangId VARCHAR2 DEFAULT 'bs',
        pVerificationCode OUT VARCHAR2,
        pValidUntil OUT DATE,
        pUserId OUT NUMBER);

    PROCEDURE confirmPasswordReset(
        pVerificationCode VARCHAR2,
        pLangId VARCHAR2 DEFAULT 'bs',
        pUserId NUMBER DEFAULT NULL);
END AUTH;
/
show error

GRANT EXECUTE ON MMOBILE.AUTH TO MMOBILE_USER;
