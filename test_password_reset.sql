-- Test skript za passwordReset i confirmPasswordReset procedure
-- Autor: AI Assistant
-- Datum: 2025-07-14

SET SERVEROUTPUT ON SIZE 1000000;

DECLARE
    -- Test podaci
    vTestUsername VARCHAR2(100) := 'testuser';
    vTestEmail VARCHAR2(200) := '<EMAIL>';
    vTestMobile VARCHAR2(50) := '+38761123456';
    vTestLang VARCHAR2(2) := 'bs';
    vClientId NUMBER;
    
    -- Procedure za test setup
    PROCEDURE setup_test_user IS
    BEGIN
        -- O<PERSON><PERSON><PERSON> posto<PERSON> test korisnika ako postoji
        DELETE FROM mcauth.client_details WHERE client_id IN (
            SELECT id FROM mcauth.client WHERE username = vTestUsername
        );
        DELETE FROM mcauth.client WHERE username = vTestUsername;
        
        -- <PERSON><PERSON>iraj test korisnika
        INSERT INTO mcauth.client (
            id, username, password, password_cs, name, description, enabled, 
            valid_from, valid_to, email, gsm, password_enabled
        ) VALUES (
            mcauth.client_seq.NEXTVAL, vTestUsername, 
            mcauth.auth.hash('TESTPASS'), mcauth.auth.hash('testpass'),
            'Test User', 'Test user for password reset', 1,
            SYSDATE - 30, SYSDATE + 365, vTestEmail, vTestMobile, 1
        );
        
        SELECT id INTO vClientId FROM mcauth.client WHERE username = vTestUsername;
        DBMS_OUTPUT.PUT_LINE('Test user created with ID: ' || vClientId);
        COMMIT;
    END setup_test_user;
    
    -- Procedure za cleanup
    PROCEDURE cleanup_test_user IS
    BEGIN
        DELETE FROM mcauth.client_details WHERE client_id = vClientId;
        DELETE FROM mcauth.client WHERE id = vClientId;
        COMMIT;
        DBMS_OUTPUT.PUT_LINE('Test user cleaned up');
    END cleanup_test_user;
    
    -- Test procedure
    PROCEDURE run_test(pTestName VARCHAR2, pTestProc VARCHAR2) IS
    BEGIN
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('=== TEST: ' || pTestName || ' ===');
        EXECUTE IMMEDIATE pTestProc;
        DBMS_OUTPUT.PUT_LINE('TEST PASSED: ' || pTestName);
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('TEST FAILED: ' || pTestName || ' - ' || SQLERRM);
    END run_test;

BEGIN
    DBMS_OUTPUT.PUT_LINE('Starting Password Reset Tests');
    DBMS_OUTPUT.PUT_LINE('============================');
    
    -- Setup
    setup_test_user;
    
    -- Test 1: Uspješno generiranje verifikacijskog koda
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== TEST: Generate Verification Code ===');
    BEGIN
        mcauth.auth.passwordReset(
            pUsername => vTestUsername,
            pEmail => vTestEmail,
            pMobilePhone => vTestMobile,
            pLangId => vTestLang,
            pUserId => vClientId
        );
        DBMS_OUTPUT.PUT_LINE('User ID: ' || vClientId);
        DBMS_OUTPUT.PUT_LINE('TEST PASSED: Generate Verification Code');
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('TEST FAILED: Generate Verification Code - ' || SQLERRM);
    END;
    
    -- Čekaj malo da se kod generiše
    DBMS_LOCK.SLEEP(1);
    
    -- Provjeri da li je kod kreiran
    DECLARE
        vStoredCodeHash VARCHAR2(200);
    BEGIN
        SELECT data_vchar INTO vStoredCodeHash
        FROM mcauth.client_details
        WHERE client_id = vClientId AND attrib_id = 'PWD_RESET_VERIF_CODE';
        DBMS_OUTPUT.PUT_LINE('Verification code stored successfully');
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            DBMS_OUTPUT.PUT_LINE('ERROR: Verification code not found in database');
    END;
    
    -- Test 2: Pokušaj ponovnog slanja koda (treba da ne uspije)
    DECLARE
        vDummyId NUMBER;
    BEGIN
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('=== TEST: Duplicate Code Generation (should fail) ===');
        mcauth.auth.passwordReset(
            pUsername => vTestUsername,
            pEmail => vTestEmail,
            pMobilePhone => vTestMobile,
            pLangId => vTestLang,
            pUserId => vDummyId
        );
        DBMS_OUTPUT.PUT_LINE('TEST FAILED: Duplicate Code Generation - Should have failed');
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('TEST PASSED: Duplicate Code Generation - Failed as expected: ' || SQLERRM);
    END;
    
    -- Test 3: Neispravni podaci za korisnika
    DECLARE
        vDummyId NUMBER;
    BEGIN
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('=== TEST: Invalid User Data (should fail) ===');
        mcauth.auth.passwordReset(
            pUsername => 'nonexistent',
            pEmail => vTestEmail,
            pMobilePhone => vTestMobile,
            pLangId => vTestLang,
            pUserId => vDummyId
        );
        DBMS_OUTPUT.PUT_LINE('TEST FAILED: Invalid User Data - Should have failed');
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('TEST PASSED: Invalid User Data - Failed as expected: ' || SQLERRM);
    END;
    
    -- Test 4: Prazni parametri
    DECLARE
        vDummyId NUMBER;
    BEGIN
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('=== TEST: Empty Parameters (should fail) ===');
        mcauth.auth.passwordReset(
            pUsername => '',
            pEmail => '',
            pMobilePhone => '',
            pLangId => vTestLang,
            pUserId => vDummyId
        );
        DBMS_OUTPUT.PUT_LINE('TEST FAILED: Empty Parameters - Should have failed');
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('TEST PASSED: Empty Parameters - Failed as expected: ' || SQLERRM);
    END;
    
    -- Test 5: Potvrda sa neispravnim kodom
    BEGIN
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('=== TEST: Invalid Verification Code (should fail) ===');
        mcauth.auth.confirmPasswordReset(
            pVerificationCode => '123456',
            pLangId => vTestLang,
            pUserId => vClientId
        );
        DBMS_OUTPUT.PUT_LINE('TEST FAILED: Invalid Verification Code - Should have failed');
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('TEST PASSED: Invalid Verification Code - Failed as expected: ' || SQLERRM);
    END;
    
    -- Test 6: Potvrda sa ispravnim kodom
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== TEST: Valid Verification Code ===');
    DBMS_OUTPUT.PUT_LINE('Note: This test requires the actual verification code from the first test');
    DBMS_OUTPUT.PUT_LINE('Example call: mcauth.auth.confirmPasswordReset(pVerificationCode => ''123456'', pLangId => ''bs'', pUserId => ' || vClientId || ')');
    
    -- Test 7: Provjeri da li su podaci u client_details tabeli
    DECLARE
        vCodeCount NUMBER;
        vValidUntilCount NUMBER;
    BEGIN
        SELECT COUNT(*) INTO vCodeCount 
        FROM mcauth.client_details 
        WHERE client_id = vClientId AND attrib_id = 'PWD_RESET_VERIF_CODE';
        
        SELECT COUNT(*) INTO vValidUntilCount 
        FROM mcauth.client_details 
        WHERE client_id = vClientId AND attrib_id = 'PWD_RESET_VERIF_VALID_UNTIL';
        
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('=== DATABASE STATE CHECK ===');
        DBMS_OUTPUT.PUT_LINE('Verification codes in database: ' || vCodeCount);
        DBMS_OUTPUT.PUT_LINE('Valid until records in database: ' || vValidUntilCount);
        
        -- Prikaži detalje
        FOR rec IN (
            SELECT attrib_id, data_vchar 
            FROM mcauth.client_details 
            WHERE client_id = vClientId 
            AND attrib_id IN ('PWD_RESET_VERIF_CODE', 'PWD_RESET_VERIF_VALID_UNTIL')
        ) LOOP
            IF rec.attrib_id = 'PWD_RESET_VERIF_CODE' THEN
                DBMS_OUTPUT.PUT_LINE('Stored code hash: ' || SUBSTR(rec.data_vchar, 1, 20) || '...');
            ELSE
                DBMS_OUTPUT.PUT_LINE('Valid until: ' || rec.data_vchar);
            END IF;
        END LOOP;
    END;
    
    -- Test 8: Test različitih jezika
    DECLARE
        vDummyId NUMBER;
    BEGIN
        -- Prvo obrišimo postojeći kod da možemo testirati
        DELETE FROM mcauth.client_details
        WHERE client_id = vClientId
        AND attrib_id IN ('PWD_RESET_VERIF_CODE', 'PWD_RESET_VERIF_VALID_UNTIL');

        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('=== TEST: Different Language (EN) ===');
        mcauth.auth.passwordReset(
            pUsername => vTestUsername,
            pEmail => vTestEmail,
            pMobilePhone => vTestMobile,
            pLangId => 'en',
            pUserId => vDummyId
        );
        DBMS_OUTPUT.PUT_LINE('User ID: ' || vDummyId);
        DBMS_OUTPUT.PUT_LINE('TEST PASSED: Different Language (EN)');
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('TEST FAILED: Different Language (EN) - ' || SQLERRM);
    END;
    
    -- Cleanup
    cleanup_test_user;
    
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('============================');
    DBMS_OUTPUT.PUT_LINE('Password Reset Tests Completed');
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('MANUAL TESTING INSTRUCTIONS:');
    DBMS_OUTPUT.PUT_LINE('1. Create a real user with valid email and mobile');
    DBMS_OUTPUT.PUT_LINE('2. Call passwordReset with real data');
    DBMS_OUTPUT.PUT_LINE('3. Check email for verification code');
    DBMS_OUTPUT.PUT_LINE('4. Call confirmPasswordReset with received code');
    DBMS_OUTPUT.PUT_LINE('5. Check SMS for new password');
    
END;
/

-- Dodatni test za provjeru audit logova
SELECT 
    logtype_id,
    description,
    logdate,
    client_id,
    logstatus,
    status_code,
    status_message
FROM mcauth.clientLog 
WHERE logtype_id = 'PASSWORD_RESET' 
AND logdate > SYSDATE - 1
ORDER BY logdate DESC;

-- Provjeri da li postoje potrebni setup parametri
SELECT 
    '/Core/Admin/Forms/SendPasswordResetVerificationAddress' as parameter_name,
    sspkg.ReadVChar('/Core/Admin/Forms/SendPasswordResetVerificationAddress') as value
FROM dual
UNION ALL
SELECT 
    '/Core/Main/TranPays/Attachments/MsgChannelId' as parameter_name,
    sspkg.ReadVChar('/Core/Main/TranPays/Attachments/MsgChannelId') as value
FROM dual;

-- Provjeri da li su potrebne error poruke definirane u sistemu
PROMPT Checking if required error message translations exist...
SELECT 
    '/Core/Auth/err/InvalidUserData/Username' as message_id,
    CASE WHEN sspkg.isExistingNode('/Core/Auth/err/InvalidUserData/Username') THEN 'EXISTS' ELSE 'MISSING' END as status
FROM dual
UNION ALL
SELECT 
    '/Core/Auth/err/InvalidUserData/Email' as message_id,
    CASE WHEN sspkg.isExistingNode('/Core/Auth/err/InvalidUserData/Email') THEN 'EXISTS' ELSE 'MISSING' END as status
FROM dual
UNION ALL
SELECT 
    '/Core/Auth/err/InvalidUserData/MobilePhone' as message_id,
    CASE WHEN sspkg.isExistingNode('/Core/Auth/err/InvalidUserData/MobilePhone') THEN 'EXISTS' ELSE 'MISSING' END as status
FROM dual
UNION ALL
SELECT 
    '/Core/Auth/err/InvalidVerificationCode' as message_id,
    CASE WHEN sspkg.isExistingNode('/Core/Auth/err/InvalidVerificationCode') THEN 'EXISTS' ELSE 'MISSING' END as status
FROM dual;

PROMPT 
PROMPT Setup instructions for missing translations:
PROMPT 1. Add error message translations in SSetup
PROMPT 2. Configure email settings for verification codes
PROMPT 3. Test with real email addresses
