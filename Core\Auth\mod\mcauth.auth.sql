PROMPT Creating package specification script for package AUTH
CREATE OR REPLACE
PACKAGE MCAUTH.AUTH AS

	/* Core authentication package */
	pkgCtxId CONSTANT VARCHAR2(10) := '/Core/Auth';
	extAuthCtx CONSTANT VARCHAR2(18) := '/Core/Auth/ExtAuth';

	/********************/
	/* LOGIN AND LOGOUT */
	/********************/

	/* This method login user to database and create session in session mgmt subsystem.
	Method return true and sessionId if authentication succeeded. If there is need to additionally
	authenticate with ex. otp then otpType will be not null.
	Additional information about login procedure success can be get with
	getLoginStatusCode and getLoginStatusDetails.
	*/
	FUNCTION basicLogin(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2,
						host VARCHAR2, certificate VARCHAR2, otpType OUT VARCHAR2,
						sessionId OUT VARCHAR2, sessLogLevel INTEGER := null,
						langId VARCHAR2 := null, pApplicationId VARCHAR2 := null,
						pInstallationId VARCHAR2 := null, pDeviceId VARCHAR2 := null, pPasswordExpireInDays OUT PLS_INTEGER,
						pNOnce IN VARCHAR2 DEFAULT NULL, pUserId OUT PLS_INTEGER, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL, pIsFirstLogin IN BOOLEAN DEFAULT FALSE)
	RETURN BOOLEAN;

	/* This method provides additional credentional for authentication purpose.
	otp is otp code sent by user.
	sessionId is id of session returned by basicLogin
	If login is successfull result is true, otherwise is false.
	Additional information about login procedure success can be get with
	getLoginStatusCode and getLoginStatusDetails
	*/
	FUNCTION extendedLogin(sessionId VARCHAR2, otp VARCHAR2, challenge VARCHAR2 := null) RETURN BOOLEAN;

	/* This method hook on session by referencing session id. */
	PROCEDURE opens(sessionId VARCHAR2);
	/* This method close hook on previously hooked session. */

	/* */
    FUNCTION getDeviceAuthorizationStatus return VARCHAR2;

	/* Return last login status. NULL means that there were no issues, otherwise this FUNCTION returns error code
	The error codes are described and listed in system setup branch: /Core/Auth/err
	*/
	FUNCTION getLoginStatusCode return VARCHAR2;

	/* Return last status message */
	FUNCTION getLoginStatusDetails return VARCHAR2;

	/* Return true if client - specified by username needs PKI login */
	FUNCTION clientRequirePKILogin(vusername VARCHAR2, pDeviceId VARCHAR2, pApplicationId VARCHAR2, pClientExtAuthId app_extauth.extauth_id%TYPE := NULL) RETURN BOOLEAN;

	/* Return true if session client - needs PKI login */
	FUNCTION clientRequirePKILogin(pClientExtAuthId app_extauth.extauth_id%TYPE := NULL) RETURN BOOLEAN;

	/* GSM login, this is a login that does not use session manager. Current client id is stored as package session parameter.
	This login method is used to authenticate inbound sms messages (ex. sms banking) */
	FUNCTION GsmLogin(vgsm VARCHAR2, vpassword VARCHAR2, vapplication VARCHAR2) RETURN BOOLEAN;

	/* Gsm logout - reset package session variable */
	PROCEDURE GsmLogout;

	PROCEDURE closes;
	/*This method provides logout user session */
	PROCEDURE logout;

	/********************/
	/* PASSWORD MGMT    */
	/********************/
	/* This method provides reset static password feature. Function will send password thru email or sms to client. */
	FUNCTION resetPasswordRQ(contactClient VARCHAR2, secQuestionID VARCHAR2, secQuestionAnswer VARCHAR2) RETURN BOOLEAN;

	/* This method provides change static password feature. Such as motp or end-user 'device' generated otp
	oldPassword is old password
	newPassword is new password
	otp is optional and should be null only if user does not use OTP features. Programmer should check by calling FUNCTION clientOTPType.*/
	FUNCTION changePassword(oldPassword VARCHAR2, newPassword VARCHAR2, otp VARCHAR2 := null, challenge VARCHAR2 := NULL, pNOnce IN VARCHAR2 := NULL) RETURN BOOLEAN;

	/* Changes password used by GSM application (SMS MOBILE)*/
	FUNCTION changeGSMPassword(vgsm VARCHAR2, voldpassword VARCHAR2, newPassword VARCHAR2)
	RETURN BOOLEAN;

	/********************/
	/* OTP RELATED      */
	/********************/

	/* This method check if OTP that client sent is ok. This method is used internally by extendedLogin and is exposed for checking otp.*/
	FUNCTION checkOTP(otp VARCHAR2) RETURN BOOLEAN;

	/* This method generate sms or email OTP (depend on client configuration) and send them to client. This method is used internally by extendedLogin and is exposed for generating sms or email otp.*/
	PROCEDURE genSendOTP;

	/* This method returns client ExtAuth if client use such feature. */
	FUNCTION clientExtAuthID(pUsername VARCHAR2, pDeviceId IN OUT VARCHAR2, pApplicationId VARCHAR2)
	RETURN app_extauth.extauth_id%TYPE;

	/* This method return client OTP type if client use such feature. */
	FUNCTION clientOTPType(pClientExtAuthId app_extauth.extauth_id%TYPE := NULL) return VARCHAR2;

	FUNCTION clientLoginMethod(pUsername VARCHAR2, pDeviceId VARCHAR2, pApplicationId VARCHAR2, pClientExtAuthId app_extauth.extauth_id%TYPE := NULL) RETURN VARCHAR2;

	/* Generate challenge for challenge response */
	FUNCTION genChallenge(sourceData VARCHAR2 := dbms_random.string('A', 24)) return VARCHAR2;

	/* Check response in challenge response scenario */
	FUNCTION checkCHRESP(challenge VARCHAR2, response VARCHAR2) RETURN BOOLEAN;

	PROCEDURE checkSignature(pChallenge IN VARCHAR2,
							pResponse IN VARCHAR2,
							pOtp IN VARCHAR2,
							pSourceData IN VARCHAR2,
							pSignature IN VARCHAR2,
							pCheckResult OUT BOOLEAN,
							pClientSignatureMethod OUT VARCHAR2,
							pClientOTPType OUT VARCHAR2,
							pErrorCode OUT VARCHAR2);

	/********************/
	/* DATA SIGNING     */
	/********************/

	/* This method return details regarding signature FUNCTIONalities that are assigned to Meerkat user (bank client). This info will be mainly used to program if some additional signing 'form' will be opened to end-client to enter signature or to calculate one.*/
	FUNCTION clientSignatureMethod(pClientExtAuthId app_extauth.extauth_id%TYPE := NULL) return VARCHAR2;

	/* This method check if data was signed properly (ex. challenge/request, digital signature...).*/
	FUNCTION checkSignature(sourceData VARCHAR2, signature VARCHAR2) RETURN BOOLEAN;

	/********************/
	/* READ AND WRITE     */
	/********************/

	-- Write varName. Session should be hooked before with opens
	PROCEDURE write(varName VARCHAR2, varValue VARCHAR2);

	-- Read varName. Session should be hooked before with opens
	FUNCTION read(varName VARCHAR2) RETURN VARCHAR2;

	-- Read modification time of varName. Session should be hooked before with opens
	FUNCTION readMDate(varName VARCHAR2) RETURN DATE;


	/********************/
	/* AUXILARY METHODS */
	/********************/

	/* Return hash value */
	FUNCTION hash(var VARCHAR2) RETURN VARCHAR2;

	-- Set language
	PROCEDURE setLang(langId VARCHAR2);

	-- Get language
	FUNCTION getLang RETURN VARCHAR2;

	/* get Current Session USER ID (username) */
	FUNCTION getSUser return VARCHAR2;

	/* get Current Session Client ID. Return null if client is not logged. */
	FUNCTION getSCID return VARCHAR2;

	/* get gsm Session Client ID. Return null if client is not logged. */
	FUNCTION getGsmSCID RETURN CLIENT.ID%TYPE;

	/* get gsm session phone number */
	FUNCTION getGsmSPhone return VARCHAR2;

	/* get gsm session application id */
	FUNCTION getGsmApplication RETURN VARCHAR2;

	/* Returns GSM Phone for given client*/
	FUNCTION getGsmForCID(vUserId client.id%TYPE) RETURN client.gsm%TYPE;

	/* get IP address used during logong */
	FUNCTION getIPAddress return VARCHAR2;

	/* returns client identifier, regardless of application user*/
	FUNCTION getClientId return VARCHAR2;

	/* returns client identifier for given username */
	FUNCTION getCID4User(vusername client.username%TYPE, raiseNotFound BOOLEAN := TRUE) RETURN client.id%TYPE;

	/* returns client application identifier*/
	FUNCTION getApplicationId return VARCHAR2;

	FUNCTION getDeviceId RETURN VARCHAR2;

	FUNCTION getEvent RETURN VARCHAR2;

	FUNCTION getHostname RETURN VARCHAR2;

	/* Return true if username is locked and return dates when lock is issued and when will expire */
	FUNCTION isUsernameLocked(vusername VARCHAR2, sinceDate OUT date, untilDate OUT date) RETURN BOOLEAN;

	/* Return true if username is locked */
	FUNCTION isUsernameLocked(vusername VARCHAR2) RETURN BOOLEAN;

	/* Return true if gsm is locked and return dates when lock is issued and when will expire */
	FUNCTION isGsmLocked(vgsm VARCHAR2, sinceDate OUT date, untilDate OUT date) RETURN BOOLEAN;

	/* Return true if gsm is locked */
	FUNCTION isGsmLocked(vgsm VARCHAR2) RETURN BOOLEAN;

	-- This FUNCTION return extauth id. If null is returned then user does not use additional OTP.
	-- Extauth_id are configured in branch /Core/Auth/ExtAuth/<ExtAuthID>. It is only usable for fully established sessions
	FUNCTION getExtAuthId RETURN VARCHAR2;
	FUNCTION getSApp return VARCHAR2;

	-- This FUNCTION returns extauth_id if already in session context. Else, it returns NULL
	FUNCTION getAppExtAuthId RETURN VARCHAR2;

	/* Return previous login */
	FUNCTION getPreviousLoginInfo RETURN SYS_REFCURSOR;
	FUNCTION getPreviousLogins(pClientId client.id%TYPE) RETURN SYS_REFCURSOR;

	FUNCTION isSessionExpired
	RETURN BOOLEAN;

	FUNCTION clientSignatureOtpType(pClientExtAuthId app_extauth.extauth_id%TYPE := NULL) return VARCHAR2;
	FUNCTION clientSignatureOtpType(pUsername VARCHAR2, pDeviceId VARCHAR2, pApplicationId VARCHAR2, pClientExtAuthId app_extauth.extauth_id%TYPE := NULL)
	RETURN VARCHAR2;

	PROCEDURE setAccountOwner(pAccOwnerId IN VARCHAR2);

	FUNCTION getAccountOwner
	RETURN VARCHAR2;

	FUNCTION getPrimaryAccountOwner
    	RETURN VARCHAR2;

	FUNCTION isAccountant(pClientId IN client.id%TYPE DEFAULT getClientId())
	RETURN BOOLEAN;

	FUNCTION isAlmostDepleted(clientID CLIENT.ID%TYPE)
	return VARCHAR2;

	/* registerDevice
		TYPE:        FUNCTION
		PRAGMA:      autonomous_transaction
		PARAMETERS:  vusername VARCHAR2,
					vpassword VARCHAR2,
					ipAddress VARCHAR2,
					host VARCHAR2,
					certificate VARCHAR2,
					sessLogLevel INTEGER := null,
					langId VARCHAR2 := null,
					pApplicationId VARCHAR2 := null,
					pInstallationId VARCHAR2 := null,
					pDeviceId VARCHAR2,
					pDeviceDescription VARCHAR2,
					pActivationKey VARCHAR2,
					pExtAuthId VARCHAR2
					pStatusMessage VARCHAR2
					pOperatingSystem VARCHAR2
		Description: Register new device (as mOTP device or elbamobile installation), when provided credentials
					and activation key are valid

		Constraints:
			- Valid credentials required
			- Valid activation key required
		EXCEPTIONS:  /Core/Auth/err/WrongCredentials - if invalid username - password specified
						/Core/Auth/err/InvalidActivationKey - if invalid (or expired) activation key specified

		RETURN VALUE: SEC_KEY for device */
	FUNCTION registerDevice(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2,
		host VARCHAR2, certificate VARCHAR2,
		sessLogLevel INTEGER := null, langId VARCHAR2 := null, pApplicationId VARCHAR2 := null, pInstallationId VARCHAR2 := null,
		pDeviceId VARCHAR2, pDeviceDescription VARCHAR2, pActivationKey VARCHAR2, pExtAuthId VARCHAR2, pStatusMessage OUT VARCHAR2, pOperatingSystem IN VARCHAR2 := NULL,
		pUserIdentifier IN VARCHAR2 := NULL, pDeviceType IN VARCHAR2 := NULL, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL)
	RETURN VARCHAR2;

	/* unregisterDevice
		TYPE:        PROCEDURE
		PRAGMA:      autonomous_transaction
		PARAMETERS:  NONE
		Description: Unregister already registered device. Procedure expect a session!

		Constraints:
			- Valid credentials required
			- Valid activation key required
		EXCEPTIONS:  /Core/Auth/err/NotPermited - There is no session!
						/Core/Auth/err/InvalidDevice - invalid device ID provided
		RETURN VALUE: n/a */
	PROCEDURE unregisterDevice(pAppExtAuthId app_extauth.id%TYPE);

	/* generateNewActivationKey
		TYPE:        PROCEDURE
		PRAGMA:      autonomous_transaction
		PARAMETERS:  pClientId NUMBER
					pActKey OUT VARCHAR2
					pValidUntil OUT DATE
		Description: Generate new activation key
					Procedure reserved for use in administration
		Constraints:
			- Valid credentials required
			- Valid activation key required
		EXCEPTIONS:  /Core/Auth/err/NotPermited - There is no session!
						/Core/Auth/err/InvalidDevice - invalid device ID provided
		RETURN VALUE: n/a */
	PROCEDURE generateNewActivationKey(
		pClientId client.id%TYPE,
		pActKey OUT VARCHAR2,
		pValidUntil OUT DATE,
		pAutonomousTransaction BOOLEAN DEFAULT TRUE,
		pSendKeyPerSMS IN BOOLEAN DEFAULT FALSE);

	PROCEDURE generateNewActivationKey(
			pClientId client.id%TYPE,
			pActKey OUT VARCHAR2,
			pUserId OUT VARCHAR2,
			pValidUntil OUT DATE,
			pAutonomousTransaction BOOLEAN DEFAULT TRUE,
			pSendKeyPerSMS IN BOOLEAN DEFAULT FALSE,
			pDeviceType IN VARCHAR2);

	PROCEDURE generateNewActivationKey(
			pClientId client.id%TYPE,
			pActKey OUT VARCHAR2,
			pUserId OUT VARCHAR2,
			pValidUntil OUT DATE,
			pAutonomousTransaction INTEGER DEFAULT 1,
			pSendKeyPerSMS IN INTEGER DEFAULT 0,
			pDeviceType IN VARCHAR2);

	PROCEDURE generateNewActivationKey(pActKey OUT VARCHAR2, pValidUntil OUT DATE);
	FUNCTION getExistingActivationKey RETURN DATE;
	FUNCTION getListOfAuthorizedDevices RETURN sys_refcursor;

	FUNCTION getListOfAuthorizedDevices(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
		sessLogLevel INTEGER := NULL, langId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pDeviceId IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL)
	RETURN sys_refcursor;

	PROCEDURE unregisterDevice(vusername VARCHAR2, vpassword VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
		sessLogLevel INTEGER := NULL, langId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL, pAppExtAuthId app_extauth.id%TYPE, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pDeviceId IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL);

	FUNCTION passwordExpireIn(pUserId IN client.id%TYPE) RETURN PLS_INTEGER;

	/* registerAppAuthMethod
		Register new ExtAuth for ThinClient or SMSClient. Called indirectly over proxy PROCEDURE registerAppAuthMethod
	*/
	PROCEDURE registerAppAuthMethod(
			pClientId app_extauth.client_id%TYPE,
			pApplicationId app_extauth.application_id%TYPE,
			pExtAuthId app_extauth.extauth_id%TYPE,
			pDeviceId app_extauth.dev_id%TYPE);

	PROCEDURE deregisterAppAuthMethod(
			pClientId app_extauth.client_id%TYPE,
			pApplicationId app_extauth.application_id%TYPE);

	/* Required for mOnce FUNCTIONality */
	FUNCTION getPassword(pUsername client.username%TYPE)
	RETURN client.password%TYPE;

	PROCEDURE setClientDetails(pClientId NUMBER, pAttribId VARCHAR2, pData VARCHAR2);
	FUNCTION getClientDetail(pClientId NUMBER, pAttribId VARCHAR2) RETURN VARCHAR2;
	PROCEDURE MarkForAutoDataSend(pClientId NUMBER);
	PROCEDURE SendAuthenticationData(pClientId IN VARCHAR2 DEFAULT '%');
	PROCEDURE SendAuthenticationData(pEndUsersId IN NUMBER, pAuthDataCategory VARCHAR2);

	FUNCTION getExtAuthTag RETURN VARCHAR2;

	FUNCTION normalizeUsername(pInput VARCHAR2)	RETURN VARCHAR2;

	PROCEDURE sendSMSMessage (clientID CLIENT.ID%TYPE, pMessage VARCHAR2, pSendBefore DATE DEFAULT NULL);
	PROCEDURE sendSMSMessage (contactInformation VARCHAR2, pMessage VARCHAR2, pSendBefore DATE DEFAULT NULL);

	FUNCTION setExtAuthMethod (pClientID IN client.id%TYPE, pApplicationId IN app_extauth.application_id%TYPE, pDeviceId IN app_extauth.dev_id%TYPE, pExtAuthId IN app_extauth.extauth_id%TYPE)
	RETURN app_extauth.id%TYPE;

	-- #14400: Prenesene definicije iz motp_plugin kako bi se funkcije mogle koristiti i od strane drugih funkcionalnosti
	FUNCTION getSecKey(pclientId IN app_extauth.client_id%TYPE, papplication_id IN app_extauth.application_id%TYPE, pdev_id IN app_extauth.dev_id%TYPE)
	RETURN app_extauth.sec_key%TYPE;

	PROCEDURE getPwdHash(pclientId IN client.id%TYPE,
  	pUsePwdIndicator OUT VARCHAR2,
  	pPwdCaseInsensitiveHash OUT VARCHAR2,
  	pPwdCaseSensitiveHash OUT VARCHAR2);

	RETURN client.password%TYPE;

	FUNCTION isPasswordForSignatureRequired
	RETURN BOOLEAN;

	FUNCTION getEncryptionKey(pId app_extauth.id%TYPE)
	RETURN VARCHAR2;

	FUNCTION getExtAuthAttributeValue (pAttribute IN VARCHAR2)
	RETURN VARCHAR2;

	PROCEDURE setExtAuthAttribute (pApplicationId IN VARCHAR2, pAttribute IN VARCHAR2, pAttributeValue IN VARCHAR2);

	FUNCTION tokenDuplicateExists(pTokenId client.ph0%TYPE)
  	RETURN BOOLEAN;

	PROCEDURE updateDeviceId(pUsername VARCHAR2, pPassword VARCHAR2, pDeviceId VARCHAR2, pSecKey VARCHAR2, ipAddress VARCHAR2, host VARCHAR2,
      		sessLogLevel INTEGER := NULL, pApplicationId VARCHAR2, langId VARCHAR2 := NULL, pInstallationId VARCHAR2 := NULL, pNOnce IN VARCHAR2 DEFAULT NULL, pPasswordHashed IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL);


	PROCEDURE checkNumberOfActiveClients;

    PROCEDURE deleteActiveClientLogs;

	PROCEDURE checkActiveClients;

	PROCEDURE setEvent(pEvent VARCHAR2);

	PROCEDURE checkPINComplexity(pin IN varchar2);

	PROCEDURE setRqrReauthorizationFlag (pClientId IN client.id%TYPE, pDeviceId IN app_extauth.dev_id%TYPE);
	PROCEDURE unsetRqrReauthorizationFlag (pClientId IN client.id%TYPE, pDeviceId IN app_extauth.dev_id%TYPE);

	PROCEDURE reauthorizeDeviceWithPassword (pClientId IN client.id%TYPE, pPassword IN VARCHAR2, pPasswordHashed IN VARCHAR2 DEFAULT NULL);
	FUNCTION verifyClientPassword (pClientId IN client.id%TYPE, pPassword IN VARCHAR2, pPwdHashed IN BOOLEAN) RETURN BOOLEAN;
	FUNCTION verifyClientPassword (pClientId IN client.id%TYPE, pPassword IN VARCHAR2, pPwdHashed IN BOOLEAN,
		pWrite2Log IN BOOLEAN, pCheckAndLock IN BOOLEAN, pCheckAndSendNotification IN BOOLEAN,
		pLogOperationType IN VARCHAR2, pLogMessage IN VARCHAR2, pHost IN VARCHAR2, pIpAddress IN VARCHAR2, pLangId IN VARCHAR2, pApplicationId IN VARCHAR2,
		pDeviceId IN VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL)
	RETURN BOOLEAN;

	/* Check PIN */
  PROCEDURE checkPIN(clientID IN CLIENT.ID%TYPE, deviceID IN APP_EXTAUTH.DEV_ID%TYPE, pin IN varchar2);

  /* Set PIN */
  PROCEDURE addPIN(clientID IN CLIENT.ID%TYPE, deviceID IN APP_EXTAUTH.DEV_ID%TYPE, pin IN varchar2,
		pChallenge IN varchar2 := NULL, pResponse IN varchar2 := NULL, pOtp IN varchar2 := NULL, pSourceData IN varchar2 := NULL,pSignature IN VARCHAR2 := NULL);

  /* Change existing PIN */
  PROCEDURE changePIN(clientID IN CLIENT.ID%TYPE, deviceID IN APP_EXTAUTH.DEV_ID%TYPE, newPIN IN varchar2, oldPIN IN varchar2,
		pChallenge IN varchar2 := NULL, pResponse IN varchar2 := NULL, pOtp IN varchar2 := NULL, pSourceData IN varchar2 := NULL,pSignature IN VARCHAR2 := NULL);

	/* Set value for app_extauth.ph4 column - number of failed validations attempts */
	PROCEDURE setVerificationAttempt (pExtAuthRowId ROWID, pAttempt# PLS_INTEGER);

	PROCEDURE setRqrReauthFlagForDevice(pClientId IN client.id%TYPE, pDeviceId IN app_extauth.dev_id%TYPE);

	PROCEDURE generateNewReactivationKey(
                pUsername IN VARCHAR2,
				pDeviceType IN VARCHAR2 DEFAULT NULL, /*ios or android*/
				pValidUntil OUT DATE);

	PROCEDURE checkNOnceDigest(pClientId NUMBER, pUsername VARCHAR2, pDigest VARCHAR2, pPassword VARCHAR2, pPasswordCs VARCHAR2, ipAddress VARCHAR2, host VARCHAR2, langId VARCHAR2, pApplicationId VARCHAR2, pNOnce VARCHAR2, pDeviceId VARCHAR2, pClientExtAuthId VARCHAR2, pPasswordHashed VARCHAR2 DEFAULT NULL, pAppVersionId IN VARCHAR2 DEFAULT NULL, pOSVersionId IN VARCHAR2 DEFAULT NULL, pIsFirstLogin IN BOOLEAN DEFAULT FALSE);

	PROCEDURE passwordReset(
        pUsername mcauth.client.username%TYPE,
        pEmail mcauth.client.email%TYPE,
        pMobilePhone mcauth.client.gsm%TYPE,
        pLangId VARCHAR2 DEFAULT 'bs',
        pUserId OUT NUMBER);

    PROCEDURE confirmPasswordReset(
        pVerificationCode VARCHAR2,
        pLangId VARCHAR2 DEFAULT 'bs',
        pUserId NUMBER DEFAULT NULL);

END AUTH;
/
show error

PROMPT
Prompt GRANT EXECUTE ON MCAUTH.AUTH TO MC_SUPER
GRANT EXECUTE ON MCAUTH.AUTH TO MC_SUPER
/

PROMPT
Prompt GRANT EXECUTE ON MCAUTH.AUTH TO MC_USERMANAGER
GRANT EXECUTE ON MCAUTH.AUTH TO MC_USERMANAGER
/

PROMPT
Prompt GRANT EXECUTE ON MCAUTH.AUTH TO MCADMIN WITH GRANT OPTION;
GRANT EXECUTE ON MCAUTH.AUTH TO MCADMIN WITH GRANT OPTION
/

PROMPT
Prompt GRANT EXECUTE ON MCAUTH.AUTH TO MCORE WITH GRANT OPTION;
GRANT EXECUTE ON MCAUTH.AUTH TO MCORE WITH GRANT OPTION
/

PROMPT
Prompt GRANT EXECUTE ON MCAUTH.AUTH TO MLANG
GRANT EXECUTE ON MCAUTH.AUTH TO MLANG
/

PROMPT
Prompt GRANT EXECUTE ON MCAUTH.AUTH TO MCUSERSETTINGS
GRANT EXECUTE ON MCAUTH.AUTH TO MCUSERSETTINGS
/

PROMPT
Prompt GRANT EXECUTE ON MCAUTH.AUTH TO MFLEX WITH GRANT OPTION;
GRANT EXECUTE ON MCAUTH.AUTH TO MFLEX WITH GRANT OPTION
/

PROMPT
Prompt GRANT EXECUTE ON MCAUTH.AUTH TO MSMSDOOR
GRANT EXECUTE ON MCAUTH.AUTH TO MSMSDOOR
/

PROMPT
Prompt GRANT EXECUTE ON MCAUTH.AUTH TO MC_SUPER
GRANT EXECUTE ON MCAUTH.AUTH TO MC_SUPER
/

PROMPT
Prompt GRANT EXECUTE ON MCAUTH.AUTH TO MC_USERMANAGER
GRANT EXECUTE ON MCAUTH.AUTH TO MC_USERMANAGER
/

PROMPT
Prompt GRANT SELECT ON mcore.applications TO MCAUTH
GRANT SELECT ON mcore.applications TO MCAUTH
/

PROMPT
Prompt GRANT EXECUTE ON MCAUTH.AUTH TO MEDOOR_API
GRANT EXECUTE ON MCAUTH.AUTH TO MEDOOR_API
/
