-- Jed<PERSON>tavna test skripta za password reset
-- Testira korak po korak da vidimo gdje se zaustavlja

SET SERVEROUTPUT ON SIZE 1000000;
SET TIMING ON;

PROMPT === STEP 1: Check if procedures exist ===
SELECT object_name, object_type, status 
FROM all_objects 
WHERE object_name = 'AUTH' 
AND owner = 'MCAUTH'
AND object_type IN ('PACKAGE', 'PACKAGE BODY');

PROMPT === STEP 2: Check for compilation errors ===
SELECT line, position, text 
FROM all_errors 
WHERE owner = 'MCAUTH' 
AND name = 'AUTH' 
AND type = 'PACKAGE BODY'
ORDER BY sequence;

PROMPT === STEP 3: Test basic functionality ===

DECLARE
    vTestUsername VARCHAR2(100) := 'simpletest';
    vTestEmail VARCHAR2(200) := '<EMAIL>';
    vTestMobile VARCHAR2(50) := '+38761111111';
    vUserId NUMBER;
    vVerificationCode VARCHAR2(6);
    vValidUntil DATE;
BEGIN
    DBMS_OUTPUT.PUT_LINE('Starting simple test...');
    
    -- Cleanup any existing test user
    DBMS_OUTPUT.PUT_LINE('Cleaning up existing test user...');
    BEGIN
        DELETE FROM mcauth.client_details WHERE client_id IN (
            SELECT id FROM mcauth.client WHERE username = vTestUsername
        );
        DELETE FROM mcauth.client WHERE username = vTestUsername;
        COMMIT;
        DBMS_OUTPUT.PUT_LINE('Cleanup completed');
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('Cleanup error (may be normal): ' || SQLERRM);
    END;
    
    -- Create simple test user
    DBMS_OUTPUT.PUT_LINE('Creating test user...');
    BEGIN
        INSERT INTO mcauth.client (
            id, username, password, password_cs, name, description, enabled, 
            valid_from, valid_to, email, gsm, password_enabled
        ) VALUES (
            mcauth.client_seq.NEXTVAL, vTestUsername, 
            'TESTHASH', 'testhash',
            'Simple Test User', 'Simple test', 1,
            SYSDATE - 1, SYSDATE + 365, vTestEmail, vTestMobile, 1
        );
        COMMIT;
        
        SELECT id INTO vUserId FROM mcauth.client WHERE username = vTestUsername;
        DBMS_OUTPUT.PUT_LINE('Test user created with ID: ' || vUserId);
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('ERROR creating test user: ' || SQLERRM);
            RETURN;
    END;
    
    -- Test passwordReset procedure
    DBMS_OUTPUT.PUT_LINE('Testing passwordReset procedure...');
    BEGIN
        mcauth.auth.passwordReset(
            pUsername => vTestUsername,
            pEmail => vTestEmail,
            pMobilePhone => vTestMobile,
            pLangId => 'bs',
            pUserId => vUserId,
            pVerificationCode => vVerificationCode,
            pValidUntil => vValidUntil
        );
        
        DBMS_OUTPUT.PUT_LINE('SUCCESS: passwordReset completed');
        DBMS_OUTPUT.PUT_LINE('User ID: ' || vUserId);
        DBMS_OUTPUT.PUT_LINE('Verification Code: ' || vVerificationCode);
        DBMS_OUTPUT.PUT_LINE('Valid Until: ' || TO_CHAR(vValidUntil, 'DD.MM.YYYY HH24:MI:SS'));
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('ERROR in passwordReset: ' || SQLERRM);
            DBMS_OUTPUT.PUT_LINE('Error stack: ' || DBMS_UTILITY.FORMAT_ERROR_STACK);
            -- Cleanup and exit
            DELETE FROM mcauth.client_details WHERE client_id = vUserId;
            DELETE FROM mcauth.client WHERE id = vUserId;
            COMMIT;
            RETURN;
    END;
    
    -- Wait a moment
    DBMS_LOCK.SLEEP(1);
    
    -- Test confirmPasswordReset procedure
    DBMS_OUTPUT.PUT_LINE('Testing confirmPasswordReset procedure...');
    BEGIN
        mcauth.auth.confirmPasswordReset(
            pUserId => vUserId,
            pVerificationCode => vVerificationCode,
            pLangId => 'bs'
        );
        
        DBMS_OUTPUT.PUT_LINE('SUCCESS: confirmPasswordReset completed');
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('ERROR in confirmPasswordReset: ' || SQLERRM);
            DBMS_OUTPUT.PUT_LINE('Error stack: ' || DBMS_UTILITY.FORMAT_ERROR_STACK);
    END;
    
    -- Cleanup
    DBMS_OUTPUT.PUT_LINE('Cleaning up test user...');
    BEGIN
        DELETE FROM mcauth.client_details WHERE client_id = vUserId;
        DELETE FROM mcauth.client WHERE id = vUserId;
        COMMIT;
        DBMS_OUTPUT.PUT_LINE('Cleanup completed');
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('Cleanup error: ' || SQLERRM);
    END;
    
    DBMS_OUTPUT.PUT_LINE('Simple test completed successfully!');
    
END;
/

PROMPT === STEP 4: Check what happened ===
PROMPT If you see this message, the test completed without hanging.
PROMPT If the script hangs before this point, we know where the problem is.

-- Quick check of recent client_details records
SELECT COUNT(*) as verification_codes_count
FROM mcauth.client_details 
WHERE attrib_id IN ('PWD_RESET_VERIF_CODE', 'PWD_RESET_VERIF_VALID_UNTIL')
AND ROWNUM <= 10;

PROMPT === TEST COMPLETED ===
