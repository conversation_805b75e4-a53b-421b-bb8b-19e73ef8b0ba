-- Test skripta za finalne passwordReset i confirmPasswordReset procedure
-- Autor: AI Assistant
-- Datum: 2025-07-14

SET SERVEROUTPUT ON SIZE 1000000;

DECLARE
    -- Test podaci
    vTestUsername VARCHAR2(100) := 'testuser_final';
    vTestEmail VARCHAR2(200) := '<EMAIL>';
    vTestMobile VARCHAR2(50) := '+38761999888';
    vTestLang VARCHAR2(2) := 'bs';
    
    -- OUT parametri za passwordReset
    vUserId NUMBER;
    vVerificationCode VARCHAR2(6);
    vValidUntil DATE;
    
    -- Test setup procedure
    PROCEDURE setup_test_user IS
    BEGIN
        DBMS_OUTPUT.PUT_LINE('=== SETTING UP TEST USER ===');
        
        -- <PERSON><PERSON><PERSON>i postojećeg test korisnika ako postoji
        DELETE FROM mcauth.client_details WHERE client_id IN (
            SELECT id FROM mcauth.client WHERE username = vTestUsername
        );
        DELETE FROM mcauth.client WHERE username = vTestUsername;
        
        -- <PERSON><PERSON><PERSON><PERSON> test korisnika
        INSERT INTO mcauth.client (
            id, username, password, password_cs, name, description, enabled,
            valid_from, valid_to, email, gsm, password_enabled, smspassword_enabled,
            usernamelocked_since, usernamelocked_until
        ) VALUES (
            mcauth.client_seq.NEXTVAL, vTestUsername,
            mcauth.common_pck.hash('OLDPASS'), mcauth.common_pck.hash('oldpass'),
            'Test User Final', 'Final test user for password reset', 1,
            SYSDATE - 30, SYSDATE + 365, vTestEmail, vTestMobile, 1, 0,
            SYSDATE - 1, SYSDATE - 1  -- Simuliraj da je bio zaključan
        );
        
        SELECT id INTO vUserId FROM mcauth.client WHERE username = vTestUsername;
        DBMS_OUTPUT.PUT_LINE('Test user created with ID: ' || vUserId);
        DBMS_OUTPUT.PUT_LINE('Username: ' || vTestUsername);
        DBMS_OUTPUT.PUT_LINE('Email: ' || vTestEmail);
        DBMS_OUTPUT.PUT_LINE('Mobile: ' || vTestMobile);
        COMMIT;
    END setup_test_user;
    
    -- Cleanup procedure
    PROCEDURE cleanup_test_user IS
    BEGIN
        DELETE FROM mcauth.client_details WHERE client_id = vUserId;
        DELETE FROM mcauth.client WHERE id = vUserId;
        COMMIT;
        DBMS_OUTPUT.PUT_LINE('Test user cleaned up');
    END cleanup_test_user;

BEGIN
    DBMS_OUTPUT.PUT_LINE('=== FINAL PASSWORD RESET TESTS ===');
    DBMS_OUTPUT.PUT_LINE('Test started: ' || TO_CHAR(SYSDATE, 'DD.MM.YYYY HH24:MI:SS'));
    DBMS_OUTPUT.PUT_LINE('');
    
    -- Setup
    setup_test_user;
    
    -- TEST 1: passwordReset sa novim OUT parametrima
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== TEST 1: passwordReset - Generate verification code ===');
    BEGIN
        mcauth.auth.passwordReset(
            pUsername => vTestUsername,
            pEmail => vTestEmail,
            pMobilePhone => vTestMobile,
            pLangId => vTestLang,
            pUserId => vUserId,
            pVerificationCode => vVerificationCode,
            pValidUntil => vValidUntil
        );
        
        DBMS_OUTPUT.PUT_LINE('✅ SUCCESS: passwordReset completed');
        DBMS_OUTPUT.PUT_LINE('   User ID: ' || vUserId);
        DBMS_OUTPUT.PUT_LINE('   Verification Code: ' || vVerificationCode);
        DBMS_OUTPUT.PUT_LINE('   Valid Until: ' || TO_CHAR(vValidUntil, 'DD.MM.YYYY HH24:MI:SS'));
        DBMS_OUTPUT.PUT_LINE('   Code expires in: ' || ROUND((vValidUntil - SYSDATE) * 24 * 60, 2) || ' minutes');
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('❌ FAILED: passwordReset - ' || SQLERRM);
    END;
    
    -- Provjeri da li je kod sačuvan u bazi
    DECLARE
        vStoredCodeHash VARCHAR2(200);
        vStoredValidUntil VARCHAR2(40);
    BEGIN
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('=== DATABASE VERIFICATION ===');
        
        SELECT data_vchar INTO vStoredCodeHash 
        FROM mcauth.client_details 
        WHERE client_id = vUserId AND attrib_id = 'PWD_RESET_VERIF_CODE';
        
        SELECT data_vchar INTO vStoredValidUntil 
        FROM mcauth.client_details 
        WHERE client_id = vUserId AND attrib_id = 'PWD_RESET_VERIF_VALID_UNTIL';
        
        DBMS_OUTPUT.PUT_LINE('✅ Verification code stored in database');
        DBMS_OUTPUT.PUT_LINE('   Stored hash: ' || SUBSTR(vStoredCodeHash, 1, 20) || '...');
        DBMS_OUTPUT.PUT_LINE('   Stored valid until: ' || vStoredValidUntil);
        
        -- Provjeri da li hash odgovara
        IF vStoredCodeHash = mcauth.common_pck.hash(vVerificationCode) THEN
            DBMS_OUTPUT.PUT_LINE('✅ Code hash verification: PASSED');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ Code hash verification: FAILED');
        END IF;
        
    EXCEPTION
        WHEN NO_DATA_FOUND THEN
            DBMS_OUTPUT.PUT_LINE('❌ Verification code not found in database');
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('❌ Database verification error: ' || SQLERRM);
    END;
    
    -- TEST 2: confirmPasswordReset sa novim parametrima
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== TEST 2: confirmPasswordReset - Reset password ===');
    BEGIN
        mcauth.auth.confirmPasswordReset(
            pUserId => vUserId,                    -- Obavezan parametar
            pVerificationCode => vVerificationCode, -- Kod iz prethodnog testa
            pLangId => vTestLang
        );
        
        DBMS_OUTPUT.PUT_LINE('✅ SUCCESS: confirmPasswordReset completed');
        DBMS_OUTPUT.PUT_LINE('   Password has been reset and SMS should be sent');
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('❌ FAILED: confirmPasswordReset - ' || SQLERRM);
    END;
    
    -- Provjeri da li je lozinka resetovana i račun otkljucan
    DECLARE
        vClient mcauth.client%ROWTYPE;
    BEGIN
        DBMS_OUTPUT.PUT_LINE('');
        DBMS_OUTPUT.PUT_LINE('=== POST-RESET VERIFICATION ===');
        
        SELECT * INTO vClient FROM mcauth.client WHERE id = vUserId;
        
        DBMS_OUTPUT.PUT_LINE('Account status after reset:');
        DBMS_OUTPUT.PUT_LINE('   Username locked since: ' || TO_CHAR(vClient.usernamelocked_since, 'DD.MM.YYYY HH24:MI:SS'));
        DBMS_OUTPUT.PUT_LINE('   Username locked until: ' || TO_CHAR(vClient.usernamelocked_until, 'DD.MM.YYYY HH24:MI:SS'));
        DBMS_OUTPUT.PUT_LINE('   Password enabled: ' || vClient.password_enabled);
        DBMS_OUTPUT.PUT_LINE('   Password change required: ' || vClient.password_change_required);
        
        -- Provjeri da li je račun otkljucan
        IF vClient.usernamelocked_since IS NULL AND vClient.usernamelocked_until >= SYSDATE THEN
            DBMS_OUTPUT.PUT_LINE('✅ Account unlocked: PASSED');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ Account unlocked: FAILED');
        END IF;
        
        -- Provjeri da li je lozinka aktivna
        IF vClient.password_enabled = 1 THEN
            DBMS_OUTPUT.PUT_LINE('✅ Password enabled: PASSED');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ Password enabled: FAILED');
        END IF;
        
        -- Provjeri da li je potrebna promjena lozinke
        IF vClient.password_change_required = 1 THEN
            DBMS_OUTPUT.PUT_LINE('✅ Password change required: PASSED');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ Password change required: FAILED');
        END IF;
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('❌ Post-reset verification error: ' || SQLERRM);
    END;
    
    -- Provjeri da li je verifikacijski kod obrisan
    DECLARE
        vCodeCount NUMBER;
    BEGIN
        SELECT COUNT(*) INTO vCodeCount 
        FROM mcauth.client_details 
        WHERE client_id = vUserId 
        AND attrib_id IN ('PWD_RESET_VERIF_CODE', 'PWD_RESET_VERIF_VALID_UNTIL');
        
        IF vCodeCount = 0 THEN
            DBMS_OUTPUT.PUT_LINE('✅ Verification code deleted: PASSED');
        ELSE
            DBMS_OUTPUT.PUT_LINE('❌ Verification code deleted: FAILED (' || vCodeCount || ' records remain)');
        END IF;
    END;
    
    -- TEST 3: MMobile wrapper test
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== TEST 3: MMobile wrapper procedures ===');
    
    -- Cleanup postojeći kod
    DELETE FROM mcauth.client_details 
    WHERE client_id = vUserId 
    AND attrib_id IN ('PWD_RESET_VERIF_CODE', 'PWD_RESET_VERIF_VALID_UNTIL');
    
    BEGIN
        mmobile.auth.passwordReset(
            pUsername => vTestUsername,
            pEmail => vTestEmail,
            pMobilePhone => vTestMobile,
            pLangId => 'en',  -- Test engleski jezik
            pUserId => vUserId,
            pVerificationCode => vVerificationCode,
            pValidUntil => vValidUntil
        );
        
        DBMS_OUTPUT.PUT_LINE('✅ SUCCESS: MMobile passwordReset completed');
        DBMS_OUTPUT.PUT_LINE('   Code: ' || vVerificationCode);
        
        -- Test confirmPasswordReset
        mmobile.auth.confirmPasswordReset(
            pUserId => vUserId,
            pVerificationCode => vVerificationCode,
            pLangId => 'en'
        );
        
        DBMS_OUTPUT.PUT_LINE('✅ SUCCESS: MMobile confirmPasswordReset completed');
        
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('❌ FAILED: MMobile wrapper test - ' || SQLERRM);
    END;
    
    -- TEST 4: Error handling tests
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== TEST 4: Error handling ===');
    
    -- Test sa neispravnim kodom
    BEGIN
        mcauth.auth.confirmPasswordReset(
            pUserId => vUserId,
            pVerificationCode => '999999',  -- Neispravni kod
            pLangId => vTestLang
        );
        DBMS_OUTPUT.PUT_LINE('❌ FAILED: Should have rejected invalid code');
    EXCEPTION
        WHEN OTHERS THEN
            DBMS_OUTPUT.PUT_LINE('✅ SUCCESS: Invalid code rejected - ' || SUBSTR(SQLERRM, 1, 100));
    END;
    
    -- Cleanup
    cleanup_test_user;
    
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('=== TEST SUMMARY ===');
    DBMS_OUTPUT.PUT_LINE('All tests completed at: ' || TO_CHAR(SYSDATE, 'DD.MM.YYYY HH24:MI:SS'));
    DBMS_OUTPUT.PUT_LINE('');
    DBMS_OUTPUT.PUT_LINE('MANUAL TESTING STEPS:');
    DBMS_OUTPUT.PUT_LINE('1. Use real user data (username, email, mobile)');
    DBMS_OUTPUT.PUT_LINE('2. Call passwordReset - check email for verification code');
    DBMS_OUTPUT.PUT_LINE('3. Call confirmPasswordReset with received code');
    DBMS_OUTPUT.PUT_LINE('4. Check SMS for new password');
    DBMS_OUTPUT.PUT_LINE('5. Try logging in with new password');
    
END;
/

-- Provjeri audit logove
PROMPT 
PROMPT === AUDIT LOG VERIFICATION ===
SELECT 
    logtype_id,
    description,
    TO_CHAR(logdate, 'DD.MM.YYYY HH24:MI:SS') as log_time,
    client_id,
    logstatus,
    status_code,
    status_message
FROM mcauth.clientLog 
WHERE logtype_id = 'PASSWORD_RESET' 
AND logdate > SYSDATE - 1/24  -- Zadnji sat
ORDER BY logdate DESC;

-- Provjeri konfiguraciju
PROMPT 
PROMPT === CONFIGURATION CHECK ===
SELECT 
    'Password Length' as config_name,
    NVL(TO_CHAR(sspkg.ReadInt('/Core/Admin/Forms/ActionGrantsVisualElements/flexPasswordLength')), 'NOT SET') as value
FROM dual
UNION ALL
SELECT 
    'Password Type' as config_name,
    NVL(sspkg.ReadVChar('/Core/Admin/Forms/ActionGrantsVisualElements/flexPasswordType'), 'NOT SET') as value
FROM dual
UNION ALL
SELECT 
    'SMS Body Template' as config_name,
    CASE WHEN sspkg.ReadVChar('/Core/Admin/Forms/SendPasswordBody') IS NOT NULL 
         THEN 'SET' ELSE 'NOT SET' END as value
FROM dual
UNION ALL
SELECT 
    'Email From Address' as config_name,
    NVL(sspkg.ReadVChar('/Core/Admin/Forms/SendPasswordResetVerificationAddress'), 'NOT SET') as value
FROM dual;

PROMPT 
PROMPT === TEST COMPLETED ===
